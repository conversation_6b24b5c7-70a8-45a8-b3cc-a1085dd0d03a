import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchUpcomingEventsService } from '@/services/totara/calendar';

// Route to get the upcoming events
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const year = request.nextUrl.searchParams.get('year') || '';
    const month = request.nextUrl.searchParams.get('month') || '';

    // Fetch the data
    const response = await TotaraFetchUpcomingEventsService(year, month);

    // Return the response
    return NextResponse.json({
      events: response?.data || [],
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error);
  }
}
