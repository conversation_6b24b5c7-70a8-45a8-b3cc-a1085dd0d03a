<svg width="54" height="56" viewBox="0 0 54 56" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_375_621)">
<rect x="54" width="56" height="54" rx="27" transform="rotate(90 54 0)" fill="url(#paint0_radial_375_621)"/>
</g>
<defs>
<filter id="filter0_b_375_621" x="-40" y="-40" width="134" height="136" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_375_621"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_375_621" result="shape"/>
</filter>
<radialGradient id="paint0_radial_375_621" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(54) rotate(43.9584) scale(77.7946 54.019)">
<stop stop-color="#DAD3D3" stop-opacity="0.56"/>
<stop offset="1" stop-color="#BFB9B9" stop-opacity="0.67"/>
</radialGradient>
</defs>
</svg>
