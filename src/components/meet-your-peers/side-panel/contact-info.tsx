import styles from './style.module.scss';
import Typography from '@/ui/typography';
import dictionary from '@/dictionaries';
import { Email, Phone, Pin } from '@/ui/icons';

const ContactInfo = () => {
  return (
    <div className={styles.profileContactInfoContainer}>
      <Typography as="h4" dictionary={dictionary.contactInfo} />
      <Typography as="p" className={styles.profileInfoIcon}>
        <Phone />
        <span className={styles.profileBlueTextInfo}>+971 4321 987 999</span>
      </Typography>
      <Typography as="p" className={styles.profileTextIconInfo}>
        <Email />
        <span className={styles.profileTextInfo}>
          <span>Work email</span>
          <span className={styles.profileBlueTextInfo}>
            <EMAIL>
          </span>
        </span>
      </Typography>
      <Typography as="p" className={styles.profileTextIconInfo}>
        <Email />
        <span className={styles.profileTextInfo}>
          <span>Office email</span>
          <span className={styles.profileBlueTextInfo}>
            <EMAIL>
          </span>
        </span>
      </Typography>
      <Typography as="p" className={styles.profileInfoIcon}>
        <Pin />
        <span>International Tower, ABu Dhabi, UAE</span>
      </Typography>
    </div>
  );
};
export default ContactInfo;
