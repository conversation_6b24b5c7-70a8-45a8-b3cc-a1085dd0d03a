import { useEffect } from 'react';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';
import Paper from '@/ui/paper';
import Grid from '@/ui/grid';
import EmptyMessage from '@/ui/empty-message';
import Course from '@/components/course';
import useSuggestedCoursesStore from '@/stores/learning/courses/suggested';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';

const SuggestedCourses = () => {
  // Getting loading state
  const isFetching = useSuggestedCoursesStore((x) => x.isFetching);

  // Getting method to fetch short courses
  const onFetch = useSuggestedCoursesStore((x) => x.fetch);

  // Getting short courses
  const list = useSuggestedCoursesStore((x) => x.list);
  const t = useTranslations();
  // React hook to fetch courses
  useEffect(() => {
    if (isFetching === null) {
      // fetching short courses
      onFetch();
    }
  }, [onFetch, isFetching]);

  return (
    <WithLoader loading={isFetching} loader={<Loading />}>
      <Paper>
        {!isFetching && list?.length > 0 ? (
          <Grid xs={1} sm={3} md={4} gap="lg">
            {list.map((item) => {
              return <Course key={item.courseid} course={item} />;
            })}
          </Grid>
        ) : (
          <EmptyMessage
            icon="search"
            title={t(dictionary.noSearchResultsFound)}
            description={t(
              dictionary.pleaseCheckThatAllWordsAreSpelledCorrectly,
            )}
          />
        )}
      </Paper>
    </WithLoader>
  );
};

export default SuggestedCourses;
