import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchCoachSlotsService } from '@/services/totara/coaches';

/**
 * Route to get the coach slots
 */
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const coachId = request.nextUrl.searchParams.get('id') || '';
    // Fetching coaches categories
    const response = await TotaraFetchCoachSlotsService(coachId);
    // Return the response
    return NextResponse.json(response?.data);
  } catch (error) {
    // Return Error response
    return ErrorResponse(error);
  }
}
