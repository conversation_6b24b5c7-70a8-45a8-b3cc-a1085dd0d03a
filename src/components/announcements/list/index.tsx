'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import useLearningAnnouncementsStore from '@/stores/learning/announcements';
import Paper from '@/ui/paper';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';
import AnnouncementCard from '@/ui/cards/announcement';
import AnnouncementDetail from '../detail';
import EmptyMessage from '@/ui/empty-message';
import styles from './style.module.scss';
import dictionary from '@/dictionaries';
import { motion, cubicBezier, AnimatePresence } from 'motion/react';
import Typography from '@/ui/typography';
import { useSearchParams } from 'next/navigation';
import { useLocale } from 'next-intl';
import BackOverly from '@/ui/back-overly';
import { Close } from '@/ui/icons';

const AnnouncementList = () => {
  const [announcementId, setAnnouncementId] = useState<string>('');
  const [isDialogContentVisible, setIsDialogContentVisible] =
    useState<boolean>(false);
  const [visible, setVisible] = useState(10);

  const dialogRef = useRef<HTMLDivElement>(null);

  const locale = useLocale();

  // Getting fetch action
  const onFetch = useLearningAnnouncementsStore((x) => x.fetch);

  // Getting fetching state
  const isFetching = useLearningAnnouncementsStore((x) => x.isFetching);

  // Geting the announcements
  const announcements = useLearningAnnouncementsStore((x) => x.list);

  const searchParams = useSearchParams();
  const paramId = searchParams.get('id');

  const fetchAnnouncements = useCallback(async () => {
    await onFetch();
    if (paramId) {
      handleDialogOpen(paramId);
    }
  }, [paramId, onFetch]);

  // Effect to fetch the announcements
  useEffect(() => {
    // Fetch the announcements
    fetchAnnouncements();
  }, [fetchAnnouncements]);

  const handleDialogOpen = (id: string) => {
    setIsDialogContentVisible(true);
    setAnnouncementId(id);
  };

  const handleDialogClose = () => {
    setIsDialogContentVisible(false);
    document.removeEventListener('click', handleClickOutside, true);
  };

  const onShowMoreAction = () => {
    const scrollTop = document.body.scrollTop;
    document.body.style.position = 'fixed';
    document.body.style.overflow = 'scroll';
    document.body.style.width = '100%';
    document.body.style.height = 'auto';
    document.body.style.marginTop = `-${scrollTop}px`;
    document.body.style.scrollBehavior = 'unset';
    setVisible((prevValue) => prevValue + 10);
    setTimeout(() => {
      document.body.style.position = '';
      document.body.style.overflow = '';
      document.body.style.width = '';
      document.body.style.height = '';
      document.body.style.marginTop = '';
      document.body.scrollTop = scrollTop;
    }, 10);

    setTimeout(() => {
      document.body.style.scrollBehavior = '';
    }, 1000);
  };

  const handleClickOutside = (event: Event) => {
    if (
      dialogRef.current &&
      !dialogRef.current.contains(event.target as HTMLDivElement)
    ) {
      setIsDialogContentVisible(false);
    }
  };

  useEffect(() => {
    document.addEventListener('click', handleClickOutside, true);
    return () => {
      document.removeEventListener('click', handleClickOutside, true);
    };
  }, []); // eslint-disable-line

  // Return JSX
  return (
    <>
      <WithLoader
        loader={<Loading className={styles.indentLoading} />}
        loading={isFetching}
      >
        <Paper className={styles.announcementsPaper}>
          <div>
            {announcements?.slice(0, visible).map((announcement, i) => {
              const delay = i % 10;
              return (
                <motion.div
                  key={announcement?.id}
                  initial={{
                    opacity: 0,
                    y: 5,
                    position: 'relative',
                  }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    delay: delay * 0.06,
                    ease: cubicBezier(0.71, 0.23, 0.31, 0.86),
                    duration: 0.4,
                  }}
                  className={styles.announcementItem}
                >
                  <AnnouncementCard
                    announcement={announcement}
                    onClick={() => handleDialogOpen(announcement?.id)}
                  />
                </motion.div>
              );
            })}
            {!announcements?.length && !isFetching && (
              <EmptyMessage
                icon="notification"
                title={dictionary.noAnnouncements}
                description={dictionary.thereAreNoNewAnnouncements}
              />
            )}
          </div>
          {announcements.length >= visible ? (
            <div className={styles.showMoreWrapper}>
              <button
                onClick={onShowMoreAction}
                className={styles.showMoreButton}
              >
                <Typography dictionary={dictionary.showMore} />
              </button>
            </div>
          ) : null}
        </Paper>
      </WithLoader>
      <>
        <AnimatePresence mode="wait">
          {isDialogContentVisible && (
            <motion.div
              initial={{
                right: locale != 'ar' ? -580 : 'auto',
                left: locale == 'ar' ? -580 : 'auto',
              }}
              animate={{
                right: locale != 'ar' ? 0 : 'auto',
                left: locale == 'ar' ? 0 : 'auto',
              }}
              exit={{
                right: locale != 'ar' ? -580 : 'auto',
                left: locale == 'ar' ? -580 : 'auto',
              }}
              className={styles.popup}
            >
              <BackOverly />
              <div className={styles.inner} ref={dialogRef}>
                <div className={styles.modalHeader}>
                  <button
                    type="button"
                    aria-label="Back To Screen"
                    className={styles.backBtn}
                    onClick={handleDialogClose}
                  >
                    <i>
                      <Close />
                    </i>
                  </button>
                </div>
                <div className={styles.modalBody}>
                  <AnnouncementDetail id={announcementId} />
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </>
    </>
  );
};

export default AnnouncementList;
