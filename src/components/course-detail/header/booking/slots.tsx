import { FC, Fragment, useMemo } from 'react';
import styles from './style.module.scss';
import Typography from '@/ui/typography';
import Item from './item';
import { useLocale } from 'next-intl';
import moment from 'moment';
import { AvailableSlots } from '@/types/domain/learning/course';
import usePhysicalCourseStore from '@/stores/learning/courses/physical';

interface ISlots {
  date: string;
  list: AvailableSlots[];
}

/**
 * Component to handle slots
 */
const Slots: FC<ISlots> = (props) => {
  // Deconstructing props
  const { date: _date, list } = props;
  // Getting selected slot
  const selected = usePhysicalCourseStore((x) => x.selected);
  const locale = useLocale();

  /**
   * Method to typecast the slot
   * date into moment object
   */
  const date = useMemo(() => {
    return moment(_date, 'YYYY MM DD', 'en').locale(
      locale === 'ar' ? 'ar' : 'en',
    );
  }, [_date, locale]);

  /**
   * Method to filter the slots
   */
  const _slots = useMemo(() => {
    const filtered = list.filter((slot) => slot.date === _date);
    const sorted = filtered.sort((a, b) => {
      const aTime = new Date(a?.startdatetime || '').getTime();
      const bTime = new Date(b?.startdatetime || '').getTime();
      return aTime - bTime;
    });
    return sorted;
  }, [list, _date]);

  // Return JSX
  return (
    <li>
      <div className={styles.left}>
        <div className={styles.center}>
          <Typography as="span" className={styles.day}>
            {date.format('ddd')}
          </Typography>
          <Typography as="span" className={styles.date}>
            {date.format('DD')}
          </Typography>
          <Typography as="span" className={styles.month}>
            {date.format('MMM')}
          </Typography>
        </div>
      </div>
      <div className={styles.data}>
        {_slots.map((x) => (
          <Fragment key={x?.id}>
            {selected?.id === x.id ? (
              <div className={styles.radioSelected}></div>
            ) : (
              <div className={styles.radionNotSelected}></div>
            )}
            <Item slot={x} title={x.slot} />
          </Fragment>
        ))}
      </div>
    </li>
  );
};

// Exporting component
export default Slots;
