import { NextRequest, NextResponse } from 'next/server';
import { ExternalAddUserWorkExperienceService } from '@/services/external/profile/work-experience';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';

// To handle a POST request to /api
export async function POST(request: NextRequest) {
  try {
    // Getting request body
    const { workExperience, userId } = await request.json();
    // Updating user profile
    const response = await ExternalAddUserWorkExperienceService(
      workExperience,
      userId,
    );
    // Return the response
    return NextResponse.json({ user: response?.data });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error as AxiosError);
  }
}
