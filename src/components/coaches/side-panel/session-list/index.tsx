import { FC, useState } from 'react';
import styles from './style.module.scss';
import useCoachBookingStore from '@/stores/learning/coaches/booking';
import moment from 'moment';
import 'moment/locale/ar';
import BookedSessionItem from '../item';
import DialogDrawer from '@/ui/dialog-drawer';
import dictionary from '@/dictionaries';
import { useLocale, useTranslations } from 'next-intl';
import Tabs from '@/ui/tabs';

const SessionList: FC = () => {
  const locale = useLocale();

  const { bookedSessions, viewAllBookedSession, setViewAllBookedSession } =
    useCoachBookingStore();

  const [tab, setTab] = useState<string>('1');

  const upcomingSessions = bookedSessions
    ?.filter((session) => {
      const today = new Date();
      const startDate = new Date(Number(session.startime) * 1000);
      if (startDate >= today) {
        return session;
      }
    })
    .sort((a, b) => {
      return Number(a.startime) - Number(b.startime);
    });

  const passedSessions = bookedSessions?.filter((session) => {
    const today = new Date();
    const startDate = new Date(Number(session.startime) * 1000);
    if (startDate < today) {
      return session;
    }
  });

  const t = useTranslations();

  const TabOptions = [
    { id: '1', label: t(dictionary.upcoming) },
    { id: '2', label: t(dictionary.past) },
  ];

  const handleChangeTab = (id: string) => {
    setTab(id);
  };

  const onClose = () => {
    setViewAllBookedSession(false);
  };

  return (
    <DialogDrawer
      title={t(dictionary.bookedSessions)}
      isOpened={viewAllBookedSession}
      onClose={onClose}
      noPadding
    >
      <div className={styles.containerSession}>
        <Tabs active={tab} tabs={TabOptions} onChange={handleChangeTab} />
        {tab === '1' && upcomingSessions && upcomingSessions.length > 0 && (
          <div className={styles.sectionSessions}>
            {upcomingSessions.map((session) => (
              <BookedSessionItem
                key={session.slotid}
                title={session.coach.fullname}
                userImg={session.coach.imgaeurl}
                designation={session.coach.jobtitle}
                day={session.startime || ''}
                time={`${moment
                  .unix(Number(session.startime))
                  .locale(locale === 'ar' ? 'ar' : 'en')
                  .format('h:mma')} - ${moment
                  .unix(Number(session.endtime))
                  .locale(locale === 'ar' ? 'ar' : 'en')
                  .format('h:mma')}`}
                location={session.location}
                description={session.description}
                meetingLink={session.meeting_link}
                photo={session.coach.imgaeurl}
              />
            ))}
          </div>
        )}
        {tab === '2' && passedSessions && passedSessions.length > 0 && (
          <div className={styles.sectionSessions}>
            {passedSessions.map((session) => (
              <BookedSessionItem
                key={session.slotid}
                title={session.coach.fullname}
                userImg={session.coach.imgaeurl}
                designation={session.coach.jobtitle}
                day={session.startime || ''}
                time={`${moment
                  .unix(Number(session.startime))
                  .locale(locale === 'ar' ? 'ar' : 'en')
                  .format('h:mma')} - ${moment
                  .unix(Number(session.endtime))
                  .locale(locale === 'ar' ? 'ar' : 'en')
                  .format('h:mma')}`}
                location={session.location}
                description={session.description}
                meetingLink={session.meeting_link}
                photo={session.coach.imgaeurl}
              />
            ))}
          </div>
        )}
      </div>
    </DialogDrawer>
  );
};
export default SessionList;
