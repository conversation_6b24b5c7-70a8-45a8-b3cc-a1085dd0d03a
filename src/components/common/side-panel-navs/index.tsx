import CONFIG from '@/config';
import { Link } from '@/i18n/routing';
import Paper from '@/ui/paper';
import Typography from '@/ui/typography';

import {
  FavouriteIcon,
  LeftArrow,
  RequestSupportIcon,
  RightArrow,
  SocialOutline,
  StreakIcon,
} from '@/ui/icons';

// Styles
import dictionary from '@/dictionaries';
import { useLocale } from 'next-intl';
import styles from './style.module.scss';

const SidePanelNavigations = () => {
  const showStreaks = false;
  const locale = useLocale();
  const Arrow = () => {
    return locale == 'en' ? <RightArrow /> : <LeftArrow />;
  };
  return (
    <Paper variant="sidePanel">
      <nav className={styles.sideNavs}>
        <ul>
          {showStreaks && (
            <li>
              <Link href={CONFIG.routes.myStreaks}>
                <i className={styles.leftIcon}>
                  <StreakIcon />
                </i>
                <Typography as="span" dictionary={dictionary.myStreaks} />
                <i className={styles.rightArrow}>{Arrow()}</i>
              </Link>
            </li>
          )}
          <li>
            <Link
              href={CONFIG.routes.myCourseList.replace('{id}', 'favorites')}
            >
              <i className={styles.leftIcon}>
                <FavouriteIcon />
              </i>
              <Typography as="span" dictionary={dictionary.favorites} />
              <i className={styles.rightArrow}>{Arrow()}</i>
            </Link>
          </li>
          <li>
            <Link href={CONFIG.routes.social}>
              <i className={styles.leftIcon}>
                <SocialOutline />
              </i>
              <Typography as="span" dictionary={dictionary.joinedCommunities} />
              <i className={styles.rightArrow}>{Arrow()}</i>
            </Link>
          </li>
          <li>
            <Link href={CONFIG.routes.supportCenter}>
              <i className={styles.leftIcon}>
                <RequestSupportIcon />
              </i>
              <Typography as="span" dictionary={dictionary.supportCenter} />
              <i className={styles.rightArrow}>{Arrow()}</i>
            </Link>
          </li>
        </ul>
      </nav>
    </Paper>
  );
};

export default SidePanelNavigations;
