import { NextResponse } from 'next/server';
import { ExternalGetCountriesService } from '@/services/external/profile/get-countries';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';

// To handle a GET request to /api
export async function GET() {
  try {
    // Fetching countries profile
    const response = await ExternalGetCountriesService();
    // Return the response
    return NextResponse.json({
      countries: response?.data,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error as AxiosError);
  }
}
