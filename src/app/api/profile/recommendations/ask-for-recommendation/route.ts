import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';
import { ExternalAskForRecommendationService } from '@/services/external/profile/recommendations/ask-for-recommendation';
import { ExternalAskForRecommendationNotifierService } from '@/services/external/profile/recommendations/ask-for-recommendation-notifier';

/**
 * Route to get the learning types
 */
export async function POST(request: NextRequest) {
  try {
    // Getting request data
    const data = await request.json();
    // Fetching the recommendations ask/give
    const response = await ExternalAskForRecommendationService(data).then(() =>
      ExternalAskForRecommendationNotifierService(data),
    );

    return NextResponse.json({ user: response.data });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error as AxiosError);
  }
}
