import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchCommunityDetailsService } from '@/services/totara/my-network';

/**
 * Route to get Community details
 */
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const communityId = request.url.split('/').pop();
    // Fetching community details
    const response = await TotaraFetchCommunityDetailsService(
      Number(communityId),
    );
    // Return the response
    return NextResponse.json({
      success: response.data.success,
      communityDetails: response.data.network_details,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error);
  }
}
