'use client';

import { useEffect, useMemo } from 'react';
import Image from 'next/image';
import CONFIG from '@/config';
import WithLoader from '@/ui/skeleton/with-loader';
import Typography from '@/ui/typography';
import useLearningBannersStore from '@/stores/learning/banners';
import Loading from './loading';

import badge from '@public/images/badge.svg';
import homebanner from '@public/images/homebanner.jpeg';
// Styles
import styles from './style.module.scss';
import TextLink from '@/ui/text-link';

const HomeBanner = () => {
  // Getting loading state
  const isFetching = useLearningBannersStore((x) => x.isFetching);

  // Getting fetch action
  const onFetch = useLearningBannersStore((x) => x.fetch);

  // Geting the banners
  const list = useLearningBannersStore((x) => x.list);

  // Effect to fetch the banners
  useEffect(() => {
    // Fetch the banners
    onFetch();
  }, [onFetch]);
  /**
   * Method to get first item
   * from the list of banners
   */
  const banner = useMemo(() => {
    // Extracting first element from the list of banners
    return list?.length > 0 ? list[0] : null;
  }, [list]);

  // Return JSX
  return (
    <WithLoader loader={<Loading />} loading={isFetching}>
      <div className={styles.aiBanner}>
        {banner?.image && (
          <div
            className={styles.aiImg}
            style={{
              backgroundImage: `url(${homebanner.src})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          />
        )}
        <div className={styles.text}>
          <h3 className={styles.heading}>
            <span className="resizeable">{banner?.title}</span>
            <i className={styles.icon}>
              <Image src={badge} alt="" className="img-fluid" />
            </i>
          </h3>
          <Typography as="p" className="lead">
            {banner?.subtitle}
          </Typography>
          <div className={styles.btnWrap}>
            <TextLink link={CONFIG.routes.badges} variant="white">
              {banner?.buttontext?.toUpperCase()}
            </TextLink>
          </div>
        </div>
      </div>
    </WithLoader>
  );
};

export default HomeBanner;
