@use 'mixins' as *;

.courseDetailWrapper {
  display: flex;
  flex-direction: column;
  flex: 1;

  .dialogDrawerClose {
    svg {
      transform: rotate(180deg);
      width: size(24);
      height: size(24);
      margin-bottom: size(26);
    }
  }
  .banner {
    height: size(337);
    width: 100%;

    img {
      height: 100%;
      width: 100%;
      object-fit: cover;
      border-radius: size(12);
    }
  }

  .header {
    margin-top: size(35);
    padding-bottom: size(32);
    display: flex;
    flex-direction: column;
    gap: size(16);
    border-bottom: size(1) solid var(--grey-400);

    .basicInfo {
      display: flex;
      flex-direction: row;
      align-items: center;
      flex-wrap: wrap;
      gap: size(16);

      span {
        font-size: size(14);
        line-height: size(10);
        color: var(--grey-900);
        display: flex;
        flex-direction: row;
        align-items: center;
      }

      .time {
        margin: 0;
        position: relative;

        svg {
          margin-right: size(4);
        }
      }

      .date,
      .location,
      .time,
      .rating {
        svg {
          width: size(16);
          height: size(16);
          path {
            color: var(--grey-900);
            stroke: var(--grey-900);
          }

          margin-right: size(4);
        }
      }

      .rating img {
        margin-right: size(4);
      }

      .location {
        flex: 0 0 100%;
      }
    }
    .curator {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: size(8);
      i {
        display: block;
        width: size(32);
        height: size(32);
        overflow: hidden;
        border-radius: 50%;
      }
      span {
        font-size: size(16);
      }
    }
  }

  .courseInfo {
    flex: 1;
  }

  .action {
    padding: 0 size(8);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    bottom: size(13);
    width: 100%;
    left: 0;
    button {
      width: 100%;
      padding: size(21.5);

      border: none;
      font-size: size(18);
      line-height: size(13);

      border-radius: size(12);
      background-color: var(--grey-900);
      color: var(--white);
      //&.open-to-enroll {
      //  background-color: var(--grey-900);
      //  color: var(--white);
      //}
      //
      //&.enrolled {
      //  background-color: var(--disabled-border);
      //  color: var(--grey-700);
      //}
    }
  }
}
