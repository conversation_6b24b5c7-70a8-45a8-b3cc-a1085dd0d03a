import Typography from '@/ui/typography';
import { FC } from 'react';
import RecommendATalentWhoAndWhy from '../RecommendATalentWhoAndWhy';
import RecommendATalentInformation from '../RecommendATalentInformation';
import RecommendATalentGoodFit from '../RecommendATalentGoodFit';
import StepProgress from '@/ui/progress/stepper';
import styles from './style.module.scss';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';

interface IRecommendATalentStepper {
  currentStep: number;
  onClose: () => void;
  nextStep: () => void;
  prevStep: () => void;
}

const RecommendATalentStepper: FC<IRecommendATalentStepper> = (props) => {
  const { currentStep, onClose, prevStep, nextStep } = props;

  const t = useTranslations();

  const renderStep = (step: number) => {
    switch (step) {
      case 1:
        return <RecommendATalentWhoAndWhy onNext={nextStep} />;
      case 2:
        return <RecommendATalentGoodFit onNext={nextStep} onBack={prevStep} />;
      case 3:
        return (
          <RecommendATalentInformation
            onBack={prevStep}
            onCloseDrawer={onClose}
          />
        );
      default:
        break;
    }
  };

  return (
    <div className="dialog-content">
      <Typography
        as="h2"
        className="dialog-heading"
        dictionary={dictionary.recommendATalent}
      />
      <StepProgress step={currentStep} />
      <div className={styles.stepProgressText}>
        <Typography as="p" className={styles.stepText}>
          {t(dictionary.step)} {currentStep} {t(dictionary.of)} 3
        </Typography>
      </div>
      {renderStep(currentStep)}
    </div>
  );
};
export default RecommendATalentStepper;
