@use 'mixins' as *;

.bodyWrapper {
  align-items: center;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: scroll;
  scroll-behavior: smooth;

  @media (min-width: 576px) {
    &::-webkit-scrollbar-track {
      background-color: transparent;
    }

    &::-webkit-scrollbar {
      width: size(8);
      background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: size(8);
      background-color: var(--grey-900);
    }
  }
}

.bodyCollapsed {
  @include forMarginPadding(padding, size(0), size(16), size(0), size(24));
}

.topDate {
  align-self: center;
  border: 1px solid var(--grey-400);
  border-radius: size(30);
  font-size: size(16);
  padding: size(10) size(20);
}

.introArea {
  margin: auto;
}

.greetingArea {
  align-items: center;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding-top: size(20);
}
.aiLoopWrapper {
  position: relative;
}

.aiLoop {
  animation-name: glowing-light;
  animation-duration: 4s;
  animation-iteration-count: infinite;
  animation-direction: normal;
  border-radius: 50%;
  box-shadow: 0 0 18px rgb(189, 222, 255, 0.6);
  height: size(150);
  margin-bottom: size(40);
  position: relative;
  transition: 0.5s all cubic-bezier(0.77, 0.23, 0.23, 0.81);
  width: size(150);
  z-index: 1;
}

@include for-dark-theme {
  .aiLoop {
    animation-name: glowing;
  }
}

.expandedImage {
  height: size(200);
  transition: 0.5s all cubic-bezier(0.77, 0.23, 0.23, 0.81);
  width: size(200);
}

.expandedAiLoopBack {
  height: size(170);
  transition: 0.5s all cubic-bezier(0.77, 0.23, 0.23, 0.81);
  width: size(170);
}

.profileMessage {
  text-align: center;
  max-width: size(380);
  width: 100%;

  &.smallFont {
    max-width: size(320);
  }
}

.greeting {
  color: var(--grey-800);
  font-size: size(30);
  font-weight: 700;

  &.smallFont {
    font-size: size(26);
  }
}

.message {
  background-image: linear-gradient(120deg, #0f7ee9, #743cbe);
  background-clip: text;
  color: transparent;
  font-size: size(40);
  font-weight: 700;
  margin: 0;

  &.smallFont {
    font-size: size(32);
    font-weight: 400;
  }
}

.askingArea {
  gap: size(16);
  display: flex;
  flex-direction: column;
  height: 0;
  margin-bottom: size(40);
  max-width: size(900);
  opacity: 0;
  transition: 0.4s all cubic-bezier(0.77, 0.23, 0.23, 0.81);
  width: 100%;

  p {
    margin-bottom: size(16);
  }

  &.activeArea {
    height: 100%;
    margin-bottom: size(16);
    opacity: 1;
    transition: 0.4s all cubic-bezier(0.77, 0.23, 0.23, 0.81);
  }
}

.generatingLoader {
  display: flex;
  gap: size(16);
  flex-direction: column;
}

.userWrapper {
  align-self: flex-end;
  border-radius: size(40);
  display: flex;
  flex-direction: column;
  gap: size(14);
  max-width: size(825);
}

.userAvatar {
  align-self: flex-end;
  border-radius: size(40);
  height: size(35);
  width: size(35);
}

.userQuestion {
  @include forMarginPadding(padding, size(15), size(20), size(14), size(15));
  background-color: var(--blue-600);
  border-radius: size(20) size(0) size(20) size(20);
  color: var(--grey-900);
  font-size: size(16);
}

@include for-dark-theme {
  .userQuestion {
    background-color: #3f4c5b;
  }
}

.answerWrapper {
  align-self: flex-start;
  max-width: size(825);
  display: flex;
  flex-direction: column;
  gap: size(14);
}

.placeholderAvatar {
  opacity: 0;
  animation: opacity-answer;
  animation-fill-mode: forwards;
  animation-duration: 0.4s;
  animation-delay: 0.2s;
  animation-timing-function: cubic-bezier(0.77, 0.23, 0.23, 0.81);
}

.placeholderAnswer {
  align-items: center;
  display: flex;
  gap: size(8);
  opacity: 0;
  animation: opacity-answer;
  animation-fill-mode: forwards;
  animation-duration: 0.4s;
  animation-delay: 0.4s;
  animation-timing-function: cubic-bezier(0.77, 0.23, 0.23, 0.81);
}

.assistantAvatar {
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(229, 242, 255, 0.5);
  height: size(35);
  width: size(35);
}

@include for-dark-theme {
  .assistantAvatar {
    box-shadow: 0 0 8px rgb(189, 222, 255, 0.5);
  }
}

.assistantAnswer {
  background-color: var(--bg-shade-5);
  border-radius: size(0) size(20) size(20) size(20);
  color: var(--grey-800);
  padding: size(20);

  p {
    &:last-child {
      margin-bottom: size(0);
    }
  }

  ol {
    @include forMarginPadding(padding, size(0), size(0), size(0), size(30));
    font-size: size(16);
    margin-bottom: size(16);

    p {
      margin-bottom: size(10);
    }

    ::marker {
      font-size: size(16);
      font-weight: 700;
    }

    li {
      margin-bottom: size(10);
    }
  }

  ul {
    @include forMarginPadding(padding, size(0), size(0), size(0), size(30));
    font-size: size(16);
    margin-bottom: size(10);
    list-style: disc;
  }

  a {
    color: var(--text-link-blue);
    text-decoration: underline;

    &:hover {
      text-decoration: none;
    }
  }

  pre {
    font-size: size(16);
    margin-bottom: size(16);
  }
}

.recommendedLabel {
  font-size: size(18);
  margin-bottom: size(16);
}

.suggestionSlider {
  align-self: flex-start;
  background-color: var(--bg-shade-5);
  border-radius: size(0) size(20) size(20) size(20);
  color: var(--grey-800);
  max-width: size(825);
  padding: size(20);
  width: 100%;
}

.courseSlide {
  height: auto;

  img {
    transform: scale(1);
    transition: 0.4s transform cubic-bezier(0.77, 0.23, 0.23, 0.81);
  }

  &:hover {
    img {
      transform: scale(1.07);
      transition: 0.4s transform cubic-bezier(0.77, 0.23, 0.23, 0.81);
    }
  }
}

@keyframes opacity-answer {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes glowing {
  0% {
    box-shadow: 0 0 16px rgb(189, 222, 255, 0.6);
  }
  50% {
    box-shadow: 0 0 20px rgb(189, 222, 255, 0.8);
  }
  100% {
    box-shadow: 0 0 16px rgb(189, 222, 255, 0.6);
  }
}

@keyframes glowing-light {
  0% {
    box-shadow: 0 0 16px rgba(235, 245, 255, 0.6);
  }
  50% {
    box-shadow: 0 0 20px rgba(235, 245, 255, 0.8);
  }
  100% {
    box-shadow: 0 0 16px rgba(235, 245, 255, 0.6);
  }
}
