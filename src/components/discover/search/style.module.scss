@use 'mixins' as *;

.discoverPaper {
  overflow: initial;
  position: relative;

  .title {
    font-size: size(22);
    font-weight: 700;
    @include forMarginPadding(margin, size(0), auto, size(25), size(0));
  }
}

.discover {
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
  @include forMarginPadding(margin, size(0), size(0), size(30), size(0));
}

.popup {
  position: absolute;
  @include leftToRight(0);
  top: 0;
  width: 100%;
  z-index: 99;

  .discover {
    @include borderRadius(var(--radius));
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    @include forMarginPadding(padding, size(25), size(25), size(25), size(25));
    min-height: initial;
    position: relative;
    z-index: 999;
    background-color: var(--white);
  }

  .head {
    @include forMarginPadding(margin, size(0), size(0), size(25), size(0));
  }
}

.head {
  display: flex;
  align-items: center;
  position: relative;
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));

  h2 {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }

  button {
    position: initial;
    @include forMarginPadding(margin, size(0), size(20), size(0), size(0));
  }
}

@include for-all-phone() {
  .discover {
    @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    min-height: initial;
    position: relative;

    h2 {
      font-size: size(28);
    }
  }

  .popup {
    position: fixed;
    background: var(--white);
    height: 100vh;
    overflow-y: auto;

    .discover {
      width: 100%;
      @include leftToRight(initial);
      border-radius: 0px;
      @include forMarginPadding(
        padding,
        size(20),
        size(24),
        size(20),
        size(24)
      );
    }
  }
}
