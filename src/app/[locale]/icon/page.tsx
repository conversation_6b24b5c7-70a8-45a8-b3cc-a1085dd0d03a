import {
  ArrowFullR<PERSON>,
  Badge,
  Bell,
  Calendar,
  Circle,
  Clock,
  Close,
  Corporate,
  Coursera,
  DiscoverIcon,
  Dots,
  Download,
  Executive,
  Experience,
  External,
  FavouriteIcon,
  GlobeIcon,
  Help,
  Home,
  LeftArrow,
  Lesson,
  Library,
  Pin,
  Plus2,
  PlusIcon,
  Presentation,
  Profile,
  QuoteIcon,
  Reset,
  RightArrow,
  ScholarIcon,
  SearchIcon,
  Setting,
  Share,
  ShareProfile,
  SocialIcon,
  Tick,
  Time,
  WarningIcon,
} from '@/ui/icons';

import styles from './style.module.scss';

export default function IconPage() {
  return (
    <div className={styles.iconList}>
      <i className={styles.icon + ' fa-icon'}>
        <Home />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Bell />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <RightArrow />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <LeftArrow />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Setting />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Help />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Time />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Lesson />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Experience />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Corporate />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <SearchIcon />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Profile />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Download />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <PlusIcon />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Calendar />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Library />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Executive />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Badge />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Coursera />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Presentation />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Close />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Circle />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Reset />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Pin />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Dots />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Share />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <ShareProfile />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Plus2 />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <FavouriteIcon />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Clock />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <Tick />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <External />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <GlobeIcon />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <WarningIcon />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <QuoteIcon />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <ArrowFullRight />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <ScholarIcon />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <DiscoverIcon />
      </i>
      <i className={styles.icon + ' fa-icon'}>
        <SocialIcon />
      </i>
    </div>
  );
}
