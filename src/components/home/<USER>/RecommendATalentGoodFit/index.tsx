import Loading from '@/components/common/loading';
import PersonalDetailToggle from '@/components/profile/personal-details/personal-detail-toggle';
import useRecommendTalentStore from '@/stores/recommend-talent';
import WithLoader from '@/ui/skeleton/with-loader';
import Typography from '@/ui/typography';
import { FC, useEffect, useState } from 'react';
import styles from './style.module.scss';
import Button from '@/ui/form/button';
import { useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';

interface IRecommendATalentGoodFit {
  onNext: () => void;
  onBack: () => void;
}

const RecommendATalentGoodFit: FC<IRecommendATalentGoodFit> = (props) => {
  const { onNext, onBack } = props;
  const {
    fetchInterest,
    isLoading,
    interests,
    recommendTalentData,
    setRecommendTalentData,
  } = useRecommendTalentStore();

  const INTEREST_SEPARATOR = '||';

  const initialInterests: string[] = recommendTalentData.goodFit
    ? recommendTalentData.goodFit
        .split(INTEREST_SEPARATOR)
        .map((item) => item.trim())
    : [];
  const [selectedInterests, setSelectedInterests] =
    useState<(string | undefined)[]>(initialInterests);

  const t = useTranslations();

  useEffect(() => {
    if (!interests) {
      fetchInterest('RecommendInterest');
    }
  }, [fetchInterest, interests]);

  const onInterestHandle = (interestsName: string | undefined) => {
    const updatedInterests = selectedInterests?.includes(interestsName)
      ? selectedInterests.filter((name) => name !== interestsName)
      : [...selectedInterests, interestsName];

    setSelectedInterests(updatedInterests);
  };

  const handleClickBack = () => {
    setRecommendTalentData({
      ...recommendTalentData,
      goodFit: selectedInterests
        .filter(Boolean)
        .map((s) => s!.trim())
        .join(INTEREST_SEPARATOR),
    });
    onBack();
  };

  const handleClickNext = () => {
    setRecommendTalentData({
      ...recommendTalentData,
      goodFit: selectedInterests
        .filter(Boolean)
        .map((s) => s!.trim())
        .join(INTEREST_SEPARATOR),
    });
    onNext();
  };

  const isEmpty = selectedInterests.length < 1;

  return (
    <>
      <WithLoader loader={<Loading />} loading={isLoading}>
        <Typography
          as="h4"
          className="dialog-heading"
          dictionary={dictionary.inWhichRoleDoYouSeeThisPersonAGoodFit}
        />
        {t(dictionary.selectAsManyResponses)}
        <div className={styles.interestsWrapper}>
          {interests?.map((result) => {
            const isSelected = selectedInterests?.includes(
              result.name as string,
            );

            return (
              <PersonalDetailToggle
                key={result.key}
                name={result.name}
                handleTag={onInterestHandle}
                isSelected={isSelected}
              />
            );
          })}
        </div>
      </WithLoader>

      <div className={styles.buttons}>
        <Button
          type="button"
          color="black"
          outline
          onClick={handleClickBack}
          dictionary={dictionary.previous}
        />
        <Button
          type="button"
          color="black"
          onClick={handleClickNext}
          disabled={isEmpty}
          dictionary={dictionary.next}
        />
      </div>
    </>
  );
};
export default RecommendATalentGoodFit;
