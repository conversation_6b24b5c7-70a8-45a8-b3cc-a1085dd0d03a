import { useCallback, useEffect } from 'react'; // Removed useMemo, useState

import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '../../../common/loading';

import useCourseDetailStore from '@/stores/learning/courses/detail';
import useCoreCourseStore from '@/stores/learning/courses/core-course';
import { useTranslations } from 'next-intl';
import govAcademy from '@public/images/gov-academy.png';

import styles from './style.module.scss';

// import useToggleable from '@/hooks/helpers/useToggle'; // Removed unused import
import dictionary from '@/dictionaries';

import { CalendarTimeSlot, ClockFrame, Pin } from '@/ui/icons';
import star from '@public/images/star.svg';
import IFormatedEvent from '@/types/domain/calendar/formated-event';
import moment from 'moment';
import Image from '@/ui/image';
import CONFIG from '@/config';
import { Link } from '@/i18n/routing';
// import Agenda from './agenda';
// import LocationTab from './location';

interface CourseDetailsProps {
  courseId: string;
  eventDetails: IFormatedEvent;
  handleCloseDrawer: () => void;
}

const CourseDetails = ({ courseId, eventDetails }: CourseDetailsProps) => {
  const t = useTranslations();

  // Getting fetch action
  const onFetch = useCourseDetailStore((state) => state.fetch);

  // Getting loading state
  const isFetching = useCourseDetailStore((state) => state.isFetching);

  // Getting course detail
  const courseDetail = useCourseDetailStore((state) => state.detail);
  const onCoreCourseFetch = useCoreCourseStore((state) => state.fetch);
  const isFetchingCore = useCoreCourseStore((state) => state.isFetching);

  useEffect(() => {
    if (courseId) {
      onFetch(courseId as string);
    }
  }, [courseId, onFetch]);

  useEffect(() => {
    if (courseId) {
      onCoreCourseFetch(courseId as string);
    }
  }, [courseId, onCoreCourseFetch]);

  const getCurator = useCallback(() => {
    const curatorLogo =
      courseDetail?.course_curatorlogo &&
      courseDetail?.course_curatorlogo !== ''
        ? courseDetail?.course_curatorlogo
        : govAcademy;
    const curatorName =
      courseDetail?.course_curatorname &&
      courseDetail?.course_curatorname !== ''
        ? courseDetail?.course_curatorname
        : t(dictionary.govAcademy);
    return { curatorLogo, curatorName };
  }, [courseDetail, t]);

  return (
    <>
      <div className={styles.courseDetailWrapper}>
        <WithLoader
          loading={isFetching && isFetchingCore}
          loader={<Loading className={styles.courseDetailWrapper} />}
        >
          <div className={styles.banner}>
            <img
              src={courseDetail?.course_image}
              alt={`${eventDetails.coursename} banner`} // Meaningful alt text
            />
          </div>
          <div className={styles.header}>
            <h4 className={styles.courseTitle + ' mb-0'}>
              {eventDetails.coursename}
            </h4>

            <div className={styles.basicInfo}>
              <span className={styles.date}>
                <CalendarTimeSlot />
                {moment(eventDetails.startDateTime).format('D MMMM,YYYY')}
              </span>
              <span className={styles.time}>
                <ClockFrame />
                {eventDetails.startTime} - {eventDetails.endTime}
              </span>
              {/* review_overall_rating */}
              {courseDetail?.review_overall_rating !== '0' && (
                <span className={styles.rating}>
                  <Image src={star} alt="Rating star" className="img-fluid" />{' '}
                  {/* Meaningful alt text */}
                  {courseDetail?.review_overall_rating}
                </span>
              )}

              {eventDetails.eventlocation?.address && (
                <span className={styles.location}>
                  <Pin />
                  {eventDetails.eventlocation.address}
                </span>
              )}
            </div>

            <div className={styles.curator}>
              <i>
                <Image
                  src={getCurator().curatorLogo}
                  alt={`${getCurator().curatorName} logo`} // Meaningful alt text
                  className="img-fluid"
                />
              </i>
              <span className={styles.curatorName}>
                {getCurator().curatorName}
              </span>
            </div>
          </div>
          <div className={styles.courseInfo}></div>
          <div className={styles.action}>
            <button
              className={` ${eventDetails.status && eventDetails.status.toLowerCase() === 'booked' ? styles['enrolled'] : styles['open-to-enroll']}`}
            >
              <Link
                href={CONFIG.routes.course.replace('{id}', courseId as string)}
                target="_blank"
              >
                {eventDetails.status &&
                eventDetails.status.toLowerCase() === 'booked'
                  ? t(dictionary.viewCourseDetails)
                  : t(dictionary.viewCourseDetails)}
              </Link>
            </button>
          </div>
        </WithLoader>
      </div>
    </>
  );
};

export default CourseDetails;
