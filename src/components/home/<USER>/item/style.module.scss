@use 'mixins' as *;

.profile {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  height: 100%;
  cursor: pointer;

  &:hover {
    div[class*='title'] {
      span {
        text-decoration: underline;
      }
    }

    div[class*='pic'] {
      img {
        transform: scale(1.08);
        transition: 0.4s all ease-in-out;
      }
    }
  }
}

.pic {
  @include borderRadius(50%);
  overflow: hidden;
  width: size(85);
  height: size(85);
  @include forMarginPadding(margin, size(0), size(0), size(16), size(0));

  img {
    transform: scale(1);
    transition: 0.2s all ease-in-out;
  }
}

.data {
  display: flex;
  flex-direction: column;
  height: calc(100% - size(101));
  width: 100%;

  @include for-all-phone() {
    height: calc(100% - size(88));
  }
}

.title {
  font-size: size(22);
  font-weight: 400;
  line-height: 1.1;
  color: var(--grey-900);
  text-decoration: none;
  @include forMarginPadding(margin, size(0), size(0), size(5), size(0));
  display: inline-block;
}

.designation {
  color: var(--grey-800);
  font-weight: 400;
  @include forMarginPadding(margin, size(0), size(0), size(10), size(0));
}

.rating {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: auto;
  line-height: 1;
}

.ratingText {
  font-size: size(16);
}

.starRating {
  width: size(14);
  height: size(14);
  display: flex;
  @include forMarginPadding(margin, size(0), size(5), size(0), size(0));
  position: relative;
  line-height: 1;
}

@include for-all-phone() {
  .pic {
    width: size(72);
    height: size(72);
  }

  .title {
    font-size: size(16);
  }

  .designation {
    font-size: size(12);
  }

  .ratingText {
    font-size: size(12);
  }
}
