@use 'mixins' as *;

// .calendarWrapper {
//   background-color: var(--white);
//   border-top: 2px solid var(--disabled);
//   border-radius: var(--radius);
//   display: flex;
//   position: relative;

//   @include for-all-phone() {
//     flex-direction: column;
//   }
// }

// div.calendarSidebar {
//   @include forMarginPadding(padding, size(40), size(16), size(16), size(16));
//   border-right: 1px solid var(--disabled);
//   width: size(300);

//   @include rtl {
//     border-right: 0;
//     border-left: 1px solid var(--disabled);
//   }

//   @include for-all-phone() {
//     width: 100%;
//   }

//   div[class*='wrapper'] {
//     min-height: size(200);
//   }

//   svg[class*='spinner'] {
//     display: block;
//     @include forMarginPadding(margin, size(100), auto, size(16), auto);
//   }

//   div[class*='image'] {
//     height: size(70);
//     margin-bottom: size(10);

//     img {
//       height: size(70);
//       margin: auto;
//       width: auto;
//     }
//   }

//   h4 {
//     font-size: size(18);
//   }
// }

// .calendarMainWrapper {
//   @include forMarginPadding(padding, size(40), size(16), size(16), size(16));
//   width: calc(100% - size(300));

//   @include for-all-phone() {
//     @include forMarginPadding(padding, size(20), size(16), size(16), size(16));
//     width: 100%;
//   }
// }

// .dateHeader {
//   margin-bottom: size(10);
// }

// .monthHeader {
//   color: var(--grey-900);
//   font-size: size(30);
//   font-weight: 700;
//   @include forMarginPadding(margin, size(0), size(4), size(0), size(0));
// }

// .yearHeader {
//   color: var(--text-link-blue);
//   font-size: size(30);
// }

// .viewSwitcher {
//   align-items: center;
//   background-color: var(--grey-300);
//   border-radius: size(22);
//   display: flex;
//   gap: size(10);
//   height: size(44);
//   padding: size(4);
//   position: absolute;
//   right: size(16);

//   @include rtl {
//     left: size(16);
//     right: auto;
//   }
// }

// .viewCta {
//   background-color: var(--white);
//   border-radius: 50%;
//   border: 0;
//   cursor: pointer;
//   height: size(36);
//   padding: size(10);
//   width: size(36);
//   transition: 0.2s all ease-in-out;

//   svg {
//     fill: var(--grey-900);
//     height: size(20);
//     width: size(20);
//     transition: 0.2s all ease-in-out;
//   }
// }

// .isActive {
//   background-color: var(--grey-900);
//   transition: 0.2s all ease-in-out;

//   svg {
//     fill: var(--white);
//     transition: 0.2s all ease-in-out;
//   }
// }

// .arrowDirection {
//   svg {
//     fill: var(--grey-900);
//     height: size(10);
//     width: size(10);
//     transform: rotate(90deg);

//     @include rtl {
//       transform: rotate(270deg);
//     }
//   }
// }

// .rotateArrow {
//   svg {
//     transform: rotate(270deg);

//     @include rtl {
//       transform: rotate(90deg);
//     }
//   }
// }

// .isWeeklyView {
//   position: relative;

//   div[class*='smallCalendarWrapper'] {
//     background-color: transparent;
//   }

//   div[class*='box'] {
//     z-index: 1;
//   }

//   div[class*='selected'] {
//     z-index: 0;

//     span[class*='day-number'],
//     div[class*='day-data'] {
//       z-index: 1;
//     }

//     &:before {
//       background-color: var(--blue-400);
//       content: '';
//       border-radius: size(4);
//       height: size(25);
//       left: 0;
//       margin-top: size(-4);
//       width: size(267);
//       position: absolute;

//       @include for-all-phone() {
//         width: 100%;
//       }
//     }
//   }
// }

@include for-dark-theme {
  .isWeeklyView {
    div[class*='smallCalendarWrapper'] {
      background-color: transparent;
    }

    div[class*='selected'] {
      &:before {
        background-color: #3f4c5b;
      }
    }
  }
}

.calendarWrapper {
  display: flex;
  flex-direction: row;
  gap: size(24);
  margin-top: size(24);

  .calendarSidebar {
    width: calc(24% - size(24));
  }

  .calendarMainWrapper {
    width: 76%;
  }

  @include for-all-phone() {
    flex-direction: column;
    overflow: scroll;
    .calendarSidebar,
    .calendarMainWrapper {
      width: 100%;
      padding: size(16);
    }

    .calendarMainWrapper {
      margin-top: size(16);
    }
  }
}

.calendarMobile {
  display: flex;
}
