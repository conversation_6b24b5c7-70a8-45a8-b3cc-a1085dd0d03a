import { NextRequest, NextResponse } from 'next/server';
import {
  ExternalFetchUserService,
  ExternalPatchUserService,
} from '@/services/external/user';
import { getUserId, updateOnBoardedState } from '@/utils/auth';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraUpdateProfileService } from '@/services/totara/profile';

/**
 * Method to get the logged in
 * user profile
 */
export async function POST(request: NextRequest) {
  try {
    // Getting user id
    const id = await getUserId();
    // Getting request body
    const data: {
      level: string;
      department: string;
    } = await request.json();
    // Updating user at liferay
    await ExternalPatchUserService({
      id: id || '',
      isOnboarded: true,
    });
    // Getting user profile
    const response = await ExternalFetchUserService(id || '');
    const user = response?.data?.items?.[0];
    // Updating totara profile
    const totaraResponse = await TotaraUpdateProfileService({
      level: data?.level || '',
      discipline: data?.department || '',
    });
    // Updating onboard state on cookie
    await updateOnBoardedState(true);
    // Return the response
    return NextResponse.json({
      user: user,
      totara: totaraResponse?.data,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error);
  }
}
