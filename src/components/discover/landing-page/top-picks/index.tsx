import { useEffect, useMemo } from 'react';
import CardsSection from '@/components/common/cards-section';
import CONFIG from '@/config';
import useTopCoursesStore from '@/stores/learning/courses/top';
import Course from '@/components/course';
import EmptyMessage from '@/ui/empty-message';
import { useTranslations } from 'next-intl';
import dictionaries from '@/dictionaries';
import dictionary from '@/dictionaries';

const TopPicks = () => {
  // Getting loading state
  const isFetching = useTopCoursesStore((x) => x.isFetching);

  // Getting fetch action
  const onFetch = useTopCoursesStore((x) => x.fetch);

  // Getting top picks
  const list = useTopCoursesStore((x) => x.list);
  const t = useTranslations();
  useEffect(() => {
    onFetch();
  }, [onFetch]);

  // Creating dyanmic array of cards
  const items = useMemo(() => {
    if (list && list.length > 0) {
      return list.map((item) => (
        <Course key={item.courseid} course={item} variant="beta" />
      ));
    }
    return [];
  }, [list]);

  // Return JSX
  return (
    <CardsSection
      title={t(dictionaries.TopPicsForYou)}
      listingPageUrl={CONFIG.routes.discover.top}
      items={items}
      limit={5}
      loading={isFetching}
      className="learningSlider"
      slidesConfig={{
        desktop: {
          view: 3.4,
          gap: 18,
        },
        mobile: {
          view: 1,
          gap: 10,
        },
      }}
      noDataMessage={
        <EmptyMessage
          icon="search"
          title={t(dictionary.noSearchResultsFound)}
          description={t(dictionary.pleaseCheckThatAllWordsAreSpelledCorrectly)}
        />
      }
    />
  );
};

export default TopPicks;
