<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_528_564)">
<circle cx="15" cy="15" r="14" fill="url(#paint0_radial_528_564)" fill-opacity="0.5"/>
<circle cx="15" cy="15" r="14.5" stroke="url(#paint1_radial_528_564)" stroke-opacity="0.8"/>
</g>
<g filter="url(#filter1_b_528_564)">
<circle cx="15" cy="15" r="5" fill="#7CA1C3"/>
<circle cx="15" cy="15" r="5.5" stroke="url(#paint2_radial_528_564)" stroke-opacity="0.8"/>
</g>
<defs>
<filter id="filter0_b_528_564" x="-40" y="-40" width="110" height="110" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_528_564"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_528_564" result="shape"/>
</filter>
<filter id="filter1_b_528_564" x="-31" y="-31" width="92" height="92" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_528_564"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_528_564" result="shape"/>
</filter>
<radialGradient id="paint0_radial_528_564" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1 1) rotate(45) scale(39.598 27.5142)">
<stop stop-color="#8A8A8A" stop-opacity="0.56"/>
<stop offset="1" stop-color="#DDDDDD" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint1_radial_528_564" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1 1) rotate(45) scale(39.598 27.5142)">
<stop stop-color="white" stop-opacity="0.56"/>
<stop offset="1" stop-color="#E9E9E9" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint2_radial_528_564" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10 10) rotate(45) scale(14.1421 9.82651)">
<stop stop-color="white" stop-opacity="0.56"/>
<stop offset="1" stop-color="#E9E9E9" stop-opacity="0.67"/>
</radialGradient>
</defs>
</svg>
