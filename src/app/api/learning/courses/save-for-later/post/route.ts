import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraSaveCourseForLaterService } from '@/services/totara/courses';
import { ISaveForLaterDTO } from '@/types/domain/learning/saved-courses/dto';

/**
 * Route to save course for later.
 */
export async function POST(request: NextRequest) {
  try {
    // Getting request body
    const data: ISaveForLaterDTO = await request.json();

    // Requesting the service
    const response = await TotaraSaveCourseForLaterService(data);

    // Return the response
    return NextResponse.json(response.data);
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
