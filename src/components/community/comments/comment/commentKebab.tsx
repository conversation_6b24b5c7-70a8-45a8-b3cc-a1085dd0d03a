import dictionary from '@/dictionaries';
import Trash from '@/ui/icons/trash';
import Typography from '@/ui/typography';
import styles from './style.module.scss';
import KababMenu from '@/ui/menu/kabab';

interface ICommentKebab {
  onClickDelete: () => void;
}

const CommentKebab: React.FC<ICommentKebab> = ({ onClickDelete }) => {
  return (
    <div className={styles.menuWrapper}>
      <KababMenu>
        <nav>
          <ul className={styles.menuList}>
            <li>
              <button type="button" onClick={onClickDelete}>
                <i>
                  <Trash />
                </i>
                <Typography as="span" dictionary={dictionary.delete} />
              </button>
            </li>
          </ul>
        </nav>
      </KababMenu>
    </div>
  );
};
export default CommentKebab;
