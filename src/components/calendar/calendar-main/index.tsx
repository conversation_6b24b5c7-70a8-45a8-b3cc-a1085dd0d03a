import React, { useState, useCallback, useMemo, useRef } from 'react';
import TopBar from './top-bar';
import IFormatedEvent from '@/types/domain/calendar/formated-event';
import styles from './style.module.scss'; // Keep styles import for calendarMainContainer
import EmptyMessage from '@/ui/empty-message';
import dictionary from '@/dictionaries';
import moment from 'moment';
import Tooltip from './tooltip';
import Spinner from '@/ui/spinner';

import { useTranslations } from 'next-intl';
import CalendarView from './calendar-view';
import TimelineView from './timeline-view';

type TimePeriod = 'daily' | 'weekly' | 'monthly';

interface CalendarMainProps {
  isMyCalendarTab: boolean;
  events: IFormatedEvent[];
  selectedTimePeriod: TimePeriod;
  onTimePeriodChange: (newPeriod: TimePeriod) => void;
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
  isFetching?: boolean;
  isMyCalendar?: boolean;
  handleShowEventDetails: (event: IFormatedEvent) => void;
}

const CalendarMain: React.FC<CalendarMainProps> = ({
  events,
  selectedTimePeriod,
  onTimePeriodChange,
  selectedDate,
  onDateSelect,
  isFetching,
  isMyCalendar,
  handleShowEventDetails,
}) => {
  const [isCalendarView, setIsCalendarView] = useState<boolean>(true);

  const calendarMainContainerRef = useRef<HTMLDivElement>(null);
  const [courseId, setCourseId] = useState<string>('');
  const [showTooltip, setShowTooltip] = useState(false);
  const [elementId, setElementId] = useState<string>('');
  const [tooltipContent, setTooltipContent] = useState<IFormatedEvent | null>(
    null,
  );
  const [tooltipPosition, setTooltipPosition] = useState<{
    top: number;
    left: number;
    width: number;
    height: number;
  } | null>(null);
  const [arrowPosition, setArrowPosition] = useState<
    | {
        top: number;
        onRight: boolean;
        width: number;
        height: number;
      }
    | undefined
  >(undefined);

  const t = useTranslations();
  // Tooltip Event Handler
  const handleEventHover = useCallback(
    (event: IFormatedEvent, rect: DOMRect, elementId: string) => {
      const containerRect =
        calendarMainContainerRef.current?.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // Default positions
      let shouldPlaceOnRight = true;
      let shouldPlaceOnTop = false;

      if (containerRect) {
        shouldPlaceOnRight =
          containerRect.right - rect.right > rect.left - containerRect.left;
        shouldPlaceOnTop =
          rect.top - containerRect.top < containerRect.bottom - rect.bottom;
      }

      let tooltipTop = shouldPlaceOnTop ? rect.height : -rect.height;
      let tooltipLeft = shouldPlaceOnRight ? rect.width + 20 : -rect.width - 20;

      // Ensure tooltip stays within viewport
      const estimatedTop = rect.top + tooltipTop;
      const estimatedLeft = rect.left + tooltipLeft;

      const tooltipWidth = 200; // You may want to dynamically calculate this
      const tooltipHeight = 100; // Same here

      // Adjust if out of vertical bounds
      if (estimatedTop + tooltipHeight > viewportHeight) {
        tooltipTop = viewportHeight - rect.top - tooltipHeight - 10; // 10px padding
      } else if (estimatedTop < 0) {
        tooltipTop = -rect.top + 10;
      }

      // Adjust if out of horizontal bounds
      if (estimatedLeft + tooltipWidth > viewportWidth) {
        tooltipLeft = -tooltipWidth - 10;
      } else if (estimatedLeft < 0) {
        tooltipLeft = 10;
      }

      setTooltipContent(event);
      setTooltipPosition({
        top: tooltipTop,
        left: tooltipLeft,
        width: rect.width,
        height: rect.height,
      });
      setArrowPosition({
        top: shouldPlaceOnTop ? rect.height : -tooltipTop,
        onRight: tooltipLeft <= 0, // flip arrow if tooltip is placed left
        width: rect.width,
        height: rect.height,
      });

      setShowTooltip(true);
      setElementId(elementId);
      setCourseId(event.id || '');
    },
    [],
  );

  const handleCloseTooltip = useCallback(() => {
    setShowTooltip(false);
    setTooltipContent(null);
    setTooltipPosition(null);
  }, []);

  // Handler for showing the DialogDrawer

  // Filter events based on selectedTimePeriod and selectedDate
  const eventsForCurrentPeriod = useMemo(() => {
    if (!events || events.length === 0) {
      return [];
    }

    const mSelectedDate = moment(selectedDate);
    let startOfPeriod;
    let endOfPeriod;

    switch (selectedTimePeriod) {
      case 'daily':
        startOfPeriod = mSelectedDate.clone().startOf('day');
        endOfPeriod = mSelectedDate.clone().endOf('day');
        break;
      case 'weekly':
        startOfPeriod = mSelectedDate.clone().startOf('isoWeek');
        endOfPeriod = mSelectedDate.clone().endOf('isoWeek');
        break;
      case 'monthly':
        startOfPeriod = mSelectedDate.clone().startOf('month');
        endOfPeriod = mSelectedDate.clone().endOf('month');
        break;
      default:
        return [];
    }

    return events.filter((event) => {
      if (!event.startDateTime) {
        return false;
      }
      const eventMoment = moment(event.startDateTime);

      return eventMoment.isBetween(startOfPeriod, endOfPeriod, null, '[]');
    });
  }, [events, selectedDate, selectedTimePeriod]);

  const handlePrevClick = useCallback(() => {
    const newDate = moment(selectedDate);
    switch (selectedTimePeriod) {
      case 'daily':
        newDate.subtract(1, 'days');
        break;
      case 'weekly':
        newDate.subtract(1, 'weeks');
        break;
      case 'monthly':
        newDate.subtract(1, 'months');
        break;
    }
    onDateSelect(newDate.toDate());
    handleCloseTooltip(); // Close tooltip on date change
  }, [selectedDate, selectedTimePeriod, onDateSelect, handleCloseTooltip]);

  const handleNextClick = useCallback(() => {
    const newDate = moment(selectedDate);
    switch (selectedTimePeriod) {
      case 'daily':
        newDate.add(1, 'days');
        break;
      case 'weekly':
        newDate.add(1, 'weeks');
        break;
      case 'monthly':
        newDate.add(1, 'months');
        break;
    }
    onDateSelect(newDate.toDate());
    handleCloseTooltip(); // Close tooltip on date change
  }, [selectedDate, selectedTimePeriod, onDateSelect, handleCloseTooltip]);

  const handleTodayClick = useCallback(() => {
    onDateSelect(new Date());
    handleCloseTooltip(); // Close tooltip on date change
  }, [onDateSelect, handleCloseTooltip]);

  const handleViewTypeChange = useCallback(() => {
    setIsCalendarView((prevIsCalendarView) => !prevIsCalendarView);
    handleCloseTooltip(); // Close tooltip on view change
  }, [handleCloseTooltip]);

  const handleTimePeriodChangeWrapper = useCallback(
    (newPeriod: TimePeriod) => {
      onTimePeriodChange(newPeriod);
      handleCloseTooltip(); // Close tooltip on time period change
    },
    [onTimePeriodChange, handleCloseTooltip],
  );

  const renderContent = () => {
    if (isFetching && eventsForCurrentPeriod.length === 0) {
      return <Spinner />;
    }

    if (!isFetching && eventsForCurrentPeriod.length === 0) {
      return (
        <EmptyMessage icon="content" title={t(dictionary.noLearningEvents)} />
      );
    }

    return (
      <div
        className={`${styles.calendarContentArea} ${
          eventsForCurrentPeriod.length === 0 && styles['emptyState']
        }`}
      >
        {isCalendarView ? (
          <CalendarView
            selectedDate={selectedDate}
            events={eventsForCurrentPeriod}
            selectedTimePeriod={selectedTimePeriod}
            onDateSelect={onDateSelect}
            onEventClick={handleShowEventDetails}
            onEventHover={handleEventHover}
          />
        ) : (
          <TimelineView
            selectedDate={selectedDate}
            events={eventsForCurrentPeriod}
            selectedTimePeriod={selectedTimePeriod}
            onDateSelect={onDateSelect}
            onShowEventDetails={handleShowEventDetails}
            isMyCalendar={isMyCalendar}
          />
        )}
      </div>
    );
  };

  return (
    <div
      className={styles.calendarMainContainer}
      ref={calendarMainContainerRef}
    >
      <TopBar
        currentDate={selectedDate}
        selectedTimePeriod={selectedTimePeriod}
        isCalendarView={isCalendarView}
        onPrevClick={handlePrevClick}
        onNextClick={handleNextClick}
        onTodayClick={handleTodayClick}
        onTimePeriodChange={handleTimePeriodChangeWrapper}
        onViewTypeChange={handleViewTypeChange}
      />

      {renderContent()}

      {showTooltip && tooltipContent && tooltipPosition && (
        <Tooltip
          content={tooltipContent}
          position={tooltipPosition}
          onClose={handleCloseTooltip}
          eventElementId={elementId}
          courseId={courseId}
          arrowPosition={arrowPosition}
        />
      )}
    </div>
  );
};

export default CalendarMain;
