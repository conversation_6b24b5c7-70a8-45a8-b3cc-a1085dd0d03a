'use client';

import { ChangeEvent, FC, useCallback } from 'react';
import InputField from '@/ui/form/input-field';
import useCoachesStore from '@/stores/learning/coaches/list';
import { useTranslations } from 'next-intl';
import dictionaries from '@/dictionaries';
// import { Close, SearchIcon } from '@/ui/icons';
import FilterIcon from '@/ui/icons/filter';

import styles from './style.module.scss';

interface ISearchProps {
  openFilterDrawer: () => void;
}

/**
 * Component to handle search
 */
const Search: FC<ISearchProps> = ({ openFilterDrawer }) => {
  const t = useTranslations();
  // Property to hold input value
  const search = useCoachesStore((x) => x?.filters?.search);

  // Getting fetch action and method to set filter value
  const { fetch: onFetch } = useCoachesStore();

  /**
   * Method to handle clear input value
   */
  // const handleClick = useCallback(() => {
  //   // Setting the filter
  //   setFilter({
  //     key: 'search',
  //     value: '',
  //   });
  //   onFetch({});
  // }, [setFilter, onFetch]);

  /**
   * Method to handle input change
   */
  const handleChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      // Setting the filter
      onFetch({ search: e.target.value });
    },
    [onFetch],
  );

  // Return JSX position-relative
  return (
    <div className={` ${styles.searchWrapper}`}>
      <InputField
        placeholder={t(dictionaries.SearchForACoach)}
        ariaLabel="search"
        endIcon=""
        className={styles.input}
        value={search || ''}
        onChange={handleChange}
      />
      <button
        type="button"
        className={styles.filterBtn}
        onClick={openFilterDrawer}
      >
        <i>
          <FilterIcon />
        </i>
      </button>
      {/* <button type="button" className={styles.searchBtn} onClick={handleClick}>
        <i>{search && search?.length > 0 ? <Close /> : <SearchIcon />}</i>
      </button> */}
    </div>
  );
};

export default Search;
