import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchMyCommunitiesService } from '@/services/totara/my-network';

/**
 * Route to get the My Communities
 */
export async function GET() {
  try {
    // Fetching booked sessions
    const response = await TotaraFetchMyCommunitiesService();
    // Return the response
    return NextResponse.json({
      success: response.data.success,
      joinedCommunities: response.data.records.joined_networks,
      otherCommunities: response.data.records.other_networks,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
