import { authenticateWithSupportCenter } from '@/services/external/support-center';
import { setSupportCenterToken } from '@/utils/auth/support-center';
import { AxiosError } from 'axios';
import { NextResponse } from 'next/server';

/**
 * An API Route that can be called by the application to obtain and securely store a service-to-service token for the Support Center backend.
 */
export async function POST() {
  try {
    const externalResponse = await authenticateWithSupportCenter();

    const token = externalResponse.data?.access;

    if (!token) {
      return new NextResponse('Failed to obtain service token from backend', {
        status: 401,
      });
    }

    await setSupportCenterToken(token);

    return NextResponse.json({
      success: true,
      message: 'Service token obtained and stored successfully.',
    });
  } catch (error) {
    console.error('Support Center service authentication error:', error);
    const axiosError = error as AxiosError;
    return new NextResponse(
      'Failed to authenticate with Support Center service',
      {
        status: (axiosError.response?.status as number) || 500,
      },
    );
  }
}
