@use 'mixins' as *;

.description {
  @include forMarginPadding(padding, size(0), size(0), size(30), size(0));
  border-bottom: solid 1px var(--grey-400);

  p {
    &:last-child {
      @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    }
  }
}

.descriptionText {
  font-size: size(16);

  a {
    text-decoration: underline;
  }

  ul {
    @include forMarginPadding(margin, size(0), size(0), size(30), size(30));
  }

  ol {
    @include forMarginPadding(margin, size(0), size(0), size(30), size(30));
  }

  dl {
    @include forMarginPadding(margin, size(0), size(0), size(30), size(0));

    dd {
      margin-inline-start: 30px;
    }
  }
}

.moreBtn {
  color: var(--text-link-blue);
  font-size: size(16);
  background: none;
  border: none;
  cursor: pointer;

  span {
    display: inline;
    padding-bottom: 0;
    transition: all 0.5s linear;
    background: linear-gradient(
      to bottom,
      var(--text-link-blue) 0%,
      var(--text-link-blue) 98%
    );
    background-size: 0 1px;
    background-repeat: no-repeat;
    background-position: left 100%;

    @include rtl {
      background-position: right 100%;
    }
  }
}

@include hover() {
  .moreBtn {
    &:hover {
      span {
        background-size: 100% 1px;
      }
    }
  }
}

@include for-all-phone() {
  .description {
    @include forMarginPadding(padding, size(0), size(0), size(16), size(0));
  }
}
