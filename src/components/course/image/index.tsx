import { FC, ReactNode, useMemo } from 'react';
import ICourse from '@/types/domain/learning/course';
import Link from '../link';
import CourseImagePlaceHolder from '@public/images/coursePI.jpg';

interface IImage {
  course?: ICourse;
  children?: ReactNode;
  hasLink?: boolean;
  className?: string;
  alt?: string;
  imageURL?: string;
}

// React component to handle course link
const Image: FC<IImage> = (props) => {
  // Deconstructing props
  const { course, className, hasLink, imageURL } = props;

  // Getting course type
  const image = useMemo(() => {
    return imageURL || course?.image || CourseImagePlaceHolder?.src;
  }, [course?.image, imageURL]);

  // Return JSX
  return (
    <div className={className} style={{ backgroundImage: `url(${image})` }}>
      {course && hasLink && <Link course={course} />}
    </div>
  );
};

// Exporting course component
export default Image;
