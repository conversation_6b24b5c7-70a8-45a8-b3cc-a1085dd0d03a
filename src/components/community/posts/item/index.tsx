import { useCallback, useState } from 'react';
import Image from 'next/image';
import Paper from '@/ui/paper';
import Typography from '@/ui/typography';
import { CommentIcon, LikeIcon, Pencil } from '@/ui/icons';

// Styles
import styles from './style.module.scss';
import { ICommunityDiscussion } from '@/types/domain/my-network/communityDetails';
import { getFullNameInitials } from '@/utils/helpers';
import Trash from '@/ui/icons/trash';
import useCommunityDetailsPostsStore from '@/stores/my-network/community-details/posts';
import useAuthStore from '@/stores/auth';
import ShareYourThoughtsPopup from '../../share-your-thoughts/popup';
import { PostTypeEnum } from '../../discussion';
import KababMenu from '@/ui/menu/kabab';
import CommentsList from '../../comments/list';
import useCommunityDetailsCommentsStore from '@/stores/my-network/community-details/comments';
import LikeFillIcon from '@/ui/icons/like-fill';
import Toast from '@/ui/toast';
import {
  EApiAreaTypes,
  EApiComponentTypes,
} from '@/types/domain/my-network/communityPost';
import { useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';
import React from 'react';

interface IPostItemProps {
  details: ICommunityDiscussion;
  isMember?: boolean;
}

const Item: React.FC<IPostItemProps> = ({ details, isMember }) => {
  const [showComments, setShowComments] = useState<boolean>(false);
  const [likes, setLikes] = useState({
    isLiked: details.is_liked,
    likes: details.likes,
  });
  const [isPreview, setIsPreview] = useState(false);

  const postContentTextLonger = details.content_text.length > 120;
  const { totaraProfile } = useAuthStore();

  const { comments, toggleLikeStatus, updateToast, message, clearUpdateToast } =
    useCommunityDetailsCommentsStore();
  const totalNumberOfCommentsForPost = comments[details.id]?.length;

  const [showShareYourThoughtsPopup, setShowShareYourThoughtsPopup] =
    useState<boolean>(false);

  const { deletePost } = useCommunityDetailsPostsStore();
  // Property to hold expand content text state
  const [showFullContent, setShowFullContent] = useState(
    !postContentTextLonger,
  );

  //Method to toggle expand state
  const toggle = useCallback(() => {
    setShowFullContent((prev) => !prev);
  }, []);
  const t = useTranslations();
  const postContentText =
    postContentTextLonger && !showFullContent
      ? details.content_text.slice(0, 120)
      : details.content_text;

  const fullNameInitials = getFullNameInitials(details.fullname);

  const postWrapperMap = {
    [PostTypeEnum.discussion]: '',
    [PostTypeEnum.question]: styles.question,
    [PostTypeEnum.celebration]: styles.popper,
  };

  const onClickEdit = () => {
    openDialog();
  };

  const onClickDelete = () => {
    deletePost(details.id);
  };

  const onClickComments = () => {
    setShowComments(true);
  };

  const openDialog = () => {
    setShowShareYourThoughtsPopup(true);
  };

  const closeDialog = () => {
    setShowShareYourThoughtsPopup(false);
  };

  const renderBorderWrapper = () => {
    if (details.type) {
      return postWrapperMap[details.type.toUpperCase() as PostTypeEnum];
    }
    return '';
  };

  const onClickLike = async () => {
    await toggleLikeStatus(
      details.id,
      details.is_liked,
      EApiAreaTypes.DISCUSSION,
      EApiComponentTypes.CONTAINER_WORKSPACE,
    ).then((response) => {
      if (response.success) {
        setLikes((state) => {
          return {
            isLiked: !state.isLiked,
            likes: state.isLiked ? state.likes - 1 : state.likes + 1,
          };
        });
      }
    });
  };

  const clearMessages = () => {
    clearUpdateToast();
  };

  const handlePreview = (isPreview: boolean) => {
    setIsPreview(!isPreview);
  };

  return (
    <>
      <Paper className={styles.postWrapper + ' ' + `${renderBorderWrapper()}`}>
        <div className={styles.header}>
          {details.userid === totaraProfile?.id && (
            <div
              className={styles.menuWrapper}
              style={{ pointerEvents: !isMember ? 'none' : 'auto' }}
            >
              <KababMenu>
                <nav>
                  <ul className={styles.menuList}>
                    <li>
                      <button type="button" onClick={onClickEdit}>
                        <i>
                          <Pencil />
                        </i>
                        <Typography
                          as="span"
                          dictionary={dictionary.editPost}
                        />
                      </button>
                    </li>
                    <li>
                      <button type="button" onClick={onClickDelete}>
                        <i>
                          <Trash />
                        </i>
                        <Typography
                          as="span"
                          dictionary={dictionary.deletePost}
                        />
                      </button>
                    </li>
                  </ul>
                </nav>
              </KababMenu>
            </div>
          )}
          <div className={styles.avatar}>
            <Typography>{fullNameInitials}</Typography>
          </div>
          <div className={styles.info}>
            <Typography as="p" className={styles.title}>
              {details.fullname}
            </Typography>
            <div className={styles.ownerContainer}>
              <Typography as="span" className={styles.hoursAgo}>
                {details.timecreated}
              </Typography>
              {details.owner && (
                <Typography
                  className={styles.ownerLabel}
                  dictionary={dictionary.owner}
                />
              )}
            </div>
          </div>
        </div>
        <div className={styles.body}>
          <div>{postContentText}</div>
          {postContentTextLonger && (
            <button type="button" className={styles.moreBtn} onClick={toggle}>
              <span className="resizable">
                {showFullContent
                  ? t(dictionary.viewLess)
                  : t(dictionary.viewMore)}
              </span>
            </button>
          )}
          {details.images.length > 0 && (
            <div className={styles.postImage}>
              {details.images.map(
                (image) =>
                  image?.url && (
                    <React.Fragment key={image.url}>
                      <Image
                        src={image.url}
                        alt=""
                        width={200}
                        height={200}
                        onClick={() => handlePreview(isPreview)}
                      />
                      {isPreview && (
                        <div
                          className={styles.lightBoxOverlay}
                          onClick={() => handlePreview(isPreview)}
                        >
                          <div
                            className={styles.lightBoxContent}
                            onClick={(e) => e.stopPropagation()}
                          >
                            <img
                              src={image.url}
                              alt={image.url}
                              className={styles.lightBoxImage}
                            />
                          </div>
                        </div>
                      )}
                    </React.Fragment>
                  ),
              )}
            </div>
          )}
        </div>
        <div className={styles.footer}>
          <button
            type="button"
            className={styles.ftButton}
            onClick={onClickLike}
            disabled={!isMember}
          >
            {likes.isLiked ? (
              <i className={styles.iconLike}>
                <LikeFillIcon />
              </i>
            ) : (
              <i>
                <LikeIcon />
              </i>
            )}
            <Typography as="span">
              <Typography as="span" dictionary={dictionary.like} /> (
              {likes.likes})
            </Typography>
          </button>
          <button
            type="button"
            className={styles.ftButton}
            onClick={onClickComments}
            disabled={!isMember}
          >
            <i>
              <CommentIcon />
            </i>
            <Typography as="span">
              <Typography as="span" dictionary={dictionary.comment} /> (
              {totalNumberOfCommentsForPost ?? details.comments})
            </Typography>
          </button>
        </div>
        {showComments && <CommentsList postId={details.id} />}
      </Paper>
      {showShareYourThoughtsPopup && (
        <ShareYourThoughtsPopup
          postType={details.type.toLocaleUpperCase() as PostTypeEnum}
          handleCloseDialog={closeDialog}
          details={details}
        />
      )}
      {updateToast && (
        <Toast text={message} type={updateToast} onClose={clearMessages} />
      )}
    </>
  );
};

export default Item;
