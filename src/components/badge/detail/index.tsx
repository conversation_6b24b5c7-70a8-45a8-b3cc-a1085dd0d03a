import { FC } from 'react';
import Typography from '@/ui/typography';
import BackOverly from '@/ui/back-overly';
import IBadge from '@/types/domain/learning/badge';

import Image from '@/ui/image';
import { Close } from '@/ui/icons';
import fallback from '@public/images/pro-perfomer.png';

// Styles
import styles from './style.module.scss';

interface IDetail {
  badge: IBadge;
  onClose: () => void;
}

const Detail: FC<IDetail> = (props) => {
  // Destructuring props
  const { badge, onClose } = props;

  // Return JSX
  return (
    <div className={styles.popup}>
      <BackOverly onClick={onClose} />
      <div className={styles.inner}>
        <button type="button" className={styles.closeBtn} onClick={onClose}>
          <i className="fa-icon">
            <Close />
          </i>
        </button>
        <div className={styles.content}>
          <div className={styles.badge}>
            <Image
              width={100}
              height={100}
              fallback={fallback}
              src={badge?.image_url}
              alt={badge?.name}
            />
          </div>
          <div className={styles.badgeTitle}>
            <Typography as="h4">{badge?.name}</Typography>
          </div>
          <div className={styles.scrollView}>
            {badge?.award_criteria?.map((award, i) => (
              <div className={styles.detail} key={'award-' + i}>
                <Typography as="h5">{award?.title}</Typography>
                <nav className={styles.listPoints}>
                  <ol>
                    {award?.criteria?.map((x, j) => (
                      <li key={'critaria-' + j}>
                        {x?.title && (
                          <Typography as="h5">{x?.title}</Typography>
                        )}
                        <div className={styles.title}>
                          <Typography as="p">Required grade: </Typography>
                          <Typography as="p">{x?.grade || '0'}%</Typography>
                        </div>
                        <div className={styles.title}>
                          <Typography as="p">Completion date:</Typography>
                          <Typography as="p">{x?.date || 'N/A'}</Typography>
                        </div>
                        {(award?.criteria?.length || 0) - 1 !== j && (
                          <div className={styles.orTxt}>
                            <Typography as="span">OR</Typography>
                          </div>
                        )}
                      </li>
                    ))}
                  </ol>
                </nav>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Detail;
