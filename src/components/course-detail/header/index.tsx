import { FC, useEffect, useState, useCallback, useMemo } from 'react';
import Link from 'next/link';
import Typography from '@/ui/typography';
import Tag from '@/ui/tag';
import Toast from '@/ui/toast';
import {
  Clock,
  Expand,
  Collapse,
  Lesson,
  UsersOutline,
  WarngingCircle,
} from '@/ui/icons';
import LinearProgress from '@/ui/progress/linear';
import useCheckCourseEnrollmentStore from '@/stores/learning/courses/enrollment/check';
import useCourseDetailStore from '@/stores/learning/courses/detail';
import useCourseEnrollmentStore from '@/stores/learning/courses/enrollment/enroll';
import IEnrollmentOptions from '@/types/domain/learning/course/enrollmentOptions';
import BookSlotAction from './book-slot-action';
import EnrollCourseAction from './enroll-course-action';
import StartCourseAction from './start-course-action';
import WriteReviewAction from './write-review-action';
import govAcademy from '@public/images/gov-academy.png';
import Image from '@/ui/image';
import { useTranslations } from 'next-intl';
import star from '@public/images/star.svg';
import useTotaraAuth from '@/stores/totara-auth';
import Spinner from '@/ui/spinner';
import { motion, cubicBezier, AnimatePresence } from 'motion/react';
import BookSessionAction from './multisession/book-session-action';
import BookedSession from './multisession/booked-session';

// Styles
import styles from './style.module.scss';
import TimeSlot from './time-slot';
import AvailableSeats from './available-seats';
import dictionary from '@/dictionaries';
import usePhysicalCourseStore from '@/stores/learning/courses/physical';
import useMultisessionCourseStore from '@/stores/learning/courses/multisession';
import { AvailableSlots } from '@/types/domain/learning/course';

import { checkThirdPartyCookies } from '@/utils/cookie-check';
import { Info } from '@/ui/icons';

interface IHeader {
  title?: string;
  dueDate?: string;
  courseId?: string | string[];
  courseType?: string;
  hours?: string;
  lessons?: number;
  rating?: string;
  learners?: number;
  isEnrolled?: boolean;
  academyName?: string;
  academyImage?: string;
  progress?: number;
  status?: string;
  reviewAvailabe?: boolean;
  onShowReview: () => void;
}

const Header: FC<IHeader> = (props) => {
  const {
    title,
    dueDate,
    courseId,
    courseType,
    hours,
    lessons,
    rating,
    learners,
    isEnrolled = false,
    academyName,
    academyImage,
    progress,
    status,
    reviewAvailabe,
    onShowReview,
  } = props;

  const [enrollementInfo, setEnrollementInfo] =
    useState<IEnrollmentOptions[]>();
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const onCheckEnrollment = useCheckCourseEnrollmentStore((x) => x.fetch);
  const enrolmentDetails = useCheckCourseEnrollmentStore(
    (x) => x.enrolmentDetails,
  );
  const multisessionToats = useMultisessionCourseStore((x) => x.bookingToast);
  const clearMultisessionToast = useMultisessionCourseStore(
    (x) => x.clearBookingToast,
  );
  const bookingToast = usePhysicalCourseStore((x) => x.bookingToast);
  const clearBookingToast = usePhysicalCourseStore((x) => x.clearBookingToast);
  const enrollToast = useCourseEnrollmentStore((x) => x.enrollToast);
  const clearEnrollToast = useCourseEnrollmentStore((x) => x.clearEnrollToast);
  const courseDetail = useCourseDetailStore((state) => state.detail);
  const totaraAuthUrl = useTotaraAuth((x) => x.totaraAuthUrl);
  const resetTotaraAuthUrl = useTotaraAuth((x) => x.resetTotaraAuthUrl);
  const isLoading = useTotaraAuth((x) => x.isLoading);
  const setIsLoading = useTotaraAuth((x) => x.setIsLoading);
  const isInstructorLedCourse =
    courseType == 'Instructor-led' ||
    courseType == 'Blended' ||
    courseType == 'مزيج';
  const isCompleted = status == 'Complete';
  const isInProgress = status == 'In Progress';
  const notStartedYet = status == 'Not Yet Started';
  const isDueDate =
    status?.toUpperCase().includes('DAY') ||
    status?.toUpperCase().includes('أيام');
  const noCurator = academyName || academyImage ? false : true;
  const [showCookieMessage, setShowCookieMessage] = useState(false);
  const isMultisessionCourse =
    courseDetail?.available_slot_multiday &&
    courseDetail.available_slot_multiday.length > 0;

  const t = useTranslations();
  useEffect(() => {
    setEnrollementInfo(
      enrolmentDetails?.enrolmentOptions?.filter((val) => val.type == 'self'),
    );
  }, [enrolmentDetails]);

  useEffect(() => {
    onCheckEnrollment(courseId as string);
  }, [courseId, onCheckEnrollment]);

  useEffect(() => {
    resetTotaraAuthUrl();
  }, []); // eslint-disable-line

  const showReviewModal = useCallback(() => {
    if (onShowReview) {
      onShowReview();
    }
  }, [onShowReview]);

  const clearMessages = useCallback(() => {
    clearEnrollToast();
  }, [clearEnrollToast]);

  const bookedOrPendingMultidaySessionSlot = useMemo(() => {
    if (!courseDetail || !Array.isArray(courseDetail.available_slot_multiday)) {
      return null;
    }
    const foundSlot = courseDetail.available_slot_multiday.find((slot) => {
      const approvalState = slot.booked;
      return (
        approvalState?.toLowerCase() === 'pending' ||
        approvalState?.toLowerCase() === 'booked'
      );
    });

    return foundSlot || null;
  }, [courseDetail]);

  const isPendingApprovalState = useMemo(() => {
    return (
      bookedOrPendingMultidaySessionSlot &&
      bookedOrPendingMultidaySessionSlot.booked.toLowerCase() === 'pending'
    );
  }, [bookedOrPendingMultidaySessionSlot]);

  const renderCourseAction = () => {
    if (isInstructorLedCourse && !isCompleted) {
      return isMultisessionCourse ? (
        <BookSessionAction list={courseDetail.available_slot_multiday ?? []} />
      ) : (
        <BookSlotAction isPendingApproval={!!slotPendingApproval} />
      );
    } else if (isEnrolled && !isCompleted && courseDetail?.lessons?.length) {
      return <StartCourseAction />;
    } else if (
      (enrollementInfo?.length && !isCompleted && !isEnrolled) ||
      (enrollementInfo?.length && !isEnrolled)
    ) {
      return <EnrollCourseAction courseId={courseDetail?.courseId} />;
    } else if (isCompleted && reviewAvailabe) {
      return <WriteReviewAction onReviewAction={showReviewModal} />;
    }
  };

  const slotPendingApproval = useMemo(() => {
    return courseDetail?.available_slots?.find(
      (slot) => slot.is_booked === 'PENDING',
    );
  }, [courseDetail?.available_slots]);

  const slotToDisplay =
    courseDetail?.assigned_slot && !Array.isArray(courseDetail?.assigned_slot)
      ? courseDetail?.assigned_slot
      : slotPendingApproval
        ? slotPendingApproval
        : courseDetail?.available_slots?.length === 1
          ? courseDetail.available_slots[0]
          : undefined;

  const renderBookingInfo = () => {
    if (isInstructorLedCourse) {
      if (slotToDisplay) {
        return (
          <TimeSlot
            assignedByAdmin={
              slotPendingApproval
                ? undefined
                : courseDetail?.assigned_slot?.assigned_by_admin
            }
            date={slotToDisplay.date}
            startDate={slotToDisplay.startdatetime}
            endDate={slotToDisplay.enddatetime}
            location={
              courseDetail?.assigned_slot &&
              !Array.isArray(courseDetail.assigned_slot)
                ? courseDetail.available_slots?.find(
                    (slot) =>
                      Number(slot.id) === courseDetail.assigned_slot?.id,
                  )?.location
                : (slotToDisplay as AvailableSlots).location
            }
            pendingSlotId={slotPendingApproval?.id}
            isAssignedSlot={
              courseDetail?.assigned_slot &&
              !Array.isArray(courseDetail?.assigned_slot)
            }
          />
        );
      } else if (bookedOrPendingMultidaySessionSlot) {
        return (
          <BookedSession
            assignedMultidaySlot={bookedOrPendingMultidaySessionSlot}
          />
        );
      }
    }
    return null;
  };

  const thirdPartyCookiesEnabled = useCallback(async () => {
    const areCookiesEnabled = await checkThirdPartyCookies();
    if (!areCookiesEnabled && !isLoading) {
      setShowCookieMessage(true);
    }
  }, [setShowCookieMessage, isLoading]);

  useEffect(() => {
    thirdPartyCookiesEnabled();
  }, [thirdPartyCookiesEnabled]);

  const managerApprovalRequired = useMemo(() => {
    return courseDetail?.lessons?.some(
      (lesson) => lesson.manager_approval_required,
    );
  }, [courseDetail]);

  const isApprovedByManager = useMemo(() => {
    return courseDetail?.lessons?.some(
      (lesson) => lesson.manager_approval_state === 'BOOKED',
    );
  }, [courseDetail]);

  return (
    <div className={styles.header}>
      <div className={styles.head}>
        <Typography as="h1" className={styles.courseTitle + ' mb-0'}>
          {title}
        </Typography>
        <div className={styles.tags}>
          <ul>
            {dueDate && (
              <li>
                <Tag color="blue">{dueDate}</Tag>
              </li>
            )}
            {courseType && (
              <li>
                <Tag>{t(courseType) || courseType}</Tag>
              </li>
            )}
            {isDueDate && (
              <li>
                <Tag color="blue">
                  <Typography as="span">
                    {t(dictionary.dueIn) + ' ' + status}
                  </Typography>
                </Tag>
              </li>
            )}
            {notStartedYet && (
              <li>
                <Tag color="blue">
                  <Typography as="span" dictionary={dictionary.notYetStarted} />
                </Tag>
              </li>
            )}
            {isInProgress && (
              <li>
                <Tag color="yellow">
                  <Typography as="span" dictionary={dictionary.inProgress} />
                </Tag>
              </li>
            )}
            {isCompleted && (
              <li>
                <Tag color="green">
                  <Typography as="span" dictionary={dictionary.completed} />
                </Tag>
              </li>
            )}
            {(slotPendingApproval || isPendingApprovalState) && (
              <li>
                <Tag color="yellow">
                  <Typography
                    as="span"
                    dictionary={dictionary.pendingApproval}
                  />
                </Tag>
              </li>
            )}
          </ul>
        </div>
      </div>
      <div className={styles.info}>
        <div className={styles.left}>
          <nav>
            <ul className={styles.courseDetail}>
              <li>
                <i>
                  <Clock />
                </i>
                <Typography as="span">{hours}</Typography>
              </li>
              <li>
                <i>
                  <Lesson />
                </i>
                <Typography as="span" className={styles.activityLabel}>
                  {lessons +
                    `  ${lessons && lessons < 2 ? t(dictionary.activity) : t(dictionary.activities)}`}
                </Typography>
              </li>
              {rating !== '0' && (
                <li>
                  <i>
                    <Image src={star} alt="" className="img-fluid" />
                  </i>
                  <Typography as="span">{rating}</Typography>
                </li>
              )}
            </ul>
          </nav>
          <div className={styles.status}>
            <i>
              <UsersOutline />
            </i>
            <Typography as="span">
              {learners + ` ${t(dictionary.peopleCompletedThisCourse)}`}
            </Typography>
          </div>
          <div className={styles.externalLink}>
            <Link href="#" className={styles.siteLink}>
              <i>
                <Image
                  src={noCurator ? govAcademy : academyImage}
                  alt=""
                  className="img-fluid"
                  width={100}
                  height={100}
                />
              </i>
              <Typography as="span">
                {noCurator ? t(dictionary.govAcademy) : academyName}
              </Typography>
            </Link>
          </div>
        </div>
        <div className={styles.right}>
          {progress ? (
            <div className={styles.progressHead}>
              <div className={styles.text}>
                <Typography
                  as="p"
                  className="lead"
                  dictionary={dictionary.overallProgress}
                />
                <Typography as="h5" className={styles.percentageNumber}>
                  {progress}
                  <span className={styles.percentageSymbol}>%</span>
                </Typography>
              </div>
              <LinearProgress progress={progress} />
            </div>
          ) : null}
          <div>{renderCourseAction()}</div>
        </div>
      </div>
      {managerApprovalRequired && !isApprovedByManager && (
        <div className={styles.approvalWarning}>
          <WarngingCircle />
          <Typography as="span">
            {t(dictionary.lineManagerApprovalRequiredMessage)}
          </Typography>
        </div>
      )}
      <div className={styles.bookingInfo}>{renderBookingInfo()}</div>
      {courseDetail?.number_of_slots &&
        courseDetail?.number_of_slots > 0 &&
        // courseDetail?.isEnrolled &&
        !isCompleted &&
        !Array.isArray(courseDetail?.seat_date_info) &&
        !slotPendingApproval &&
        !(
          courseDetail.assigned_slot &&
          !Array.isArray(courseDetail.assigned_slot)
        ) && (
          <AvailableSeats
            totalAvailableSeat={
              courseDetail?.seat_date_info?.total_available_seat
            }
            startDate={courseDetail?.seat_date_info?.startdate}
          />
        )}
      <div
        className={
          styles.iframeWrapper + ` ${isExpanded ? styles.expanded : ''}`
        }
      >
        {isExpanded ? (
          <span
            className={styles.clickableBackground}
            onClick={() => setIsExpanded(false)}
          ></span>
        ) : null}
        <AnimatePresence>
          {isLoading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{
                ease: cubicBezier(0.71, 0.23, 0.31, 0.86),
                duration: 0.3,
              }}
              className={styles.loadingWrapper}
            >
              <Spinner />
            </motion.div>
          )}
        </AnimatePresence>
        {totaraAuthUrl ? (
          <motion.div
            initial={{
              opacity: 0,
              height: 0,
              position: 'relative',
            }}
            animate={{ opacity: 1, height: 450 }}
            transition={{
              ease: cubicBezier(0.71, 0.23, 0.31, 0.86),
              duration: 0.4,
            }}
            className={
              styles.iframeInnerWrapper +
              ` ${isExpanded ? styles.innerExpanded : ''}`
            }
          >
            {showCookieMessage && (
              <div className={styles.cookieMessage}>
                <div className={styles.cookieMessageText}>
                  <Info />
                  <Typography
                    as="span"
                    dictionary={dictionary.tpCookiesMessage}
                  />
                </div>
              </div>
            )}
            <div className={styles.actionsWrapper}>
              <button
                className={styles.action}
                onClick={() => setIsExpanded(!isExpanded)}
              >
                {!isExpanded ? <Expand /> : <Collapse />}
              </button>
            </div>
            <iframe
              src={totaraAuthUrl}
              className={styles.activityIframe}
              onLoad={() => setIsLoading(false)}
            ></iframe>
          </motion.div>
        ) : null}
      </div>
      {isExpanded ? <div className={styles.placeholder}></div> : null}
      {enrollToast && (
        <Toast
          text={
            enrollToast === 'success'
              ? dictionary.youveSuccessfullyEnrolledInACourse
              : 'There was an error with enrollment'
          }
          type={enrollToast}
          onClose={clearMessages}
        />
      )}
      {bookingToast && (
        <Toast
          text={bookingToast.message}
          type={bookingToast.type}
          onClose={clearBookingToast}
        />
      )}
      {multisessionToats && (
        <Toast
          text={multisessionToats.message}
          type={multisessionToats.type}
          onClose={clearMultisessionToast}
        />
      )}
    </div>
  );
};

export default Header;
