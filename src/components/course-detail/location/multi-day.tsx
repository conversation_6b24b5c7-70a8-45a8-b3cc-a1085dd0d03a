import useCourseDetailStore from '@/stores/learning/courses/detail';
import { AvailableSlotMultiday } from '@/types/domain/learning/course';
import Typography from '@/ui/typography';
import Event from './event';
import styles from './style.module.scss';

const MultiDay = () => {
  const courseDetail = useCourseDetailStore((state) => state.detail);

  const assignedSlot = courseDetail?.available_slot_multiday?.find(
    (slot) =>
      slot.id ===
      String(
        (courseDetail.assigned_slot_multiday as AvailableSlotMultiday[])[0].id,
      ),
  );

  return (
    <>
      <Typography as="p" className={styles.cohortName}>
        {assignedSlot?.name}
      </Typography>
      {assignedSlot?.events.map((event) => (
        <Event key={event.id} event={event} />
      ))}
    </>
  );
};

export default MultiDay;
