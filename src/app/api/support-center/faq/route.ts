import {
  getFaqCategories,
  getFaqItems,
  type FaqCategory,
  type FaqItem,
} from '@/services/external/support-center/faq-crm';
import { getUserLocale } from '@/utils/auth';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';
import { NextRequest, NextResponse } from 'next/server';

// Simple in-memory cache for aggregation results
// Keyed by `${locale}::${search}`
const CACHE_TTL_MS = 10 * 60 * 1000; // 10 minutes
const cache = new Map<string, { expires: number; payload: unknown }>();

export async function GET(request: NextRequest) {
  const search = request.nextUrl.searchParams.get('search') || '';

  try {
    const rawLocale = await getUserLocale();
    const locale = rawLocale?.startsWith('ar') ? 'ar' : 'en';

    const cacheKey = `${locale}::${search}`;
    const hit = cache.get(cacheKey);
    if (hit && hit.expires > Date.now()) {
      return NextResponse.json(hit.payload);
    }

    const categoriesResp = await getFaqCategories();
    const categories = (categoriesResp as unknown as { data: FaqCategory[] })
      .data;

    const concurrency = 5;
    const queue = [...categories];
    const aggregated: Array<{ category: FaqCategory; items: FaqItem[] }> = [];

    const workers = Array.from({
      length: Math.max(1, Math.min(concurrency, queue.length)),
    }).map(async () => {
      while (queue.length) {
        const category = queue.shift();
        if (!category) break;
        try {
          const itemsResp = await getFaqItems({
            categoryId: category.id,
            search,
          });
          const items = ((itemsResp as unknown as { data: FaqItem[] }).data ||
            []) as FaqItem[];
          if (items.length) {
            aggregated.push({ category, items });
          }
        } catch {
          console.log('Failed to fetch items for category', category);
        }
      }
    });

    await Promise.all(workers);

    type FaqQuestionProp = { en_US: string; ar_SA: string };
    type FaqQuestion = {
      id: string;
      question: FaqQuestionProp;
      answer: FaqQuestionProp;
      category: FaqQuestionProp;
    };

    const makeProp = (value: string): FaqQuestionProp =>
      locale === 'en'
        ? { en_US: value || '', ar_SA: '' }
        : { en_US: '', ar_SA: value || '' };

    const items: FaqQuestion[] = [];
    for (const group of aggregated) {
      const categoryName = group.category?.name || '';
      for (const item of group.items) {
        items.push({
          id: String(item.id),
          question: makeProp(item.question),
          answer: makeProp(item.answer),
          category: makeProp(categoryName),
        });
      }
    }

    const payload = { faqQuestions: { items } };

    cache.set(cacheKey, { expires: Date.now() + CACHE_TTL_MS, payload });
    return NextResponse.json(payload);
  } catch (error) {
    return ErrorResponse(error as AxiosError);
  }
}
