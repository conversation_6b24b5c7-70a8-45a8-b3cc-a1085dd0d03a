@use 'mixins' as *;

.list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: size(16);
  counter-reset: section;
}

.card {
  border: solid 1px var(--grey-400);
  @include forMarginPadding(padding, size(16), size(16), size(16), size(72));
  position: relative;
  @include borderRadius(var(--radius));
  height: 100%;
}

.number {
  width: size(40);
  height: size(40);
  background: var(--oat-milk-600);
  @include borderRadius(50%);
  display: flex;
  justify-content: center;
  align-items: center;
  @include forMarginPadding(margin, size(0), size(16), size(0), size(0));
  position: absolute;
  @include leftToRight(size(16));

  &:before {
    color: var(--grey-900);
    counter-increment: section;
    content: counter(section);
    font-size: size(18);
  }
}

@include for-dark-theme {
  .number {
    background: var(--oat-milk-700);

    &:before {
      color: var(--grey-300);
    }
  }

  .externalLink,
  .singleExternalLink {
    &:disabled {
      svg {
        fill: var(--grey-600);
      }
    }

    i {
      display: flex;

      svg {
        fill: var(--grey-900);
        width: 100%;
        height: 100%;
      }
    }
  }
}

.data {
  .title {
    font-weight: 700;
    display: block;
    @include forMarginPadding(margin, size(0), size(0), size(12), size(0));
  }

  .title2 {
    font-size: size(14);
    font-weight: 700;
    display: block;
    @include forMarginPadding(margin, size(0), size(0), size(5), size(0));
  }

  .content {
    font-size: size(14);
    display: block;
    color: var(--grey-800);
  }
}

.weekList {
  ul {
    @include forMarginPadding(margin, size(10), size(0), size(0), size(0));
    @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: size(12);

    li {
      list-style: none;
      @include forMarginPadding(margin, size(0), size(0), size(0), size(0));

      + li {
        @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
      }

      a {
        width: 100%;
        display: flex;
        align-items: center;
        position: relative;
        background: var(--blue-500);
        @include borderRadius(8px);
        color: var(--grey-700);
        fill: var(--grey-900);
        font-size: size(14);
        @include forMarginPadding(
          padding,
          size(7),
          size(10),
          size(7),
          size(10)
        );

        i {
          width: size(20);
          height: size(20);
          @include forMarginPadding(margin, size(0), size(0), size(0), auto);

          svg {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
}

.isCompleted {
  background: var(--success-900);

  &:before {
    opacity: 0;
    font-size: 0;
  }

  svg {
    fill: var(--white);
    height: size(42);
    padding: size(10);
    width: size(40);
  }
}

.itemCard {
  background-color: var(--blue-500);
  border: solid 1px var(--blue-500);
  @include forMarginPadding(padding, size(10), size(10), size(10), size(10));
  position: relative;
  @include borderRadius(size(8));
  width: calc(50% - size(8));

  @include for-all-phone() {
    width: 100%;
  }
}

@include for-dark-theme {
  .itemCard {
    background-color: var(--blue-500);
    border: solid 1px var(--blue-500);

    p {
      color: var(--white);
    }

    button svg {
      fill: var(--white);
    }
  }

  .isCompleted {
    background: var(--success-900);
  }
}

.itemData {
  @include forMarginPadding(padding, size(0), size(50), size(0), size(0));

  .title {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    font-weight: 700;
    display: block;
    font-size: size(14);
  }

  .content {
    font-size: size(14);
    display: block;
    color: var(--grey-800);
  }
}

.itemsWrapper {
  display: flex;
  flex-wrap: wrap;
  gap: size(12);
}

.singleTitle {
  color: var(--grey-900);
  font-size: size(14);
}

.singleDescription {
  color: var(--grey-700);
  font-size: size(14);
}

.externalLink,
.singleExternalLink {
  background: none;
  border: 0;
  position: absolute;
  cursor: pointer;
  @include rightToLeft(size(10));
  top: size(10);
  bottom: 0;
  width: size(15);
  height: size(15);
  text-decoration: none;

  &:disabled {
    cursor: not-allowed;

    svg {
      fill: var(--grey-600);
    }
  }

  i {
    display: flex;

    svg {
      fill: var(--grey-900);
      width: 100%;
      height: 100%;
    }
  }
}

.singleExternalLink {
  @include rightToLeft(size(16));
  top: size(16);
}

@include for-all-phone() {
  .list {
    display: block;

    .card {
      + .card {
        @include forMarginPadding(margin, size(16), size(0), size(0), size(0));
      }
    }
  }

  .weekList {
    ul {
      display: grid;
      grid-template-columns: repeat(1, 1fr);
      grid-gap: size(16);
    }
  }
}
