@use 'mixins' as *;

.card {
  position: relative;
  // overflow: hidden;
  width: 100%;
  height: 100%;
  background: var(--white);

  &:hover {
    div[class*='bg'] {
      transform: scale(1.1, 1.1);
      transition: 0.4s all cubic-bezier(0.77, 0.23, 0.23, 0.81);

      &:before {
        opacity: 0.7;
        transition: 0.4s all cubic-bezier(0.77, 0.23, 0.23, 0.81);
      }
    }

    h4[class*='title'] {
      text-decoration: underline;
    }
  }
}

.bg {
  width: 100%;
  height: size(220);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  position: relative;
  transform: scale(1, 1);
  transition: 0.4s all cubic-bezier(0.77, 0.23, 0.23, 0.81);

  a {
    width: 100%;
    height: 100%;
    position: absolute;
    @include leftToRight(0);
    top: 0;
    z-index: 1;
  }
}

.imageWrapper {
  width: 100%;
  height: size(220);
  @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
  @include borderRadius(var(--radius));
  position: relative;
  overflow: hidden;
}

.top {
  display: flex;
  align-items: center;
  gap: size(4);
  @include forMarginPadding(margin, size(0), size(0), size(22), size(0));

  p {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    font-weight: 400;
    font-size: size(14);
    text-transform: uppercase;
    flex: 1;
  }

  button {
    @include forMarginPadding(margin, size(0), size(0), size(0), auto);
  }

  svg[class*='spinner'] {
    height: size(22);
    width: size(22);
    @include forMarginPadding(margin, size(0), size(0), size(0), auto);
  }
}

.bottom {
  @include forMarginPadding(margin, auto, 0, 0, 0);
  display: flex;
  flex-direction: column;
  height: calc(100% - size(236));
}

.heading {
  font-size: size(18);
  min-height: size(43);
  margin-bottom: size(16);
}

div.progress {
  @include forMarginPadding(margin, auto, size(0), size(0), size(0));
}

nav.listNav {
  @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
}

.provider {
  line-height: size(11);
  font-size: size(16);
  min-height: size(11);
  @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
}

div.cardTags {
  @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
}

@include for-dark-theme() {
  .card {
    color: var(--grey-900);
    fill: var(--grey-900);
  }

  .provider {
    color: var(--grey-800);
  }
}

@include for-all-phone() {
  .bg {
  }
}
