@use 'mixins' as *;

.bookedSessionWrapper {
  background-color: var(--bg-shade-4);
  border-radius: size(12);
  @include forMarginPadding(padding, size(24), size(24), size(24), size(24));
  @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
  max-width: size(336);
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.sessionInfo {
  display: flex;
  flex-direction: column;
  gap: size(8);
}

.sessionHeader,
.sessionInfoRow {
  align-items: center;
  display: flex;
  gap: size(8);
}

.sessionHeader {
  width: calc(100% - size(40));

  .iconWrapper {
    @include forMarginPadding(padding, size(9), size(8), size(8), size(8));
    display: flex;
    flex-direction: row;
    justify-content: center;
    background-color: var(--warning-900);
    border-radius: 50%;
    height: size(32);
    width: size(32);

    svg {
      fill: var(--white);
      height: size(15);
      width: size(14);
    }
  }

  span {
    font-size: size(18);
    font-weight: 700;
    color: var(--grey-900);
  }
}

.sessionInfoRow {
  align-items: center;
  svg {
    fill: var(--grey-900);
    height: size(21);
    width: size(21);

    path {
      stroke: var(--grey-900);
    }
  }

  span {
    font-size: size(18);
    font-weight: 400;
    line-height: 1;
  }
}

.menuWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: size(32);
  height: size(32);
  gap: size(16);
  position: absolute;
  top: size(24);
  right: size(24);
  border: 1px solid var(--grey-700);
  @include borderRadius(50%);

  button {
    width: size(32);
    height: size(32);
  }

  @include rtl {
    right: auto;
    left: size(10);
  }

  div[class*='menu'] {
    // @include forMarginPadding(padding, size(12), size(18), size(12), size(18));
    background-color: var(--tag);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    width: auto;
    min-width: auto;

    button {
      width: size(220);
    }
  }

  ul {
    list-style: none;
  }

  li {
    &:not(:last-child) {
      @include forMarginPadding(
        padding,
        size(12),
        size(18),
        size(12),
        size(18)
      );
    }
  }

  .menuItem {
    display: flex;
    align-items: center;
    border: none;
    width: 100%;
    height: 100%;
    background: none;
    font-size: size(16);
    @include forMarginPadding(padding, size(16), size(16), size(16), size(16));
    @include transitions(0.2s);
    cursor: pointer;
    gap: size(14);

    & i {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    & svg {
      fill: var(--grey-900);
      width: size(20);
      height: size(20);
    }

    span {
      font-size: size(14);
      font-weight: 400;
      color: var(--grey-900);
    }
  }
}

@include hover() {
  @include for-dark-theme {
    .menuWrapper {
      .menuItem:hover {
        background-color: var(--grey-500);
      }
    }
  }
  .menuWrapper {
    .menuItem:hover {
      background-color: var(--grey-300);
    }
  }
}

@include for-dark-theme() {
  .bookedSessionWrapper {
    background-color: var(--bg-card-1);
  }
}
