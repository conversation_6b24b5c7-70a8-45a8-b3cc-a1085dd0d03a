import React, { FC, useEffect, useMemo } from 'react';
import CONFIG from '@/config';
import CardsSection from '@/components/common/cards-section';
import Course from '@/components/course';
import EmptyMessage from '@/ui/empty-message';
import useFavoritesCoursesStore from '@/stores/learning/pathways/favorites';
import styles from './style.module.scss';
import dictionary from '@/dictionaries';

interface IFavorite {
  active: string;
  filters: { id: string; title: string }[];
  onChange: (id: string) => void;
}

// Favorite component
const Favorite: FC<IFavorite> = (props) => {
  // Deconstructing props
  const { active, filters, onChange } = props;

  // Method to fetch courses
  const isFetching = useFavoritesCoursesStore((x) => x.isFetchingFavorites);
  const list = useFavoritesCoursesStore((x) => x.favoritesList);
  const onFetch = useFavoritesCoursesStore((x) => x.fetchFavorites);

  // Creating listing page url
  const page = useMemo(() => {
    // If list has more than 3 items
    if (list?.length > 10) {
      return CONFIG.routes.myCourseList.replace('{id}', 'favorites');
    }
    // Return the listing page URL
    return null;
  }, [list]);

  // Creating dyanmic array of cards
  const items = useMemo(() => {
    // Return the courses
    return list?.map((item) => <Course key={item?.id} course={item} />);
  }, [list]);

  // Effect to fetch the courses
  useEffect(() => {
    if (isFetching === null) {
      onFetch({
        type: 'personal',
      });
    }
  }, [isFetching, onFetch]);

  // Return JSX
  return (
    <CardsSection
      title={dictionary.myLearning}
      listingPageUrl={page}
      items={items}
      limit={10}
      loading={isFetching}
      // forceViewAll={items && items.length >= 10}
      className="learningSlider2"
      wrapperClass={styles.mylearningWrapper}
      filters={filters}
      activeFilterOption={active}
      onChangeFilter={onChange}
      slidesConfig={{
        desktop: {
          view: list?.length > 4 ? 4.2 : 4,
          gap: 16,
        },
        tablet: {
          view: list?.length > 3 ? 3.2 : 3,
          gap: 16,
        },
        mobile: {
          view: 1.2,
          gap: 10,
        },
      }}
      noDataMessage={
        <EmptyMessage
          icon="search"
          title={dictionary.noCoursesAvailableUnderThisSection}
        />
      }
    />
  );
};

// Export the component
export default Favorite;
