import { useCallback, useEffect, useMemo, useState } from 'react';
import CardsSection from '@/components/common/cards-section';
import Course from '@/components/course';
import EmptyMessage from '@/ui/empty-message';
import { useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';
import { topPickCourses } from '@/hooks/ai/chat/constants';
import createAiUserProfile from '@/hooks/ai/profile/useAiUserProfile';
import useAuthStore from '@/stores/auth';
import useChatStore from '@/stores/ai/chat';
import createChatPayload from '@/hooks/ai/chat/useChatPayload';
import { IChatCourseResponse } from '@/types/domain/ai/chatResponse';
import dictionaries from '@/dictionaries';
import { getRandom10 } from '@/utils/helpers';

const AITopPicks = () => {
  const [list, setList] = useState<IChatCourseResponse[]>();

  const { user } = useAuthStore();
  const fetch = useChatStore((x) => x.fetch);
  const isFetching = useChatStore((x) => x.isFetching);

  const t = useTranslations();
  // Creating dyanmic array of cards
  const items = useMemo(() => {
    // Mapping list to create cards
    return (
      list?.map((item) => {
        const course = {
          id: parseInt(item.CourseURL.split('id=')[1]),
          title: item.Name,
          duration: item.Duration,
          image: item.ImageURL,
          description: item.Summary,
          course_type: item.Type,
        };
        return <Course key={course.id} course={course} variant="beta" />;
      }) || []
    );
  }, [list]);

  const fetchAItopPicks = useCallback(async () => {
    if (user) {
      const promptData = topPickCourses.prompt + createAiUserProfile(user);
      const payload = createChatPayload(promptData, 50);
      const response = await fetch(payload);
      if (response?.data.success) {
        const randomCourses = getRandom10(
          response.data.data.search_results['gov-academy-courses'],
        );
        setList(randomCourses);
      }
    }
  }, [fetch, user]);

  useEffect(() => {
    fetchAItopPicks();
  }, [fetchAItopPicks]);

  // Return JSX
  return (
    <CardsSection
      title={t(dictionaries.AITopPicks)}
      items={items}
      limit={10}
      loading={isFetching}
      className="learningSlider"
      slidesConfig={{
        desktop: {
          view: 2.65,
          gap: 18,
        },
        mobile: {
          view: 1,
          gap: 10,
        },
      }}
      noDataMessage={
        <EmptyMessage
          icon="search"
          title={t(dictionary.noSearchResultsFound)}
          description={t(dictionary.pleaseCheckThatAllWordsAreSpelledCorrectly)}
        />
      }
    />
  );
};

export default AITopPicks;
