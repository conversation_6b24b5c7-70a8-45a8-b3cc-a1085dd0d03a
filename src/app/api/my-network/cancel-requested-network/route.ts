import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraCancelRequestedNetworkService } from '@/services/totara/my-network/cancel-requested-network';

/**
 * Route to cancel requested network
 */
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const id = request.nextUrl.searchParams.get('id') || '';
    // Fetching cancel requested network
    const response = await TotaraCancelRequestedNetworkService(Number(id));
    // Return the response
    return NextResponse.json({
      data: response.data,
    });
  } catch (error) {
    // Rturn Error response
    return ErrorResponse(error);
  }
}
