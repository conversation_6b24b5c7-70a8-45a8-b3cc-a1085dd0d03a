import { createCase } from '@/services/external/support-center';
import { authenticateWithSupportCenter } from '@/services/external/support-center';
import { ICreateCasePayload } from '@/types/domain';
import {
  getVerifiedSupportCenterPayload,
  setSupportCenterToken,
} from '@/utils/auth/support-center';
import { SupportCaseErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';
import { NextRequest, NextResponse } from 'next/server';

async function ensureServiceToken() {
  try {
    await getVerifiedSupportCenterPayload();
  } catch {
    try {
      // Use the proper external service for server-side authentication
      const externalResponse = await authenticateWithSupportCenter();
      const token = externalResponse.data?.access;

      if (!token) {
        throw new Error('Failed to obtain service token from backend');
      }

      await setSupportCenterToken(token);
    } catch (authError) {
      console.warn(
        'Support Center service temporarily unavailable:',
        authError,
      );
      // Gracefully handle when external service is down
      throw new Error(
        'Support Center service is currently unavailable. Please try again later.',
      );
    }
  }
}

// Server side api that creates case.
export async function POST(request: NextRequest) {
  try {
    await ensureServiceToken();

    const payload: ICreateCasePayload = await request.json();

    const response = await createCase(payload);

    return NextResponse.json({
      data: response.data,
    });
  } catch (error) {
    const axiosError = error as AxiosError;

    // Handle specific service unavailability errors
    if (
      axiosError.message?.includes(
        'Support Center service is currently unavailable',
      )
    ) {
      return new NextResponse(
        'Support Center service is temporarily unavailable. Please try again later.',
        { status: 503 },
      );
    }

    // Handle authentication errors
    if (axiosError.response?.status === 401) {
      return new NextResponse(
        'Authentication failed. Please refresh the page and try again.',
        { status: 401 },
      );
    }

    // Use the default error handler for other errors
    return SupportCaseErrorResponse(axiosError);
  }
}
