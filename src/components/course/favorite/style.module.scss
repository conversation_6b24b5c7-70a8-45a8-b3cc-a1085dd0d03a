@use 'mixins' as *;

.button {
  position: relative;
  @include borderRadius(50%);
  display: flex;
  justify-content: center;
  align-items: center;
  width: size(22);
  height: size(22);
  border: none;
  cursor: pointer;
  @include transitions(0.5s);
  fill: var(--grey-900);
  overflow: hidden;
  background: none;

  i {
    width: size(20);
    height: size(18);
    display: block;
    position: relative;
    z-index: 2;

    svg {
      vertical-align: top;
      fill-rule: evenodd;
    }
  }

  &.active {
    fill: var(--danger-900);
  }
}

@include for-dark-theme() {
  .button {
    &.active {
      fill: var(--danger-900);
    }
  }
}
