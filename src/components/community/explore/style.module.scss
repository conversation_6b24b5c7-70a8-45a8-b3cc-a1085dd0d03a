@use 'mixins' as *;

.item {
  position: relative;
  @include forMarginPadding(padding, size(0), size(25), size(0), size(25));
  @include forMarginPadding(margin, size(0), size(-25), size(0), size(-25));
  transition: all 0.1s ease-in;
  display: block;

  .inner {
    display: flex;
    border-bottom: 1px solid var(--grey-500);
    @include forMarginPadding(padding, size(32), size(0), size(32), size(0));

    .img {
      width: size(64);
      height: size(64);
      object-fit: cover;
      @include borderRadius(var(--radius));
      @include forMarginPadding(margin, size(0), size(16), size(0), size(0));
    }

    .right {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .title {
        margin-bottom: 0;
        font-size: size(16);
        color: var(--grey-900);
        @include forMarginPadding(margin, size(0), size(12), size(0), size(0));
      }

      .members {
        display: flex;
        align-items: center;
        color: var(--grey-700);
        font-size: size(16);

        svg {
          fill: var(--grey-700);
          width: size(16);
          height: size(16);
          @include forMarginPadding(margin, size(0), size(8), size(0), size(0));
        }
      }
    }
  }
}

.viewAll {
  display: flex;
  justify-content: center;
  @include forMarginPadding(margin, size(24), size(0), size(0), size(0));
  font-size: size(16);
  text-decoration: underline;
}

.title {
  font-size: size(22);
  font-weight: 700;
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));
}

@include hover() {
  .item {
    &:hover {
      background-color: var(--grey-300);
    }
  }
}
