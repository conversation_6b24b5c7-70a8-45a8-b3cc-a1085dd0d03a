import React, { useRef, useEffect, useMemo } from 'react';
import usePersonalListsStore from '@/stores/learning/personal-lists';
import styles from './style.module.scss';
import Typography from '@/ui/typography';
import dictionary from '@/dictionaries';
import { Close, PlusThin } from '@/ui/icons';
import { useTranslations } from 'next-intl';
import AddToListModalCard from '../add-to-list-modal-card';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/my-courses/loading';
import Button from '@/ui/form/button';
import useFavoritesCoursesStore from '@/stores/learning/pathways/favorites';

interface AddToListModalProps {
  isOpened: boolean;
}

const AddToListModal: React.FC<AddToListModalProps> = ({ isOpened }) => {
  const dialogRef = useRef<HTMLDialogElement>(null);

  const {
    fetch: fetchLists,
    isFetching,
    list,
    toggleShowAddListDrawer,
    setShowAddToListFromFavorites,
    addingCourses,
    removingCourses,
  } = usePersonalListsStore();

  const { fetchFavorites } = useFavoritesCoursesStore();

  const t = useTranslations();

  const sortedList = useMemo(() => {
    const _items = list || [];
    return [..._items].sort((a, b) => {
      const timeA = new Date(a.timemodified || 0).getTime();
      const timeB = new Date(b.timemodified || 0).getTime();
      return timeB - timeA;
    });
  }, [list]);

  useEffect(() => {
    if (isOpened) {
      dialogRef.current?.showModal();
    } else {
      dialogRef.current?.close();
    }
  }, [isOpened]);

  useEffect(() => {
    fetchFavorites();
    fetchLists();
  }, [fetchFavorites, fetchLists, addingCourses, removingCourses]);

  return (
    <dialog
      ref={dialogRef}
      className={
        styles.addToListDialog +
        ` ${isOpened ? styles.addToListDialogOpened : ''}`
      }
    >
      <WithLoader loader={<Loading />} loading={isFetching}>
        <div className={styles.addToListModalHeader}>
          <div className={styles.addToListModalHeaderCloseContainer}>
            <button
              className={styles.closeAddToListModalButton}
              onClick={() => {
                setShowAddToListFromFavorites(false);
              }}
            >
              <Close />
            </button>
          </div>
          <div className={styles.addToListModalTitle}>
            <Typography as="h3" dictionary={dictionary.addToList} />
            <div
              className={styles.createListButtonContainer}
              onClick={() => {
                toggleShowAddListDrawer(true);
                setShowAddToListFromFavorites(false);
              }}
            >
              <i>
                <PlusThin />
              </i>
              <Typography as="span" className={styles.createListButton}>
                {t(dictionary.createNewList)}
              </Typography>
            </div>
          </div>
        </div>
        <div
          className={`${styles.addToListModalListContainer} ${sortedList.length > 3 ? styles.hasScrollBar : ''}`}
        >
          {sortedList.length &&
            sortedList.map((personalList, i) => (
              <AddToListModalCard
                list={personalList}
                key={`${personalList.id}-${i}`}
              />
            ))}
        </div>
        <Button
          className={styles.closeModalButton}
          dictionary={dictionary.confirm}
          onClick={() => {
            setShowAddToListFromFavorites(false);
          }}
        />
      </WithLoader>
    </dialog>
  );
};

export default AddToListModal;
