import { useEffect } from 'react';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';
import Paper from '@/ui/paper';
import Grid from '@/ui/grid';
import EmptyMessage from '@/ui/empty-message';
import useNewCoursesStore from '@/stores/learning/courses/new';
import Course from '@/components/course';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';

const NewCoursesList = () => {
  // Getting loading state
  const isFetching = useNewCoursesStore((state) => state.isFetching);

  // Getting method to fetch categories
  const onFetch = useNewCoursesStore((state) => state.fetch);

  // Getting categories
  const list = useNewCoursesStore((state) => state.list);

  // Effect to fetch categories
  useEffect(() => {
    if (list?.length === 0) {
      // fetching
      onFetch();
    }
  }, [onFetch, list]);
  const t = useTranslations();
  return (
    <WithLoader loading={isFetching} loader={<Loading />}>
      <Paper>
        {!isFetching && list.length > 0 ? (
          <Grid xs={1} sm={3} md={4} gap="lg">
            {list.map((item) => {
              return <Course key={item.courseid} course={item} />;
            })}
          </Grid>
        ) : (
          <EmptyMessage
            icon="search"
            title={t(dictionary.noSearchResultsFound)}
            description={t(
              dictionary.pleaseCheckThatAllWordsAreSpelledCorrectly,
            )}
          />
        )}
      </Paper>
    </WithLoader>
  );
};

export default NewCoursesList;
