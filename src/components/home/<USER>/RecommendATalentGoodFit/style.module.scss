@use 'mixins' as *;

.interestsWrapper {
  @include forMarginPadding(margin, size(15), size(0), size(12), size(0));
  display: flex;
  gap: size(12);
  flex-wrap: wrap;
  margin-bottom: size(36);
}

.buttons {
  bottom: size(52);
  position: absolute;
  width: calc(100% - size(80));
  display: flex;
  flex-direction: row;
  gap: size(8);

  &[disabled] {
    background: var(--grey-700) !important;
    color: var(--disabled) !important;
  }

  @include for-all-phone() {
    width: calc(100% - size(48));
  }
}
