'use client';
import { useCallback, useEffect } from 'react';
import CONFIG from '@/config';
import Paper from '@/ui/paper';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';
import Item from './item';

// Styles
import styles from './style.module.scss';
import useCoacheDetailStore from '@/stores/learning/coaches/detail';
import useCoachesStore from '@/stores/learning/coaches/list';
import EmptyMessage from '@/ui/empty-message';
import dictionary from '@/dictionaries';

const Coaches = () => {
  // Getting loading state action
  const isFetching = useCoachesStore((x) => x.isFetching);

  // Getting fetch action
  const onFetch = useCoachesStore((x) => x.fetch);
  const clearFilters = useCoachesStore((x) => x.clearFilters);

  // Geting the pathways
  const list = useCoachesStore((x) => x.list);

  // Getting the coach details action for visibility
  const setVisible = useCoacheDetailStore((x) => x.setVisible);

  // Getting the coach details fetch action
  const onDetailFetch = useCoacheDetailStore((x) => x.fetch);

  /**
   * Method to handle click event
   */
  const onClick = useCallback(
    (id: string) => {
      // Fetching the coach details
      onDetailFetch(id);
      // Showing detail popup
      setVisible(true);
    },
    [onDetailFetch, setVisible],
  );

  // Effect to fetch the coaches
  useEffect(() => {
    // Clear coaching filters
    clearFilters();
    // Fetching the coaches
    onFetch({});
  }, [onFetch, clearFilters]);

  // Return JSX
  return (
    <Paper
      title={dictionary.coaching}
      titleVariant="large"
      linkTitle={dictionary.viewAll}
      link={CONFIG.routes.coaches}
    >
      <WithLoader
        loader={<Loading className={styles.coachingWrapper} />}
        loading={isFetching}
      >
        {list?.length === 0 && !isFetching ? (
          <EmptyMessage
            icon="search"
            title={dictionary.noInformationAvailable}
          />
        ) : (
          <nav className={styles.list}>
            <ul>
              {list?.slice(0, 5)?.map((coach) => (
                <li key={coach?.id}>
                  <Item
                    coachId={coach?.id}
                    image={coach?.picture}
                    title={`${coach?.firstname} ${coach?.lastname}`}
                    designation={coach?.designation || '-'}
                    rating={coach?.rating}
                    ratingCount={coach?.user_count}
                    onClick={onClick}
                  />
                </li>
              ))}
            </ul>
          </nav>
        )}
      </WithLoader>
    </Paper>
  );
};

export default Coaches;
