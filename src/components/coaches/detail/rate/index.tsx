import { FC, useCallback, useState } from 'react';
import Typography from '@/ui/typography';
import Image from 'next/image';
import Button from '@/ui/form/button';
import { LeftArrow } from '@/ui/icons';
import styles from './style.module.scss';
import dictionary from '@/dictionaries';
import useRateCoachStore from '@/stores/learning/coaches/rate-coach';
import starOutline from '@public/images/star-outline.svg';
import star from '@public/images/star.svg';
import Toast from '@/ui/toast';
import { useLocale, useTranslations } from 'next-intl';
import RightArrow from '@/ui/icons/right-arrow';

interface IRate {
  coachId: string;
  myRate: string | undefined;
  onBack: () => void;
  onSubmitted: () => void;
}

// React component to handle coach booking
const Rate: FC<IRate> = (props) => {
  const { coachId, myRate, onBack, onSubmitted } = props;
  const [toastMessage, setToastMessage] = useState<string | null>(null);
  const [reviewRating, setReviewRating] = useState<number>(
    Number(myRate) ? Number(myRate) : 0,
  );
  const locale = useLocale();
  const t = useTranslations();
  const fetchRate = useRateCoachStore((x) => x.fetch);
  const isFetching = useRateCoachStore((x) => x.isFetching);
  // const rateMessage = useRateCoachStore((x) => x.rateMessage);

  const onRateSubmit = useCallback(async () => {
    const response = await fetchRate(coachId, reviewRating);
    if (response.status) {
      setToastMessage(t(dictionary.rateSubmittedSuccessfully));
    }
    setTimeout(() => {
      onSubmitted();
    }, 2000);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetchRate, reviewRating, coachId, onSubmitted]);

  const clearMessages = useCallback(() => {
    setToastMessage(null);
  }, [setToastMessage]);

  // Return JSX
  return (
    <>
      <div className={styles.head}>
        <button type="button" className={styles.backDetail} onClick={onBack}>
          <i className="fa-icon">
            {locale == 'ar' ? <RightArrow /> : <LeftArrow />}
          </i>
          <Typography as="span">
            {myRate
              ? t(dictionary.viewYourRating)
              : t(dictionary.rateYourCoach)}
          </Typography>
        </button>
      </div>
      <div className={styles.body}>
        {Boolean(!myRate) && (
          <div className={styles.textWrapper}>
            <Typography
              className={styles.subtitle}
              dictionary={dictionary.yourRatingHelpsOther}
            />
            <Typography
              className={styles.description}
              dictionary={dictionary.helpUsImproveCoaching}
            />
          </div>
        )}
        <nav className={styles.rating}>
          <ul>
            <li>
              <button
                type="button"
                className={reviewRating > 0 ? styles.active : ''}
                disabled={Boolean(myRate)}
                onClick={() => setReviewRating(1)}
              >
                <i>
                  <Image src={starOutline} alt="" className={styles.outline} />
                  <Image src={star} alt="" className={styles.filled} />
                </i>
              </button>
            </li>
            <li>
              <button
                type="button"
                className={reviewRating > 1 ? styles.active : ''}
                disabled={Boolean(myRate)}
                onClick={() => setReviewRating(2)}
              >
                <i>
                  <Image src={starOutline} alt="" className={styles.outline} />
                  <Image src={star} alt="" className={styles.filled} />
                </i>
              </button>
            </li>
            <li>
              <button
                type="button"
                className={reviewRating > 2 ? styles.active : ''}
                disabled={Boolean(myRate)}
                onClick={() => setReviewRating(3)}
              >
                <i>
                  <Image src={starOutline} alt="" className={styles.outline} />
                  <Image src={star} alt="" className={styles.filled} />
                </i>
              </button>
            </li>
            <li>
              <button
                type="button"
                className={reviewRating > 3 ? styles.active : ''}
                disabled={Boolean(myRate)}
                onClick={() => setReviewRating(4)}
              >
                <i>
                  <Image src={starOutline} alt="" className={styles.outline} />
                  <Image src={star} alt="" className={styles.filled} />
                </i>
              </button>
            </li>
            <li>
              <button
                type="button"
                className={reviewRating > 4 ? styles.active : ''}
                disabled={Boolean(myRate)}
                onClick={() => setReviewRating(5)}
              >
                <i>
                  <Image src={starOutline} alt="" className={styles.outline} />
                  <Image src={star} alt="" className={styles.filled} />
                </i>
              </button>
            </li>
          </ul>
        </nav>
      </div>
      <div className={styles.footer}>
        {Boolean(!myRate) && (
          <Button
            type="button"
            color="black"
            loading={isFetching}
            disabled={isFetching || reviewRating == 0}
            className={styles.innerBookCta}
            dictionary={dictionary.submit}
            onClick={onRateSubmit}
          ></Button>
        )}
      </div>
      {toastMessage && (
        <Toast
          text={toastMessage as string}
          type={'success'}
          onClose={clearMessages}
        />
      )}
    </>
  );
};

export default Rate;
