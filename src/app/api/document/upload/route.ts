import { ExternalDocumentUploadService } from '@/services/external/document/document.service';
import { NextRequest, NextResponse } from 'next/server';

// To handle a POST request to /api
export async function POST(request: NextRequest) {
  // Parse the incoming form data
  const formData = await request.formData();

  // Getting folder for file to upload
  const folder = formData.get('folder')?.toString() || 'assets';

  // Get the file from the form data
  const file = formData.get('file');

  // Check if a file is received
  if (!file) {
    // If no file is received, return a JSON response with an error and a 400 status code
    return NextResponse.json({ error: 'No files received.' }, { status: 400 });
  }

  //Getting user form liferay
  const response = await ExternalDocumentUploadService(formData, folder);

  // Do whatever you want
  return NextResponse.json({ id: response?.data?.fileName });
}
