'use client';

import CONFIG from '@/config';
import useSettingsStore from '@/stores/settings';
import { useCallback } from 'react';
import DialogDrawer from '@/ui/dialog-drawer';
import {
  Info,
  RightArrow,
  FileCheck,
  Logout,
  DarkMode,
  Help,
  Linkedin,
  X,
  Instagram,
  GlobeIcon,
  LeftArrow,
} from '@/ui/icons';
import Typography from '@/ui/typography';
import Link from 'next/link';
import styles from './style.module.scss';
import ThemeSwitcher from '@/screens/settings/theme-button';
import { useLocale, useTranslations } from 'next-intl';
import OptionList from '../option-list';
import dictionaries from '@/dictionaries';

const SettingsBar = () => {
  const { displaySettingsBar, setDisplaySettingsBar } = useSettingsStore();
  const t = useTranslations();
  const closeSettingsBar = () => {
    setDisplaySettingsBar(false);
  };

  const locale = useLocale();

  const toggleLanguage = useCallback(() => {
    const newLocale = locale === 'ar' ? 'en' : 'ar';
    const newUrl = window.location.href.replace(
      '/' + locale + '/',
      '/' + newLocale + '/',
    );
    window.location.replace(newUrl);
  }, [locale]);

  const aboutLink = 'https://www.dge.gov.ae/en/what-we-do/programs';

  const privacyPolicyLink =
    'https://www.dge.gov.ae/en/policies-and-legislations';

  const logoutLink =
    CONFIG.application.internalAPIEndpoint +
    CONFIG.endpoints.internal.auth.logout.attempt;
  function getArrow() {
    return locale == 'en' ? <RightArrow /> : <LeftArrow />;
  }

  const linkedInLink =
    'https://ae.linkedin.com/company/department-of-government-enablement';

  const xLink = 'https://twitter.com/DGEabudhabi';

  const instagramLink = 'https://www.instagram.com/DGEabudhabi';

  return (
    <DialogDrawer
      title={t(dictionaries.Settings)}
      isOpened={displaySettingsBar}
      onClose={closeSettingsBar}
    >
      <Typography as="h3" className={styles.settingsSectionTypography}>
        {t(dictionaries.General)}
      </Typography>
      <div
        className={
          locale === 'en'
            ? styles.optionListContainer
            : styles.optionListContainerAr
        }
      >
        <OptionList
          options={[
            {
              text: locale === 'ar' ? 'English' : 'العربية',
              leftIcon: <GlobeIcon />,
              isRoute: true,
              onClick: () => {
                toggleLanguage();
                closeSettingsBar();
              },
            },
            {
              text: t(dictionaries.DarkMode),
              leftIcon: <DarkMode />,
              toggleSwitch: <ThemeSwitcher toggle={true} />,
              removeDivider: true,
            },
          ]}
        />
      </div>
      <Typography as="h3" className={styles.settingsSectionTypography}>
        {t(dictionaries.Other)}
      </Typography>
      <div
        className={
          locale === 'en'
            ? styles.optionListContainer
            : styles.optionListContainerAr
        }
      >
        <OptionList
          options={[
            {
              text: t(dictionaries.About),
              leftIcon: <Info />,
              rightIcon: getArrow(),
              link: aboutLink,
              linkTarget: 'blank',
            },
            {
              text: t(dictionaries.SupportCenter),
              leftIcon: <Help />,
              rightIcon: getArrow(),
              link: CONFIG.routes.supportCenter,
              isRoute: true,
              onClick: () => {
                closeSettingsBar();
              },
            },
            {
              text: t(dictionaries.PrivacyPolicy),
              leftIcon: <FileCheck />,
              rightIcon: getArrow(),
              link: privacyPolicyLink,
              linkTarget: 'blank',
            },
            {
              text: t(dictionaries.Logout),
              leftIcon: <Logout />,
              rightIcon: getArrow(),
              link: logoutLink,
              removeDivider: true,
            },
          ]}
        />
      </div>
      <div className={styles.iconContainer}>
        <Link href={linkedInLink} target="blank">
          <i className={styles.leftIcon}>
            <Linkedin />
          </i>
        </Link>
        <Link href={xLink} target="blank">
          <i className={styles.leftIcon}>
            <X />
          </i>
        </Link>
        <Link href={instagramLink} target="blank">
          <i className={styles.leftIcon}>
            <Instagram />
          </i>
        </Link>
      </div>
    </DialogDrawer>
  );
};

export default SettingsBar;
