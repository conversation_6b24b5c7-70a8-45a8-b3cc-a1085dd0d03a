---
description: whenever the user mentiones thinking in his prompt or whenever deep thinking is requested or the user mentiones 'think deeply'
globs: 
alwaysApply: false
---
<thinking>1. Deconstruct the Request & Define GoalsFirst, I need to break down the user's request into its core components. I will not make assumptions.Primary Objective: What is the main goal the user wants to achieve? What is the "definition of done" for this task?Inputs: What data, context, or existing code am I being given?Outputs: What is the expected final artifact? (e.g., a function, a full component, a class, a refactored file, a shell script).Implicit Needs: What is the user not saying? Are there underlying needs like performance, scalability, or future maintainability that I should address?2. Identify Constraints & Technical RequirementsNow, I'll identify all technical and non-technical constraints that will shape the solution.Language/Framework: What is the specific language, framework, and version? (e.g., Python 3.11, React 18 with TypeScript, .NET 8).Architectural Style: Does the existing codebase follow a specific pattern (e.g., MVC, Layered, Microservices)? My solution must fit seamlessly into this architecture.Code Style & Conventions: Are there established style guides (e.g., PEP 8, Google C++ Style) or linting rules I must adhere to?Dependencies: What libraries or packages are available or forbidden? I must work within the existing dependency tree.Non-Functional Requirements: Are there any performance, security, or accessibility requirements? I must design for these from the start.3. High-Level Plan & Architectural ApproachBefore implementation, I will outline a high-level strategy. This is my architectural blueprint.Chosen Pattern: What design pattern is most appropriate for this problem? (e.g., Strategy for interchangeable algorithms, Factory for object creation, Observer for state notifications). I will justify my choice.Separation of Concerns: How will I break the problem down? I will plan for clear boundaries between UI, business logic, data access, and services.Data Flow: How will data move through the system? I will map the flow from input to output.State Management: Where will the state live? Will it be local, lifted, or managed in a global store/context? I'll choose the simplest effective solution.4. Component & Data Structure DesignWith a plan in place, I'll design the specific code structures.Data Models: What interfaces, types, or classes are needed to represent the data? I will define these first.Function/Method Signatures: What are the "verbs" of my solution? I will define the function names, parameters (with types), and return values.--   Component Breakdown: (For UI tasks) What are the individual components? I'll define their props, state, and responsibilities.5. Risk Analysis & Edge Case IdentificationA professional anticipates problems. I will identify potential failure points before they happen.Error States: What can go wrong? (e.g., network failure, invalid input, null/undefined data, database errors). I will plan for robust error handling for each case.Edge Cases: What are the non-obvious inputs? (e.g., empty arrays, zero values, strings with special characters, very large numbers). My code must handle these gracefully.Security Vulnerabilities: How could this code be exploited? I will check for common vulnerabilities like Injection, XSS, or insecure direct object references and plan mitigations.Performance Bottlenecks: Could this code be slow? I will identify any potentially expensive operations (e.g., nested loops, large data transformations) and consider optimizations like memoization if necessary, but I will prioritize readability over premature optimization.6. Final Implementation Plan (Step-by-Step)Finally, I will synthesize all the above points into a concrete, step-by-step plan for generating the code.Scaffolding: Create the basic file/class/function structure.Type/Interface Definitions: Write the data models and type definitions first.Core Logic Implementation: Implement the primary business logic based on the design.Error & Edge Case Handling: Wrap the core logic with robust error and edge case handling.Integration: Connect all the pieces together.Documentation & Comments: Add high-quality docstrings and insightful inline comments for non-obvious logic.Final Review: Perform a final self-correction pass to ensure the code meets all requirements, is clean, and adheres to all rules.I will now proceed with the implementation based on this detailed plan.</thinking>