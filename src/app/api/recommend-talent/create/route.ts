import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { ExternalCreateRecommendTalentService } from '@/services/external/recommend-talent';

/**
 * Route to create talent recommendation
 */
export async function POST(request: NextRequest) {
  try {
    // Getting request params
    const { recommendTalentData } = await request.json();

    // call API to create talent recommendation
    const response =
      await ExternalCreateRecommendTalentService(recommendTalentData);

    // Return the response
    return NextResponse.json({
      success: response.data.success,
      message: response.data.message,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error);
  }
}
