import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { motion } from 'motion/react';
import FilterIcon from '@/ui/icons/filter';
import Media from '@/ui/media';
import BackOverly from '@/ui/back-overly';
import Button from '@/ui/form/button';
import Typography from '@/ui/typography';
import useToggleable from '@/hooks/helpers/useToggle';
import useCoursesFiltersStore from '@/stores/learning/courses/filters/advance';
import useSearchCoursesStore from '@/stores/learning/courses/search';
import useSearchCourses from '@/hooks/helpers/search/course/useSearchCourses';
import Head from './head';
import Durations from './durations';
import Types from './types';

// Styles
import styles from './style.module.scss';
import dictionary from '@/dictionaries';
import IFilter from '@/types/domain/learning/course/filter';
import Curators from './curators';

const Filters: FC = () => {
  // State variable to store current filters to be reset if the popup is closed without applying new filters
  const [filtersSnapshot, setFiltersSnapshot] = useState<IFilter | undefined>();

  // Getting methods from custom hook to handle search courses
  const { updateQueryParams } = useSearchCourses();

  // Popup state
  const popup = useToggleable(false);

  // Getting the fetch method for filters
  const onFetch = useCoursesFiltersStore((x) => x.fetch);

  // Getting the filters
  const filters = useSearchCoursesStore((x) => x.filters);
  const setFilters = useSearchCoursesStore((x) => x.setFilters);
  const setFilter = useSearchCoursesStore((x) => x.setFilter);

  /**
   * Effect to Fetch filters
   */
  useEffect(() => {
    if (popup.visible) {
      // Fetching the filters
      onFetch();
    }
  }, [popup, onFetch]);

  /**
   * Method to handle apply filter
   */
  const onApplyFilter = useCallback(() => {
    setFilter({ key: 'page', value: 1 });

    // Updating query params
    updateQueryParams({
      search: filters.keyword,
      category: filters.categoryID,
      learningType: filters.learningType,
      creator: filters.creator,
      duration: filters.duration,
    });

    // Closing the popup
    popup.close();

    // Clear filters snapshot
    setFiltersSnapshot(undefined);
  }, [updateQueryParams, filters, popup, setFilter]);

  const handleOpen = useCallback(() => {
    setFiltersSnapshot(filters);
    popup.open();
  }, [setFiltersSnapshot, filters, popup]);

  const handleClose = useCallback(
    (isReset?: boolean) => {
      if (filtersSnapshot && !isReset) {
        setFilters(filtersSnapshot);
      }
      popup.close();
      setFiltersSnapshot(undefined);
    },
    [filtersSnapshot, setFilters, popup],
  );

  // flag to enable filter button
  const enableFilterButton = useMemo(() => {
    if (
      filters?.learningType ||
      filters?.creator?.length ||
      filters?.product ||
      filters?.duration ||
      filtersSnapshot?.learningType ||
      filtersSnapshot?.creator?.length ||
      filtersSnapshot?.duration
    ) {
      return true;
    }
    return false;
  }, [filters, filtersSnapshot]);

  // Return JSX
  return (
    <>
      <button className={styles.filterBtn} onClick={handleOpen}>
        <i>
          <FilterIcon />
        </i>
      </button>
      {popup.visible && (
        <motion.div
          initial={{ opacity: 0, zIndex: 9999 }}
          animate={{ opacity: 1 }}
          className={styles.filterPopup}
        >
          <Media mobile={false}>
            <BackOverly onClick={() => handleClose(false)} />
          </Media>
          <div className={styles.wrapper}>
            <Head
              onClose={() => handleClose(true)}
              resetEnabled={enableFilterButton}
            />
            <div className={styles.filterList}>
              <Types />
              <Curators />
              <Durations />
            </div>
            <div className={styles.applyFilterButton}>
              <Button
                type="button"
                onClick={onApplyFilter}
                disabled={!enableFilterButton}
                color="black"
              >
                <Typography as="span" dictionary={dictionary.applyFilters} />
              </Button>
            </div>
          </div>
        </motion.div>
      )}
    </>
  );
};

// Export the component
export default Filters;
