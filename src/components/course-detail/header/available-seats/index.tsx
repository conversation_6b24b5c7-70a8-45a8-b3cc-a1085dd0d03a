import { FC } from 'react';
import { CalendarRounded } from '@/ui/icons';
import Typography from '@/ui/typography';
import styles from './style.module.scss';
import { format } from '@/utils/date';
import { useLocale, useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';

interface IAvailableSeats {
  totalAvailableSeat?: number;
  startDate?: string;
}

const AvailableSeats: FC<IAvailableSeats> = ({
  totalAvailableSeat,
  startDate,
}) => {
  const locale = useLocale();
  const t = useTranslations();

  return (
    <div className={styles.availableSeatsWrapper}>
      <div className={styles.availableSeatsHeader}>
        <span className={styles.availableSeatsIconWrapper}>
          <CalendarRounded />
        </span>
        <Typography className={styles.availableSeatsText}>
          {totalAvailableSeat} {t(dictionary.seatsAvailable)}{' '}
          {t(dictionary.startsFrom)}{' '}
          {format(startDate || '', locale, 'YYYY-MM-DD HH:mm:ss', 'DD MMMM')}
        </Typography>
      </div>
    </div>
  );
};

export default AvailableSeats;
