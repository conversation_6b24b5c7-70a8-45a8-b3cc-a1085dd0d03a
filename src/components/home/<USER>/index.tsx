import Typography from '@/ui/typography';
import { QuoteIcon } from '@/ui/icons';

// Styles
import styles from './style.module.scss';
import Paper from '@/ui/paper';
import dictionaries from '@/dictionaries';
import { useTranslations } from 'next-intl';

const QuoteOfTheDay = () => {
  const t = useTranslations();
  // Return JSx
  return (
    <Paper>
      <div className={styles.quote}>
        <i className={styles.icon}>
          <QuoteIcon />
        </i>
        <Typography as="h2" className={styles.title}>
          {t(dictionaries.quoteText)}
        </Typography>
        <div className={styles.name}>
          <Typography as="span">{t(dictionaries.quoteBy)}</Typography>
        </div>
      </div>
    </Paper>
  );
};

export default QuoteOfTheDay;
