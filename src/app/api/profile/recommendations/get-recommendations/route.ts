import { NextRequest, NextResponse } from 'next/server';
import { ExternalGetRecommendationsService } from '@/services/external/profile/recommendations/get-recommendations';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';

// To handle a GET request to /api
export async function GET(request: NextRequest) {
  try {
    const params = request.nextUrl.searchParams;
    // Getting request params
    const askUserId = params.get('askUserId') || '';
    const giverUserId = params.get('giverUserId') || '';
    const statusRecommend = params.get('statusRecommend') || '';

    // Fetching user profile
    const response = await ExternalGetRecommendationsService(
      askUserId,
      giverUserId,
      statusRecommend,
    );
    // Return the response
    return NextResponse.json({
      user: response?.data,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error as AxiosError);
  }
}
