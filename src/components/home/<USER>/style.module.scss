@use 'mixins' as *;

div.announcementsPaper {
  @include forMarginPadding(padding, size(25), size(0), size(0), size(0));

  div[class*='head'] {
    margin-bottom: size(14);
    @include forMarginPadding(padding, size(0), size(25), size(0), size(25));
  }
}

div.indentLoading {
  @include forMarginPadding(margin, size(24), size(24), size(24), size(24));
  min-height: size(480);
  width: calc(100% - size(48));
}

.announcementItem {
  &:first-child {
    > div[class*='item'] {
      &::before {
        display: none;
      }
    }
  }

  &:last-child {
    > div[class*='item'] {
      &::after {
        display: none;
      }
    }
  }
}
