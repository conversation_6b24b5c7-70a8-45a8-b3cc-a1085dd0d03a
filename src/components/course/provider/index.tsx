import { FC, ReactNode, useMemo } from 'react';
import ICourse from '@/types/domain/learning/course';
import Typography from '@/ui/typography';
import { useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';

interface IProvider {
  course: ICourse;
  className?: string;
  children?: ReactNode;
}

// React component to handle course link
const Provider: FC<IProvider> = (props) => {
  // Deconstructing props
  const { className, course } = props;

  const t = useTranslations();
  // Getting course title
  const provider = useMemo(() => {
    return course?.providername || t(dictionary.govAcademy);
  }, [course?.providername, t]);

  // Return JSX
  return (
    <Typography as="p" className={className}>
      {provider}
    </Typography>
  );
};

// Exporting course component
export default Provider;
