$(document).ready(function(){

	if ($('.slider__wrapper').length) {
		$('.slider__wrapper .slider').slick({
			slidesToShow:1,
			arrows:true,
			adaptiveHeight:true
		})
		$('.slider__wrapper .prev__btn').on('click' ,function(e){
			e.preventDefault();
			$('.slider__wrapper .slick-prev').click();
		});
		$('.slider__wrapper .next__btn').on('click' ,function(e){
			e.preventDefault();
			$('.slider__wrapper .slick-next').click();
		});
	}

	$('.faq__wrapper .faq__element>.head').on('click' ,function(e){
		e.preventDefault();
		if ($(this).closest('.faq__element').hasClass('opened')) {
			$(this).closest(".faq__element").removeClass('opened');
			$(this).closest('.faq__element').find('.content').slideUp(300);
		} else {
			$('.faq__element').removeClass('opened');
			$('.faq__element  .content').slideUp(300);
			$(this).closest('.faq__element').addClass('opened');
			$(this).closest('.faq__element').find('.content').slideDown(300);
		}
	});

	$('.float__menu ul li a').on('click' ,function(e){
		e.preventDefault();
		let curr = $(this).attr("data-tab");
		 $('html').animate({ 
    	    scrollTop: $("." + curr).offset().top - 60
        }, 1200 
        );
	});

	$('.float__menu--sub ul li a').on('click' ,function(e){
		e.preventDefault();
		let curr = $(this).attr("data-tab");
		 $('html').animate({ 
    	    scrollTop: $("." + curr).offset().top - 60
        }, 1200 
        );
	});

	$(window).on('scroll' ,function(e){
		if ($(window).scrollTop() > $('.program__vision').offset().top) {
			$('.float__menu').addClass("visible");
		} else {
			$('.float__menu').removeClass('visible');
		}

		if ($(window).scrollTop() > $('.objectives').offset().top - 200 && $(window).scrollTop() < ($('.objectives').offset().top + $('.objectives').outerHeight())) {
			$('.float__menu .current').removeClass('current');
			$('.float__menu li:nth-child(1)>a').addClass('current');
		} else {
			$('.float__menu li:nth-child(1)>a').removeClass('current')			
		}

		if ($(window).scrollTop() > $('.learn__info').offset().top - 200 && $(window).scrollTop() < ($('.learn__info').offset().top + $('.learn__info').outerHeight())) {
			$('.float__menu .current').removeClass('current');
			$('.float__menu li:nth-child(2)>a').addClass('current');
		} else {
			$('.float__menu li:nth-child(2)>a').removeClass('current')			
		}

		if ($(window).scrollTop() > $('.program__benefits').offset().top - 200 && $(window).scrollTop() < ($('.program__benefits').offset().top + $('.program__benefits').outerHeight())) {
			$('.float__menu .current').removeClass('current');
			$('.float__menu li:nth-child(3)>a').addClass('current');
		} else {
			$('.float__menu li:nth-child(3)>a').removeClass('current')			
		}

		if ($(window).scrollTop() > $('.who__can--apply').offset().top - 200 && $(window).scrollTop() < ($('.who__can--apply').offset().top + $('.who__can--apply').outerHeight())) {
			$('.float__menu .current').removeClass('current');
			$('.float__menu li:nth-child(4)>a').addClass('current');
		} else {
			$('.float__menu li:nth-child(4)>a').removeClass('current')			
		}

		if ($(window).scrollTop() > $('.process__wrapper').offset().top - 200 && $(window).scrollTop() < ($('.process__wrapper').offset().top + $('.process__wrapper').outerHeight())) {
			$('.float__menu .current').removeClass('current');
			$('.float__menu li:nth-child(5)>a').addClass('current');
		} else {
			$('.float__menu li:nth-child(5)>a').removeClass('current')			
		}

		if ($(window).scrollTop() > $('.contact__us').offset().top - 200 && $(window).scrollTop() < ($('.contact__us').offset().top + $('.contact__us').outerHeight())) {
			$('.float__menu .current').removeClass('current');
			$('.float__menu li:nth-child(6)>a').addClass('current');
		} else {
			$('.float__menu li:nth-child(6)>a').removeClass('current')			
		}
	});

	if ($('.load__animation').length) {
		let interval = setInterval(function(){
		$(window).scrollTop(0);
	},4);
	setTimeout(function(){
		$('.load__animation').addClass("loaded");
		setTimeout(function(){
			$('.load__animation .bottom').addClass("loaded");
		}, 400);
	}, 500);
	setTimeout(function(){
		$(window).on('scroll' ,function(e){
			if (!$('.load__animation').hasClass('hidden')) {
				$('.load__animation').addClass('hidden');
				setTimeout(function(){
					$('.load__animation').fadeOut(400);
					$('.hero__section').css('opacity' , "1");
					clearInterval(interval);
				}, 200);
			} 
		});
	},1000);
	}
	$('.hero__section').css('opacity' , "1");
});