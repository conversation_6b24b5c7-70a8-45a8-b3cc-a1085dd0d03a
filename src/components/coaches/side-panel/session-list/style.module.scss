@use 'mixins' as *;

.popup {
  position: fixed;
  top: 0;
  @include rightToLeft(0);
  width: 100%;
  height: 100vh;
  z-index: 11;
}

.inner {
  width: size(580);
  height: 100vh;
  position: relative;
  z-index: 9;
  background: var(--white);
  margin-inline-start: auto;
}

.body {
  width: 100%;
  @include forMarginPadding(padding, size(24), size(24), size(24), size(24));
  overflow-y: auto;
  height: 90vh;
}

.sectionSessions {
  @include forMarginPadding(margin, size(0), size(0), size(30), size(0));
  @include forMarginPadding(padding, size(30), size(40), size(30), size(40));
  h4 {
    @include forMarginPadding(margin, size(0), size(0), size(40), size(0));
  }
  &:first-child {
    @include forMarginPadding(padding, size(0), size(0), size(24), size(0));
    border-bottom: solid 1px var(--grey-400);
  }
  &:only-child {
    border-bottom: none;
  }
}

.containerSession {
  @include forMarginPadding(padding, size(0), size(0), size(30), size(0));
  @include for-all-phone() {
    @include forMarginPadding(padding, size(0), size(24), size(30), size(0));
  }
  li {
    width: 50%;
  }
}

.buttonClose {
  @include forMarginPadding(padding, size(9), size(9), size(9), size(9));
  background-color: transparent;
  border: 1px solid var(--grey-900);
  border-radius: 50%;
  cursor: pointer;
  height: size(30);
  width: size(30);
}

.buttonCloseContainer {
  @include forMarginPadding(padding, size(0), size(40), size(36), size(0));
  text-align: right;

  @include for-all-phone() {
    @include forMarginPadding(padding, size(0), size(24), size(36), size(0));
  }

  @include rtl {
    text-align: left;
  }

  svg {
    display: block;
    fill: var(--grey-900);
  }
}
