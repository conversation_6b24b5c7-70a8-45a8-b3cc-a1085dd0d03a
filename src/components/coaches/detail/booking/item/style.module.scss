@use 'mixins' as *;

.item {
  border: solid 1px var(--grey-400);
  @include forMarginPadding(padding, size(12), size(16), size(12), size(16));
  background: var(--blue-500);
  @include borderRadius(var(--radius));
  color: var(--grey-800);
  cursor: pointer;

  + .item {
    @include forMarginPadding(margin, size(10), size(0), size(0), size(0));
  }

  &.active {
    background: var(--grey-800);
    color: var(--white);

    svg {
      fill: var(--white);
    }
  }
}

@include for-dark-theme() {
  .item {
    color: var(--grey-800);
    background: var(--white);
    &.active {
      background: var(--cardBg);
      color: var(--grey-800);
      svg {
        fill: var(--grey-800);
      }
    }
  }
  .location {
    i {
      fill: var(--grey-800);
    }
  }
}

.head {
  display: flex;
  justify-content: space-between;

  span {
    font-size: size(16);
    line-height: 1;
    font-weight: bold;
  }
}

.time {
  text-transform: lowercase;
}

.location {
  display: flex;
  align-items: center;

  @include forMarginPadding(padding, size(10), size(0), size(0), size(0));

  i {
    width: size(16);
    height: size(16);
    @include forMarginPadding(margin, size(0), size(10), size(0), size(0));
    display: flex;
    svg {
      vertical-align: top;
    }
  }

  span {
    font-size: size(16);
    position: relative;
    top: 2px;
  }
}
