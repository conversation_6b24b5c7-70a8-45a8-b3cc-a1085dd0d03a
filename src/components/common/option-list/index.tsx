'use client';

import { useState } from 'react';
import Typography from '@/ui/typography';
import { RightArrow } from '@/ui/icons';
import dictionary from '@/dictionaries';
import styles from './style.module.scss';
import { useLocale } from 'next-intl';
import LeftArrow from '@/ui/icons/left-arrow';
import { StaticImageData } from 'next/image';
import Image from '@/ui/image';
import { Link } from '@/i18n/routing';

interface ImageProps {
  src: StaticImageData;
  alt: string;
}

interface OptionListItem {
  text: string;
  leftIcon?: JSX.Element;
  leftImage?: ImageProps;
  rightIcon?: JSX.Element;
  rightImage?: ImageProps;
  onClick?: () => void;
  removeDivider?: boolean;
  link?: string;
  linkTarget?: string;
  toggleSwitch?: JSX.Element;
  isRoute?: boolean;
}

export interface OptionListProps {
  options: OptionListItem[];
  showOptions?: number;
}

const OptionList: React.FC<OptionListProps> = ({ options, showOptions }) => {
  const [viewAll, setViewAll] = useState<boolean>(false);
  const locale = useLocale();
  return (
    <div className={styles.optionListContainer}>
      <ul className={styles.optionList}>
        {options.map((option, index) => {
          if (
            (option.link &&
              (!showOptions || (showOptions && index < showOptions))) ||
            viewAll
          ) {
            return (
              <div key={`option-list-item-${index}`}>
                {option.isRoute ? (
                  <Link
                    href={option.link as string}
                    target={option.linkTarget ? option.linkTarget : ''}
                    key={`option-list-item-${index}`}
                  >
                    <li
                      className={
                        option.removeDivider
                          ? `${styles.optionListItem} ${styles.removeDivider}`
                          : styles.optionListItem
                      }
                      onClick={option.onClick}
                    >
                      {option.leftImage && (
                        <div className={styles.image}>
                          <Image
                            src={option.leftImage.src}
                            alt={option.leftImage.alt}
                          />
                        </div>
                      )}
                      <div>
                        {option.leftIcon && (
                          <div className={styles.optionContentContainer}>
                            <i className={styles.leftIcon}>{option.leftIcon}</i>
                          </div>
                        )}
                        <Typography as="span">{option.text}</Typography>
                      </div>
                      {option.rightImage && (
                        <div className={styles.image}>
                          <Image
                            src={option.rightImage.src}
                            alt={option.rightImage.alt}
                          />
                        </div>
                      )}
                      {option.rightIcon && (
                        <div className={styles.optionContentContainer}>
                          <i className={styles.rightIcon}>{option.rightIcon}</i>
                        </div>
                      )}
                    </li>
                  </Link>
                ) : (
                  <a
                    href={option.link as string}
                    target={option.linkTarget ? option.linkTarget : ''}
                    key={`option-list-item-${index}`}
                  >
                    <li
                      className={
                        option.removeDivider
                          ? `${styles.optionListItem} ${styles.removeDivider}`
                          : styles.optionListItem
                      }
                      onClick={option.onClick}
                    >
                      {option.leftImage && (
                        <div className={styles.image}>
                          <Image
                            src={option.leftImage.src}
                            alt={option.leftImage.alt}
                          />
                        </div>
                      )}
                      <div>
                        {option.leftIcon && (
                          <div className={styles.optionContentContainer}>
                            <i className={styles.leftIcon}>{option.leftIcon}</i>
                          </div>
                        )}
                        <Typography as="span">{option.text}</Typography>
                      </div>
                      {option.rightImage && (
                        <div className={styles.image}>
                          <Image
                            src={option.rightImage.src}
                            alt={option.rightImage.alt}
                          />
                        </div>
                      )}
                      {option.rightIcon && (
                        <div className={styles.optionContentContainer}>
                          <i className={styles.rightIcon}>{option.rightIcon}</i>
                        </div>
                      )}
                    </li>
                  </a>
                )}
              </div>
            );
          }
          if (!showOptions || (showOptions && index < showOptions) || viewAll) {
            return (
              <li
                className={
                  option.removeDivider
                    ? `${styles.optionListItem} ${styles.removeDivider}`
                    : styles.optionListItem
                }
                onClick={option.onClick}
                key={`option-list-item-${index}`}
              >
                <div>
                  {option.leftImage && (
                    <div className={styles.image}>
                      <Image
                        src={option.leftImage.src}
                        alt={option.leftImage.alt}
                      />
                    </div>
                  )}
                  {option.leftIcon && (
                    <div className={styles.optionContentContainer}>
                      <i className={styles.leftIcon}>{option.leftIcon}</i>
                    </div>
                  )}
                  <Typography as="span">{option.text}</Typography>
                </div>
                {option.rightImage && (
                  <div className={styles.image}>
                    <Image
                      src={option.rightImage.src}
                      alt={option.rightImage.alt}
                    />
                  </div>
                )}
                {option.rightIcon && (
                  <div className={styles.optionContentContainer}>
                    <i className={styles.rightIcon}>{option.rightIcon}</i>
                  </div>
                )}
                {option.toggleSwitch && <div>{option.toggleSwitch}</div>}
              </li>
            );
          }
        })}
      </ul>
      {showOptions && (
        <div className={styles.viewAllContainer}>
          <span>
            <Typography
              as="span"
              onClick={() => {
                setViewAll(!viewAll);
              }}
            >
              {viewAll ? (
                <Typography as="span" dictionary={dictionary.showLess}>
                  Show less
                </Typography>
              ) : (
                <Typography as="span" dictionary={dictionary.viewAll}>
                  View all
                </Typography>
              )}
              <i className={styles.icon}>
                {locale == 'en' ? <RightArrow /> : <LeftArrow />}
              </i>
            </Typography>
          </span>
        </div>
      )}
    </div>
  );
};

export default OptionList;
