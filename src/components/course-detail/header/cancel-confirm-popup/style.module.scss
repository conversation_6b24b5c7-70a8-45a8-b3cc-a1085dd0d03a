@use 'mixins' as *;

.confirmationDialog {
  display: none;
}

.confirmationDialogOpened {
  border: 0;
  border-radius: size(24);
  display: flex;
  flex-direction: column;
  margin: auto;
  min-height: size(262);
  @include forMarginPadding(padding, size(40), size(24), size(32), size(24));
  width: size(430);
}

.confirmationDialogTitle {
  font-size: size(28);
  font-weight: 700;
}

.confirmationDialogMessage {
  font-size: size(20);
}

.confirmationDialogActions {
  display: flex;
  gap: size(8);
  margin-top: auto;
}

.confirmationDialogCancel {
  background-color: transparent;
  color: var(--grey-900);
}

@include hover() {
  .confirmationDialogCancel:hover {
    background-color: var(--grey-900);
    color: var(--white);
  }
}
