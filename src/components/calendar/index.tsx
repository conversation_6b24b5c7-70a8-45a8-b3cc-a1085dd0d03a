// components/calendar/index.tsx
import { FC, useState, useCallback, useEffect } from 'react';
import styles from './style.module.scss';
import SmallCalendar from './calendar-sidebar/small-calendar';
import AgendaItems from './calendar-sidebar/agenda-items';

import 'moment/locale/ar';
import momentTimeZone from 'moment-timezone';
import IFormatedEvent from '@/types/domain/calendar/formated-event';
import ICourseEvent from '@/types/domain/calendar/course-event';
import useUpcomingEventsStore from '@/stores/calendar';

import dictionary from '@/dictionaries';
import IEventTypes from '@/types/domain/calendar/event-types';
import Spinner from '@/ui/spinner';

import { useTranslations } from 'next-intl';
import Media from '@/ui/media';
import CalendarMain from './calendar-main';
import Paper from '@/ui/paper';
import CalendarTabs from './calendar-tabs';
import DialogDrawer from '@/ui/dialog-drawer';
import CourseDetails from '@/components/calendar/calendar-main/course-details';

type TimePeriod = 'daily' | 'weekly' | 'monthly';

interface ICalendar {
  text?: string;
}

const Calendar: FC<ICalendar> = () => {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth());
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [isMyCalendar, setIsMyCalendar] = useState<boolean>(true); // State to control "My Calendar" vs "Academic Calendar"
  const [selectedTimePeriod, setSelectedTimePeriod] =
    useState<TimePeriod>('daily');

  const t = useTranslations();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [clickedEvent, setClickedEvent] = useState<IFormatedEvent | null>(null);
  const [courseId, setCourseId] = useState<string>('');
  const onFetch = useUpcomingEventsStore((x) => x.fetch);
  const isFetching = useUpcomingEventsStore((x) => x.isFetching);
  const allEvents = useUpcomingEventsStore((x) => x.list as IEventTypes);
  const handleShowEventDetails = useCallback((event: IFormatedEvent) => {
    setIsDrawerOpen(true);
    setCourseId(event.id as string);
    setClickedEvent(event);
  }, []);

  // Handler for closing the DialogDrawer
  const handleCloseDrawer = useCallback(() => {
    setIsDrawerOpen(false);
  }, []);
  const mergeBookedProperty = (
    firstArray: ICourseEvent[],
    secondArray: ICourseEvent[],
  ) => {
    if (firstArray && secondArray) {
      const bookedIds = new Set(
        firstArray
          .filter((item) => item.status === 'booked')
          .map((item) => item.courseid),
      );

      return secondArray.map((item) => {
        if (bookedIds.has(item.courseid)) {
          const bookedItem = firstArray.find(
            (b) => b.courseid === item.courseid,
          );
          return { ...item, status: bookedItem?.status };
        }
        return item;
      });
    }
    return [];
  };

  useEffect(() => {
    onFetch(selectedYear.toString(), (selectedMonth + 1).toString());
  }, [onFetch, selectedYear, selectedMonth]);

  useEffect(() => {
    setSelectedMonth(selectedDate.getMonth());
    setSelectedYear(selectedDate.getFullYear());
  }, [selectedDate]);

  const events: ICourseEvent[] = allEvents?.records;
  const academicEvents: ICourseEvent[] = mergeBookedProperty(
    events,
    allEvents?.instr_led_course_events,
  );
  const eventsPerView = isMyCalendar ? events : academicEvents;

  const formatLocalTime = (dateString: string | undefined) => {
    const dateTime = dateString?.split('T');
    const date = dateTime?.[0];
    const time = dateTime?.[1]?.split('.')?.[0];
    const dubaiLocalDate = momentTimeZone.tz(`${date}T${time}`, 'Asia/Dubai');
    const utcTime = dubaiLocalDate.clone().tz('UTC');
    const systemDefaultTz = utcTime.clone().tz(momentTimeZone.tz.guess());

    return systemDefaultTz;
  };

  const monthlyEvents: IFormatedEvent[] = eventsPerView?.map(
    (event: ICourseEvent) => {
      const startDate = new Date(event.startdatetime as string);
      const endDate = new Date(event.enddatetime as string);
      const startTimezone = formatLocalTime(event.startdatetime);
      const endTimezone = formatLocalTime(event.enddatetime);
      const formattedStartTime = startTimezone
        .locale('en')
        .format('h:mm a')
        .replace('am', t(dictionary.am))
        .replace('pm', t(dictionary.pm));
      const formattedEndTime = endTimezone
        .locale('en')
        .format('h:mm a')
        .replace('am', t(dictionary.pm))
        .replace('pm', t(dictionary.pm));

      const hour = startTimezone.hour();

      return {
        id: event.courseid ? String(event.courseid) : '0',
        date: startDate,
        startDateTime: event.startdatetime,
        endDateTime: event.enddatetime,
        startTime: formattedStartTime,
        endTime: formattedEndTime,
        text: event.coursename,
        howLong: endDate.getHours() - startDate.getHours(),
        topPosition: hour,
        image: event.imageurl,
        booked: event.status === 'booked',
        coursename: event.coursename,
        eventlocation: event.eventlocation,
        status: event.status,
        eventname: event.eventname,
        courseType: event.coursetype ?? '',
        courseDays: event.courseday ?? '',
        courseEnrollmentType: event.course_enrollmenttype ?? '',
      };
    },
  );

  const handleDateChange = useCallback((date: Date) => {
    setSelectedDate(date);
  }, []);

  const handleTimePeriodChange = useCallback((newPeriod: TimePeriod) => {
    setSelectedTimePeriod(newPeriod);
  }, []);

  const handleToggleCalendar = useCallback((isMyCal: boolean) => {
    setIsMyCalendar(isMyCal);
  }, []);

  return (
    <>
      <Media mobile={true} tablet={false} desktop={false}></Media>
      <Media mobile={true} tablet={true} desktop={true}>
        <CalendarTabs
          isMyCalendar={isMyCalendar}
          onToggleCalendar={handleToggleCalendar}
        />
        <div className={styles.calendarWrapper}>
          <div className={styles.calendarSidebar}>
            <Paper>
              <SmallCalendar
                isMyCalendarTab={isMyCalendar}
                events={monthlyEvents}
                selectedTimePeriod={selectedTimePeriod}
                onTimePeriodChange={handleTimePeriodChange}
                selectedDate={selectedDate}
                onDateSelect={handleDateChange}
              />
            </Paper>
            <Paper>
              {isFetching ? (
                <Spinner />
              ) : (
                <AgendaItems
                  events={monthlyEvents}
                  handleShowEventDetails={handleShowEventDetails}
                />
              )}
            </Paper>
          </div>
          <div className={styles.calendarMainWrapper}>
            <Paper>
              <CalendarMain
                isMyCalendarTab={isMyCalendar}
                events={monthlyEvents || []}
                selectedTimePeriod={selectedTimePeriod}
                onTimePeriodChange={handleTimePeriodChange}
                selectedDate={selectedDate}
                onDateSelect={handleDateChange}
                isFetching={isFetching || events === undefined}
                isMyCalendar={isMyCalendar}
                handleShowEventDetails={handleShowEventDetails}
              />
            </Paper>
            {isDrawerOpen && clickedEvent && (
              <DialogDrawer isOpened={isDrawerOpen} onClose={handleCloseDrawer}>
                <CourseDetails
                  courseId={courseId}
                  eventDetails={clickedEvent}
                  handleCloseDrawer={handleCloseDrawer}
                />
              </DialogDrawer>
            )}
          </div>
        </div>
      </Media>
    </>
  );
};

export default Calendar;
