@use 'mixins' as *;

.filterHeading {
  justify-content: space-between;
}

.resetBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  color: var(--grey-900);
  font-size: size(16);
  border: none;
  cursor: pointer;

  i {
    width: size(20);
    height: size(20);
    display: block;
    @include forMarginPadding(margin, size(0), size(10), size(0), size(0));

    svg {
      fill: var(--grey-900);
      fill-rule: evenodd;
      width: 100%;
    }
  }

  span {
    display: inline;
    padding-bottom: 0;
    transition: all 0.5s linear;
    background: linear-gradient(
      to bottom,
      var(--grey-900) 0%,
      var(--grey-900) 98%
    );
    background-size: 0 1px;
    background-repeat: no-repeat;
    background-position: left 100%;

    @include rtl {
      background-position: right 100%;
    }
  }
}

@include hover {
  .resetBtn {
    &:hover {
      span {
        background-size: 100% 1px;
      }
    }
  }
}

.sep {
  opacity: 0.1;
  border-bottom: solid 1px var(--grey-900);
  height: 1px;
  width: 100%;
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));
}

.genderWrapper {
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));

  h3 {
    font-size: size(22);
    font-weight: 400;
    line-height: 1.2;
    color: var(--grey-800);
  }

  .gender {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: size(15);

    label {
      font-weight: 400;
      font-size: size(16);
      color: var(--grey-800);
    }
  }
}

.applyFilterButton {
  position: sticky;
  bottom: size(24);
  width: 100%;
  @include forMarginPadding(margin, auto, size(0), size(0), size(0));
}

.customDates {
  display: flex;
  gap: size(15);
}
