import { FC, useMemo } from 'react';
import styles from './style.module.scss';
import Image from 'next/image';
import timeslot from '@public/images/timeslote.png';
import Typography from '@/ui/typography';
import moment from 'moment';
import 'moment/locale/ar';
import { formatLocalTime } from '@/utils/time';
import useCoachBookingStore from '@/stores/learning/coaches/booking';
import { useLocale } from 'next-intl';
import dictionary from '@/dictionaries';

const SelectedInfo: FC = () => {
  // Getting selected slot
  const selected = useCoachBookingStore((x) => x.selected);
  const locale = useLocale();
  // Memoizing the start
  const start = useMemo(() => {
    // If start is available
    if (selected?.time_from) {
      return formatLocalTime(selected?.time_from);
    }
    // Returning null
    return null;
  }, [selected?.time_from]);

  // Memoizing the end
  const end = useMemo(() => {
    // If start is available
    if (selected?.time_to) {
      return formatLocalTime(selected?.time_to);
    }
    // Returning null
    return null;
  }, [selected?.time_to]);

  return (
    <div className={styles.selectedDetailsContainer}>
      <div className={styles.selectedImage}>
        <Image
          src={timeslot}
          width={40}
          height={40}
          className="img-fluid"
          alt=""
        />
      </div>
      <div className={styles.selectedInfo}>
        <Typography as="span" dictionary={dictionary.selectedSlot} />
        <Typography as="h4">
          {moment(selected.slot_date, 'ddd MMM DD YYYY', 'en')
            .locale(locale === 'ar' ? 'ar' : 'en')
            .format('ddd, DD MMM YYYY')}
        </Typography>
        {(start || end) && (
          <Typography as="h4" className={styles.time}>
            {start?.locale(locale === 'ar' ? 'ar' : 'en').format('h:mma')} -{' '}
            {end?.locale(locale === 'ar' ? 'ar' : 'en').format('h:mma')}
          </Typography>
        )}
      </div>
    </div>
  );
};

// Exporting component
export default SelectedInfo;
