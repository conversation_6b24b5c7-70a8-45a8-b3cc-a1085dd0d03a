import { FC } from 'react';
import ICourse from '@/types/domain/learning/course';
import Alpha from './variants/alpha';
import Beta from './variants/beta';
import Gama from './variants/gama';

interface ICourseProps {
  course: ICourse;
  variant?: 'alpha' | 'beta' | 'gama' | 'delta' | 'epsilon';
  inProgress?: boolean;
}

/**
 * React component to handle course
 */
const Course: FC<ICourseProps> = (props) => {
  // Deconstructing props
  const { course, variant = 'alpha', inProgress } = props;

  // Switching variant
  switch (variant) {
    // Alfa variant
    case 'alpha':
      return <Alpha course={course} />;
    // Beta variant
    case 'beta':
      return <Beta course={course} inProgress={inProgress} />;
    // Gama variant
    case 'gama':
      return <Gama course={course} inProgress={inProgress} />;
  }

  // Return null by default
  return null;
};

// Exporting course component
export default Course;
