import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraBookCoachSlotService } from '@/services/totara/coaches';

/**
 * Route to book the coach slot
 */
export async function POST(request: NextRequest) {
  try {
    // Getting request body
    const { slotId } = await request.json();
    // Booking Slot
    const response = await TotaraBookCoachSlotService(slotId);
    // Return the response
    return NextResponse.json(response?.data);
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
