import { NextResponse } from 'next/server';
import { getUserLocale, removeUserSession } from '@/utils/auth';
import { generateApplicationURL } from '@/utils/url';
import CONFIG from '@/config';
import { ExternalUserLogout } from '@/services/external/auth/login.service';

/**
 * Method to handle user logout
 */
export async function GET() {
  // Getting user locale
  const locale = await getUserLocale();

  // User login url
  const loginURL = await generateApplicationURL(
    CONFIG.routes.login.uaePass,
    locale,
  );

  // Trying to logout user from liferay
  try {
    // Loggin user out form liferay
    await ExternalUserLogout();
  } catch (error) {
    // Log error
    console.error('Error while logging out user', error);
  }

  // Deleting user session
  await removeUserSession();

  //redirect user to hompepage
  return NextResponse.redirect(loginURL);
}
