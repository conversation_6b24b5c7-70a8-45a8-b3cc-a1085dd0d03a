import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraUpdatePersonalListService } from '@/services/totara/personal-lists';

export async function GET(request: NextRequest) {
  const id = request.nextUrl.searchParams.get('id') || '';
  const name = request.nextUrl.searchParams.get('name') || '';

  try {
    const response = await TotaraUpdatePersonalListService(id, name);

    return NextResponse.json({
      addList: response?.data || null,
    });
  } catch (error) {
    return ErrorResponse(error);
  }
}
