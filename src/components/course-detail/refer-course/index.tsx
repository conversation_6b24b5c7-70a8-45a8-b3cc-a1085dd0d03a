import { FC, useCallback, useState, ChangeEvent, useEffect } from 'react';
import DialogDrawer from '@/ui/dialog-drawer';
import useReferCourseStore from '@/stores/learning/courses/refer-course';
import useAuthStore from '@/stores/auth';
import dictionary from '@/dictionaries';
import Typography from '@/ui/typography';
import InputField from '@/ui/form/input-field';
import InputTextarea from '@/ui/form/input-textarea';
import Button from '@/ui/form/button';
import ProfileSearchResults from './profile-search-results';
import useDebounce from '@/hooks/helpers/useDebounce';
import Spinner from '@/ui/spinner';
import { Close, SearchIcon } from '@/ui/icons';
import { isValidEmail } from '@/utils/validations/email';
import { useTranslations } from 'next-intl';
import Toast from '@/ui/toast';

import styles from './style.module.scss';

const ReferCourse: FC = () => {
  const [keyword, setKeyword] = useState<string>('');
  const [comments, setComments] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [isContentVisible, setIsContentVisible] = useState<boolean>(false);
  const searchTerm = useDebounce(keyword, 2000);
  const visible = useReferCourseStore((state) => state.visible);
  const setVisible = useReferCourseStore((state) => state.setVisible);
  const sendReferal = useReferCourseStore((state) => state.sendReferal);
  const isFetching = useReferCourseStore((state) => state.isFetching);
  const courseInfo = useReferCourseStore((state) => state.courseInfo);
  const search = useReferCourseStore((state) => state.search);
  const isSearching = useReferCourseStore((state) => state.isSearching);
  const results = useReferCourseStore((state) => state.results);
  const setResults = useReferCourseStore((state) => state.setResults);
  const setSelectedProfile = useReferCourseStore(
    (state) => state.setSelectedProfile,
  );
  const selectedProfile = useReferCourseStore((state) => state.selectedProfile);
  const referToast = useReferCourseStore((state) => state.referToast);
  const clearReferToast = useReferCourseStore((state) => state.clearReferToast);
  const profile = useAuthStore((x) => x.user);

  const t = useTranslations();

  const handleDialogClose = useCallback(() => {
    setVisible(false);
    setTimeout(function () {
      setKeyword('');
      setSelectedProfile({});
      setResults([]);
      setError('');
      setComments('');
      setIsContentVisible(false);
    }, 400); // Dialog animation transition time.
  }, [setResults, setSelectedProfile, setVisible]);

  const handleInputAction = useCallback(() => {
    if (keyword.length > 0) {
      setKeyword('');
      setSelectedProfile({});
      setResults([]);
      setError('');
    }
  }, [keyword.length, setKeyword, setSelectedProfile, setResults]);

  const onSelectProfile = useCallback(
    (
      id: number | undefined,
      name: string | undefined,
      picture: string | undefined,
    ) => {
      if (setSelectedProfile) {
        setSelectedProfile({ id: id, name: name, picture: picture });
      }
      setResults([]);
      setKeyword(name as string);
    },
    [setSelectedProfile, setResults],
  );

  const handleTextChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    setComments(e.currentTarget.value);
  }, []);

  const handleReferSubmit = async () => {
    const payload = {
      courseId: courseInfo?.courseId,
      courseName: courseInfo?.courseName,
      courseImage: courseInfo?.courseImage,
      referBy: profile?.id,
      comment: comments,
    };
    if (keyword.length && !selectedProfile?.id) {
      if (!isValidEmail(keyword)) {
        setError(t(dictionary.pleaseEnterAValidEmailAddress));
        return;
      }
      await sendReferal({ ...payload, email: keyword });
    } else if (selectedProfile?.id) {
      await sendReferal({ ...payload, referTo: selectedProfile?.id });
    }
    setTimeout(function () {
      setKeyword('');
      setSelectedProfile({});
      setResults([]);
      setError('');
      setComments('');
      setIsContentVisible(false);
    }, 400);
    setVisible(false);
  };

  useEffect(() => {
    if (searchTerm.length > 2 && !selectedProfile?.id) {
      search(searchTerm);
    }
  }, [searchTerm, search, selectedProfile?.id]);

  useEffect(() => {
    if (visible) {
      setIsContentVisible(true);
    }
  }, [visible, setIsContentVisible]);

  const clearMessages = useCallback(() => {
    clearReferToast();
  }, [clearReferToast]);

  // Return JSX
  return (
    <div className={styles.referWrapper}>
      <DialogDrawer isOpened={visible} onClose={handleDialogClose}>
        <div className="dialog-heading">
          <Typography as="h2" dictionary={dictionary.referACourse} />
        </div>
        {isContentVisible ? (
          <div className="dialog-content">
            <Typography
              as="p"
              className={styles.formTitle}
              dictionary={dictionary.youCanReferACourseToAColleague}
            />
            <div className={styles.referInput + ' position-relative'}>
              <div>
                <InputField
                  label={dictionary.whoDoYouWantToReferACourseTo}
                  placeholder={dictionary.enterNameOrEmailAddress}
                  ariaLabel="search"
                  endIcon=""
                  error={error}
                  value={keyword || ''}
                  required={true}
                  onChange={(e: ChangeEvent<HTMLInputElement>) => {
                    setKeyword(e.currentTarget.value);
                    setError('');
                  }}
                />
                {isSearching && keyword?.length > 2 ? (
                  <Spinner className="dialog-field-spinner" />
                ) : null}
                <button
                  className="dialog-search-button"
                  onClick={handleInputAction}
                >
                  <i>{keyword?.length > 0 ? <Close /> : <SearchIcon />}</i>
                </button>
              </div>
              <ProfileSearchResults
                items={results}
                isVisible={
                  results?.length && keyword?.length > 0 ? true : false
                }
                onClick={onSelectProfile}
              />
            </div>
            <InputTextarea
              label={dictionary.commentsOptional}
              placeholder={dictionary.pleaseAddAnyCommentsHere}
              onChange={handleTextChange}
            />
          </div>
        ) : null}
        <Button
          className="dialog-form-action"
          type="button"
          color="black"
          disabled={keyword.length === 0}
          loading={isFetching}
          onClick={handleReferSubmit}
          dictionary={dictionary.referACourse}
        />
      </DialogDrawer>
      {referToast && (
        <Toast
          text={
            referToast?.type === 'success'
              ? t(dictionary.youveSuccessfullyReferredACourse)
              : referToast?.message
          }
          description={
            referToast?.type === 'success'
              ? t(dictionary.thankYouTorSharingTheOpportunityToLearnAndGrow)
              : undefined
          }
          type={referToast?.type}
          onClose={clearMessages}
        />
      )}
    </div>
  );
};

export default ReferCourse;
