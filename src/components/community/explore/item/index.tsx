import dictionary from '@/dictionaries';
import INetworkCommunity from '@/types/domain/my-network/networkCommunity';
import { UsersOutline } from '@/ui/icons';
import Image from '@/ui/image';
import Typography from '@/ui/typography';
import { useTranslations } from 'next-intl';
import { FC } from 'react';
import styles from '../style.module.scss';
import { Link } from '@/i18n/routing';

interface IItem {
  community: INetworkCommunity;
}

const CommunityItem: FC<IItem> = ({ community }) => {
  const t = useTranslations();

  return (
    <Link href={`/community/${community.networkid}`} className={styles.item}>
      <div className={styles.inner}>
        <Image
          className={styles.img}
          src={community.imageurl as string}
          width={120}
          height={120}
          alt={community.title as string}
        />
        <div className={styles.right}>
          <Typography as="p" className={styles.title}>
            {community.title}
          </Typography>
          <div className={styles.members}>
            <UsersOutline />
            <Typography as="span">
              {community.members} {t(dictionary.members)}
            </Typography>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default CommunityItem;
