import { NextRequest, NextResponse } from 'next/server';
import { generateUAEPassURL } from '@/utils/auth/uae-pass';
import { getUserLocale } from '@/utils/auth';

/**
 * Method to redirect user to UAE PASS
 * for authentication with required
 * parameters
 */
export async function GET(request: NextRequest) {
  const from = request.nextUrl.searchParams.get('from');
  //User locale
  const locale = await getUserLocale();

  // Generating UAE pass URL
  const UAEPassURL = await generateUAEPassURL(locale, from);

  // Redirecting user to UAE pass
  return NextResponse.redirect(UAEPassURL);
}
