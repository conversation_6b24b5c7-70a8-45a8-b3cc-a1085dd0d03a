@use 'mixins' as *;

.header {
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
}

.head {
  display: flex;
  justify-content: space-between;
  @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
}

.info {
  align-items: flex-start;
  display: flex;
  @include forMarginPadding(margin, size(0), size(0), size(16), size(0));

  &.multisession {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }
}

.left {
  width: 60%;
}

.right {
  width: 40%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-end;
  gap: size(20);

  button {
    width: auto;
  }
}

.tags {
  @include forMarginPadding(margin, size(0), size(0), size(0), auto);

  ul {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
    display: flex;

    li {
      @include forMarginPadding(margin, size(0), size(10), size(0), size(0));
      list-style: none;

      &:last-child {
        @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
      }
    }
  }
}

.approvalWarning {
  display: flex;
  align-items: center;
  font-size: size(16);
  background-color: var(--bg-shade-4);
  @include forMarginPadding(padding, size(16), size(24), size(16), size(24));
  @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
  @include borderRadius(var(--radius));

  & svg {
    fill: var(--grey-900);
    width: size(16);
    height: size(16);
    flex: none;
    @include forMarginPadding(margin, size(0), size(10), size(0), size(0));
  }
}

.courseDetail {
  @include forMarginPadding(margin, size(0), size(0), size(10), size(0));
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
  display: flex;

  li {
    @include forMarginPadding(margin, size(0), size(10), size(0), size(0));
    list-style: none;
    display: flex;
    color: var(--grey-800);

    i {
      display: flex;
      width: size(16);
      height: size(16);
      @include forMarginPadding(margin, size(0), size(5), size(0), size(0));

      svg {
        fill: var(--grey-800);
        fill-rule: evenodd;
      }
    }

    span {
      font-size: size(14);
    }

    &:last-child {
      @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    }
  }
}

.activityLabel {
  text-transform: lowercase;
}

.courseTitle {
  font-size: size(28);
  font-weight: 700;
}

.status {
  align-items: center;
  display: flex;
  color: var(--grey-800);
  margin-bottom: size(20);

  i {
    display: flex;
    width: size(20);
    height: size(20);
    @include forMarginPadding(margin, size(0), size(5), size(0), size(0));

    svg {
      fill: var(--grey-800);
    }
  }

  span {
    font-size: size(14);
    text-transform: lowercase;
  }
}

.externalLink {
  @include forMarginPadding(padding, size(0), size(0), size(16), size(0));
}

.siteLink {
  font-size: size(16);
  text-decoration: none;
  color: var(--grey-900);
  display: inline-flex;
  align-items: center;

  i {
    width: size(32);
    height: size(32);
    @include forMarginPadding(margin, size(0), size(10), size(0), size(0));
    overflow: hidden;
    @include borderRadius(50%);
  }

  span {
    display: inline;
    padding-bottom: 0;
    transition: all 0.5s linear;
    background: linear-gradient(
      to bottom,
      var(--grey-900) 0%,
      var(--grey-900) 98%
    );
    background-size: 0 1px;
    background-repeat: no-repeat;
    background-position: left 100%;

    @include rtl {
      background-position: right 100%;
    }
  }
}

.progressHead {
  width: 80%;

  + button {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(20));
  }

  div[class*='bar'] {
    border-radius: size(30) 0 0 size(30);
    position: relative;

    @include rtl {
      border-radius: 0 size(30) size(30) 0;
    }

    &:after {
      background-color: var(--white);
      content: '';
      height: 100%;
      position: absolute;
      right: 0;
      width: size(5);

      @include rtl {
        left: 0;
        right: auto;
      }
    }
  }

  div[class*='is-completed'] {
    border-radius: size(30) size(30) size(30) size(30);

    &:after {
      display: none;
    }
  }

  div[class*='progress'] {
    background-color: var(--disabled);
  }

  p[class*='lead'] {
    font-size: size(18);
    color: var(--grey-700);
  }
}

.percentageNumber {
  font-size: size(20);
}

.percentageSymbol {
  font-size: size(10);
}

.iframeWrapper {
  animation-name: fade-in-collapsed-iframe;
  animation-duration: 0.4s;
  position: relative;
}

.expanded {
  animation-name: fade-in-iframe;
  animation-duration: 0.4s;
  background-color: rgba(0, 0, 0, 0.4);
  height: 100vh;
  left: 0;
  position: fixed;
  top: 0;
  width: 100vw;
  z-index: 1000;
}

.iframeInnerWrapper {
  position: relative;
}

.innerExpanded {
  height: calc(100% - size(80)) !important;
  margin: size(40);
  width: calc(100% - size(80)) !important;

  @media (min-width: 1400px) {
    margin: size(40) auto;
    max-width: 1400px;
  }
}

.loadingWrapper {
  background-color: #f9f9f9;
  border-radius: size(12);
  left: 1px;
  height: calc(100% - 2px);
  position: absolute;
  top: 1px;
  width: calc(100% - 2px);
  z-index: 1;

  svg {
    display: block;
    margin: size(-20) auto auto auto;
    position: relative;
    top: 53%;
  }
}

.cookieMessage {
  position: absolute;
  top: 0;
  max-height: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  background-color: var(--pageBg);
  gap: size(10);
  border: 1px solid var(--grey-400);

  .cookieMessageText {
    width: 60%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  svg {
    fill: var(--grey-900);
    height: size(40);
    width: size(40);
  }

  span,
  p {
    font-size: size(28);
  }
}

.innerExpanded .cookieMessage {
  height: 100%;
  width: 100%;
}

.activityIframe {
  background-color: #f9f9f9;
  border: 1px solid var(--grey-400);
  border-radius: size(12);
  width: 100%;
  height: 100%;
}

.actionsWrapper {
  display: block;
  position: absolute;
  right: size(25);
  top: size(15);

  @include rtl {
    left: size(25);
    right: auto;
  }
}

.action {
  background-color: rgba(255, 255, 255, 0.7);
  border: 0;
  border-radius: 50%;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  display: flex;
  height: size(42);
  padding: size(12);
  width: size(42);
  transition: 0.2s all ease-in-out;

  svg {
    fill: var(--grey-900);
    height: size(20);
    transform: scale(1);
    width: size(20);
  }

  &:hover {
    background-color: rgba(255, 255, 255, 1);
    transition: 0.2s all ease-in-out;

    svg {
      transform: scale(1.1);
      transition: 0.2s all ease-in-out;
    }
  }
}

.clickableBackground {
  top: 0;
  left: 0;
  height: 100%;
  position: absolute;
  width: 100%;
}

.placeholder {
  background-color: #f9f9f9;
  border: 1px solid var(--grey-400);
  border-radius: size(12);
  height: size(450);
  width: 100%;
}

.onsiteMessage {
  align-items: center;
  background-color: var(--warning-400);
  border-radius: size(12);
  color: var(--grey-900);
  display: flex;
  gap: size(8);
  font-size: size(16);
  margin-bottom: size(24);
  padding: size(16) size(24);

  svg {
    fill: var(--grey-900);
    height: size(16);
    width: size(16);
  }

  p {
    flex: 1;
    margin: 0;
  }
}

@include for-dark-theme {
  .progressHead {
    div[class*='progress'] {
      background-color: var(--grey-500);
    }
  }

  .activityIframe {
    background-color: var(--pageBg);
    border: 1px solid var(--grey-400);
    border-radius: size(12);
    width: 100%;
    height: 100%;
  }

  .loadingWrapper {
    background-color: #1f1e1e;
  }

  .action {
    svg {
      fill: var(--grey-300);
    }
  }

  .placeholder {
    background-color: #1f1e1e;
  }

  .approvalWarning {
    background-color: var(--bg-card-1);
  }

  .onsiteMessage {
    background-color: var(--bg-shade-4);
  }
}

.text {
  display: flex;
  justify-content: space-between;
  @include forMarginPadding(margin, size(0), size(0), size(10), size(0));

  p,
  h5 {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }
}

@include hover() {
  .siteLink {
    &:hover {
      span {
        background-size: 100% 1px;
      }
    }
  }
}

@include for-all-phone() {
  .head {
    flex-direction: column;
    @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
  }

  .tags {
    order: -1;
    @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
  }

  .externalLink {
    @include forMarginPadding(padding, size(0), size(0), size(16), size(0));
  }

  .info {
    display: block;
  }

  .left {
    width: 100%;
  }

  .right {
    bottom: size(16);
    @include leftToRight(0);
    width: 100%;
    @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
    @include forMarginPadding(margin, size(10), size(0), size(0), size(0));
    align-items: baseline;

    button {
      width: 100%;
      min-height: size(56);
      font-size: size(18);
    }
  }

  .progressHead {
    width: 100%;
  }

  .innerExpanded {
    height: calc(100% - size(100)) !important;
    margin: size(10);
    width: calc(100% - size(20)) !important;
  }
}

@keyframes fade-in-collapsed-iframe {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fade-in-iframe {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
