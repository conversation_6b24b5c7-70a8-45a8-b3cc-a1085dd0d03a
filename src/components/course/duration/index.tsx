import { FC, ReactNode, useMemo } from 'react';
import ICourse from '@/types/domain/learning/course';
import Typography from '@/ui/typography';
import { Clock } from '@/ui/icons';

interface IDuration {
  course: ICourse;
  children?: ReactNode;
  icon?: boolean;
}

// React component to handle course link
const Duration: FC<IDuration> = (props) => {
  // Deconstructing props
  const { course, icon = true } = props;

  // Getting course type
  const duration = useMemo(() => {
    return course?.duration || '';
  }, [course?.duration]);

  // Return JSX
  return (
    <>
      {icon &&
        (duration ? (
          <i>
            <Clock />
          </i>
        ) : null)}
      {duration ? <Typography as="span">{duration || '-'}</Typography> : null}
    </>
  );
};

// Exporting course component
export default Duration;
