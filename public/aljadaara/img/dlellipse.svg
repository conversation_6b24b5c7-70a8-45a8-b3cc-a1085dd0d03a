<svg width="819" height="1169" viewBox="0 0 819 1169" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.5" clip-path="url(#clip0_354_596)">
<g filter="url(#filter0_f_354_596)">
<path d="M514.259 889.026C589.709 812.709 522.697 658.957 364.583 545.611C206.469 432.266 333.942 629.36 258.492 705.676C183.042 781.993 -66.7604 708.633 91.3535 821.979C249.467 935.325 438.808 965.343 514.259 889.026Z" fill="url(#paint0_linear_354_596)"/>
</g>
<g filter="url(#filter1_f_354_596)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M-71.7207 608.499C-50.4405 540.062 1.14251 478.656 72.8112 478.378C144.598 478.1 195.573 539.44 218.776 607.374C243.059 678.472 245.588 763.327 185.57 808.519C123.587 855.192 37.0712 837.944 -24.186 790.322C-78.8648 747.814 -92.2848 674.634 -71.7207 608.499Z" fill="url(#paint1_linear_354_596)"/>
</g>
<g filter="url(#filter2_f_354_596)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M63.2981 392.073C79.7477 354.356 75.0127 304.935 106.787 278.793C138.538 252.671 185.404 262.861 226.231 258.024C263.22 253.642 298.705 249.426 335.886 251.651C383.219 254.484 447.644 233.501 474.099 272.857C502.267 314.763 444.417 369.294 448.778 419.602C452.336 460.641 497.015 490.111 496.505 531.301C495.951 576.009 474.302 618.528 445.887 653.048C415.178 690.356 377.368 736.8 329.101 734.677C278.174 732.437 258.212 659.844 210.213 642.682C164.701 626.41 108.884 666.489 67.6876 641.21C26.9592 616.217 4.2662 563.576 3.42429 515.791C2.60324 469.19 44.6654 434.796 63.2981 392.073Z" fill="url(#paint2_linear_354_596)"/>
</g>
</g>
<defs>
<filter id="filter0_f_354_596" x="-53.5863" y="417.779" width="692.333" height="606.945" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="46.7443" result="effect1_foregroundBlur_354_596"/>
</filter>
<filter id="filter1_f_354_596" x="-184.738" y="374.501" width="523.479" height="565.379" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="51.9381" result="effect1_foregroundBlur_354_596"/>
</filter>
<filter id="filter2_f_354_596" x="-109.813" y="135.333" width="719.547" height="712.64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="56.6125" result="effect1_foregroundBlur_354_596"/>
</filter>
<linearGradient id="paint0_linear_354_596" x1="202.925" y1="901.96" x2="426.539" y2="590.025" gradientUnits="userSpaceOnUse">
<stop stop-color="#7DA1C4"/>
<stop offset="1" stop-color="#8C958F"/>
</linearGradient>
<linearGradient id="paint1_linear_354_596" x1="187.491" y1="872.809" x2="145.516" y2="460.524" gradientUnits="userSpaceOnUse">
<stop offset="1" stop-color="#7DA1C4"/>
</linearGradient>
<linearGradient id="paint2_linear_354_596" x1="130.848" y1="906.331" x2="722.411" y2="611.178" gradientUnits="userSpaceOnUse">
<stop stop-color="#B4AFA5"/>
<stop offset="1" stop-color="#D5C8BE"/>
</linearGradient>
<clipPath id="clip0_354_596">
<rect width="932.289" height="919.305" fill="white" transform="translate(-342 887.576) rotate(-72.1832)"/>
</clipPath>
</defs>
</svg>
