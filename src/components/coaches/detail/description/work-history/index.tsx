import { FC } from 'react';
import styles from './styles.module.scss';
import { WorkOutline } from '@/ui/icons';
import Typography from '@/ui/typography';

interface ICoachWorkHistory {
  logo: string;
  designation: string;
  department: string;
  employer: string;
  date_from: string;
  date_to: string;
  time_period: string;
  location: string;
}

const CoachWorkHistory: FC<ICoachWorkHistory> = ({
  logo,
  designation,
  department,
  employer,
  date_from,
  date_to,
  time_period,
  location,
}) => {
  return (
    <div className={styles.coachWorkHistory}>
      <div className={styles.companyLogo}>
        {logo ? (
          <img
            src={logo as string}
            alt="company logo"
            className={`img-fluid ${styles.photo}`}
          />
        ) : (
          <i>
            <WorkOutline />
          </i>
        )}
      </div>
      <div className={styles.workDetails}>
        <Typography className={styles.designation}>{designation}</Typography>
        <Typography className={styles.department}>{department}</Typography>
        <Typography className={styles.employer}>{employer}</Typography>
        <div className={styles.datesAndLocation}>
          <Typography as="span">{date_from}</Typography>-
          {date_to ? (
            <Typography as="span">{date_to}</Typography>
          ) : (
            ' Currently working'
          )}{' '}
          ·
          {time_period ? (
            <>
              <Typography as="span">{time_period}</Typography>·
            </>
          ) : (
            ''
          )}
          <Typography as="span">{location}</Typography>
        </div>
      </div>
    </div>
  );
};

export default CoachWorkHistory;
