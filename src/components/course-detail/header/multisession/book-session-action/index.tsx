import { FC, useCallback, useMemo, useState } from 'react';
import useCourseDetailStore from '@/stores/learning/courses/detail';
import useMultisessionCourseStore from '@/stores/learning/courses/multisession';
import dictionary from '@/dictionaries';
import Button from '@/ui/form/button';
import DialogDrawer from '@/ui/dialog-drawer';
import { useTranslations } from 'next-intl';
import BookingCohort from '../booking';
import ConfirmationDialog from '../confirm-dialog';
import { AvailableSlotMultiday } from '@/types/domain/learning/course';

interface IBookSessionAction {
  list: AvailableSlotMultiday[];
}

const BookSessionAction: FC<IBookSessionAction> = () => {
  // Store states and setters
  const courseDetail = useCourseDetailStore((state) => state.detail);
  const popupMessage = useMultisessionCourseStore(
    (state) => state.popupMessage,
  );
  const setPopupMessage = useMultisessionCourseStore(
    (state) => state.setPopupMessage,
  );

  // Internationalization
  const t = useTranslations();

  // Local component state
  const [isBookingDialogOpened, setIsBookingDialogOpened] = useState(false);

  const managerApprovalRequired = useMemo(() => {
    return courseDetail?.lessons?.some(
      (lesson) => lesson.manager_approval_required,
    );
  }, [courseDetail?.lessons]);

  const hasPendingOrBookedLesson = useMemo(() => {
    const targetStates = ['booked', 'pending'];
    return courseDetail?.available_slot_multiday?.some((slot) =>
      targetStates.includes(slot.booked?.toLowerCase() || ''),
    );
  }, [courseDetail?.available_slot_multiday]);

  const buttonText = useMemo(() => {
    if (
      managerApprovalRequired &&
      courseDetail?.available_slot_multiday?.length === 1
    ) {
      return t(dictionary.requestToEnroll);
    } else if (managerApprovalRequired) {
      return t(dictionary.requestACohort);
    } else {
      return t(dictionary.selectACohort);
    }
  }, [
    managerApprovalRequired,
    courseDetail?.available_slot_multiday?.length,
    t,
  ]);

  const handleOpenBookingDialog = useCallback(() => {
    setIsBookingDialogOpened(true);
  }, []);

  const handleCloseBookingDialog = useCallback(() => {
    setIsBookingDialogOpened(false);
  }, []);

  const handleCloseConfirmationDialog = useCallback(() => {
    setPopupMessage(null);
  }, [setPopupMessage]);

  if (!courseDetail?.isSelfEnrollmentEnabled) {
    return null;
  }

  // If any lesson is already booked or pending approval, hide booking action
  if (hasPendingOrBookedLesson) {
    return null;
  }

  return (
    <>
      <Button color="black" type="button" onClick={handleOpenBookingDialog}>
        {buttonText}
      </Button>

      <DialogDrawer
        isOpened={isBookingDialogOpened}
        onClose={handleCloseBookingDialog}
        title={t(dictionary.selectACohort)}
      >
        <BookingCohort
          list={courseDetail?.available_slot_multiday || []}
          closeDrawer={handleCloseBookingDialog}
          managerApprovalRequired={!!managerApprovalRequired}
        />
      </DialogDrawer>

      {popupMessage && courseDetail && (
        <ConfirmationDialog
          isOpen={!!popupMessage}
          PopupMessageMultisession={popupMessage}
          onClose={handleCloseConfirmationDialog}
          courseId={String(courseDetail.courseId ?? '')}
        />
      )}
    </>
  );
};

export default BookSessionAction;
