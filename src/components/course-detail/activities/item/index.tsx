import { FC, useCallback } from 'react';
import Link from 'next/link';
import Typography from '@/ui/typography';
import ExternalIcon from '@/ui/icons/external';

// Styles
import styles from '../style.module.scss';

interface IItem {
  isAvailable?: boolean;
  isEnrolled?: boolean;
  title?: string;
  subTitle?: string;
  text?: [];
  link?: string;
  attendance?: Array<{
    id: number;
    text: string;
  }>;
  onActivityClick?: (url: string) => void;
}

const Item: FC<IItem> = (props) => {
  const {
    isAvailable,
    isEnrolled,
    title,
    text,
    link,
    attendance = [],
    onActivityClick,
  } = props;

  const handleClick = useCallback(
    (url: string) => {
      if (onActivityClick) {
        onActivityClick(url);
      }
    },
    [onActivityClick],
  );

  return (
    <div className={styles.itemCard}>
      <div className={styles.itemData}>
        <Typography as="p" className={styles.title}>
          {title}
        </Typography>
        <Typography as="span" className={styles.content}>
          {text}
        </Typography>
      </div>

      {attendance?.length > 0 && (
        <nav className={styles.weekList}>
          <ul>
            {attendance.map((x) => (
              <li key={x.id}>
                <Link href="#">
                  <Typography as="span">{x.text}</Typography>
                  <i>
                    <ExternalIcon />
                  </i>
                </Link>
              </li>
            ))}
          </ul>
        </nav>
      )}
      {link && (
        <button
          disabled={!isAvailable || !isEnrolled}
          onClick={() => handleClick(link)}
          className={styles.externalLink}
        >
          <i>
            <ExternalIcon />
          </i>
        </button>
      )}
    </div>
  );
};

export default Item;
