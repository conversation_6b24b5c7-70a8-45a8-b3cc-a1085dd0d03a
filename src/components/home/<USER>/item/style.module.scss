@use 'mixins' as *;

.profile {
  display: flex;
  align-items: center;
  justify-content: center;
  @include borderRadius(var(--radius));
  border: 1px solid var(--progressBg);
  @include forMarginPadding(padding, size(21), size(20), size(21), size(20));
  cursor: pointer;
  height: 100%;
}

.pic {
  @include borderRadius(50%);
  flex-shrink: 0;
  overflow: hidden;
  width: size(85);
  height: size(85);
  @include forMarginPadding(margin, size(0), size(16), size(0), size(0));

  img {
    transform: scale(1);
    transition: 0.2s all ease-in-out;
  }
}

.name {
  font-weight: 400;
  margin-bottom: size(10);
}

.role {
  margin-bottom: size(5);
}
