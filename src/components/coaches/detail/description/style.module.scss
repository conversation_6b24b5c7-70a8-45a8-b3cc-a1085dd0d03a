@use 'mixins' as *;

.body {
  width: 100%;
  @include forMarginPadding(padding, size(0), size(32), size(24), size(32));
  overflow-y: auto;
  height: calc(100vh - size(192));

  @media (min-width: 576px) {
    &::-webkit-scrollbar-track {
      background-color: var(--white);
    }

    &::-webkit-scrollbar {
      width: size(8);
      background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: size(8);
      background-color: var(--grey-900);
    }
  }
}

.tabSections {
  @include forMarginPadding(padding, size(40), size(0), size(0), size(0));

  .tabTitle {
    font-weight: 700;
    font-size: size(24);
    color: var(--grey-900);
  }
}

.footer {
  display: flex;
  gap: size(16);
  width: 100%;
  z-index: 9;
  background: var(--white);
  @include forMarginPadding(padding, size(24), size(24), size(24), size(24));
}

.banner {
  position: relative;
  height: size(446);
  overflow: hidden;
  @include borderRadius(var(--radius));
  width: 100%;
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));
}

.bannerImg {
  width: 100%;
  height: 100%;
  position: absolute;
  @include leftToRight(0);
  top: 0;

  &:before {
    position: absolute;
    top: 0;
    @include leftToRight(0);
    width: 100%;
    height: 100%;
    background: linear-gradient(
      180deg,
      rgba(13, 33, 54, 0.4) 14.43%,
      rgba(13, 33, 54, 0) 64.7%
    );
    content: '';
  }

  &:after {
    position: absolute;
    bottom: 0;
    @include leftToRight(0);
    width: 100%;
    height: 100%;
    background: linear-gradient(
      0deg,
      rgba(13, 33, 54, 0.4) 14.43%,
      rgba(13, 33, 54, 0) 64.7%
    );
    content: '';
  }
}

.bannerData {
  display: flex;
  flex-direction: column;

  @include forMarginPadding(padding, size(24), size(0), size(24), size(0));
  @include forMarginPadding(margin, size(0), size(0), size(35), size(0));

  .nameAndRating {
    display: flex;
    justify-content: space-between;

    @include forMarginPadding(margin, size(0), size(0), size(24), size(0));
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p {
    color: var(--grey-900);
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }
}

@include for-dark-theme() {
  .bookCta:disabled {
    background-color: var(--grey-700) !important;
    color: var(--disabled) !important;
  }
}

.backBtn {
  @include leftToRight(size(24));
  top: size(24);
  width: size(30);
  height: size(30);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 7;
  background: var(--white);
  border: 1px solid var(--grey-900);
  @include borderRadius(50%);
  cursor: pointer;
  @include transitions(0.5s);
  fill: var(--grey-900);

  i {
    width: size(20);
    height: size(20);
    display: flex;
    padding: size(5);

    svg {
      fill-rule: evenodd;
      width: 100%;
      height: 100%;
    }
  }
}

.infoWrapper {
  display: flex;
  align-items: center;
  gap: size(12);

  svg {
    width: size(29);
    height: size(29);
    fill: var(--grey-900);
  }

  p {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    font-size: size(25);
    color: var(--grey-900);
  }

  @include forMarginPadding(margin, size(0), size(0), size(15), size(0));
}

.rating {
  display: flex;
  align-items: center;
  gap: size(5);
  color: var(--grey-800);
  font-size: size(18);
  font-weight: 400;

  img {
    height: size(23);
    width: size(23);
  }
}

@include for-dark-theme() {
  .rating {
    color: var(--grey-900);
  }
}

// .skillsList {
//   ul {
//     @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
//     @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
//     display: flex;
//     flex-wrap: wrap;

//     li {
//       list-style: none;
//       @include forMarginPadding(margin, size(0), size(10), size(10), size(0));
//     }
//   }
// }

// .skills {
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   @include borderRadius(40px);
//   @include forMarginPadding(padding, size(8), size(16), size(8), size(16));
//   font-size: size(16);
//   background: var(--gun-smoke-500);
//   color: var(--grey-900);
// }

.skills {
  margin-top: auto;
  display: flex;
  flex-wrap: wrap;
  gap: size(10);
  @include forMarginPadding(padding, size(20), size(0), size(0), size(0));

  .skillItem {
    display: flex;
    justify-content: center;
    @include borderRadius(40px);
    @include forMarginPadding(padding, size(8), size(16), size(8), size(16));
    background-color: var(--bg-shade-10);
    font-size: size(16);

    .skill {
      color: var(--grey-900);
    }
  }
}

.moreBtn {
  color: var(--text-link-blue);
  font-size: size(16);
  background: none;
  border: none;
  cursor: pointer;

  span {
    display: inline;
    padding-bottom: 0;
    transition: all 0.5s linear;
    background: linear-gradient(
      to bottom,
      var(--text-link-blue) 0%,
      var(--text-link-blue) 98%
    );
    background-size: 0 1px;
    background-repeat: no-repeat;
    background-position: left 100%;

    @include rtl {
      background-position: right 100%;
    }
  }
}

.alert {
  background: var(--oat-milk-500);
  @include forMarginPadding(padding, size(10), size(35), size(10), size(15));
  display: inline-flex;
  flex-wrap: nowrap;
  align-items: center;
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));
  @include borderRadius(var(--radius));

  p {
    color: var(--grey-900);
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    opacity: 0.8;
  }
}

.icon {
  width: size(32);
  height: size(32);
  display: flex;
  justify-content: center;
  align-items: center;
  @include borderRadius(50%);
  border: solid 1px var(--grey-400);
  padding: size(7);
  @include forMarginPadding(margin, size(0), size(8), size(0), size(0));

  i {
    width: size(20);
    height: size(20);
  }
}

.section {
  @include forMarginPadding(margin, size(0), size(0), size(30), size(0));

  h4 {
    @include forMarginPadding(margin, size(0), size(0), size(15), size(0));
  }
}

.description {
  color: var(--grey-800);
}

.coachModalHeader {
  display: flex;
  justify-content: flex-end;
  height: size(72);
  position: relative;
  padding: size(20);
}

@include hover() {
  .backBtn {
    &:hover {
      background: var(--grey-900);
      fill: var(--white);
    }
  }

  .moreBtn {
    &:hover {
      span {
        background-size: 100% 1px;
      }
    }
  }
}

@include for-dark-theme() {
  .bannerData {
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p {
      color: var(--grey-900);
    }
  }

  .alert {
    background: var(--oat-milk-300);
    color: var(--white);
  }

  .icon {
    border-color: var(--grey-500);

    svg {
      fill: var(--grey-900);
    }
  }

  // .skills {
  //   color: var(--grey-300);
  // }
}

@include for-all-phone() {
  .footer {
    button {
      font-size: size(18);
      height: size(56);
      text-align: center;
    }
  }

  .alert {
    p {
      font-size: size(14);

      &:last-child {
        font-weight: bold;
      }
    }
  }
}
