import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraToggleLikeStatusService } from '@/services/totara/my-network';

/**
 * Route to toggle like status on POST, COMMENT OR REPLY
 */
export async function POST(request: NextRequest) {
  try {
    // Getting request params
    const { id, action, area, component } = await request.json();

    // call API to toggle like status on POST, COMMENT OR REPLY
    const response = await TotaraToggleLikeStatusService(
      id,
      action,
      area,
      component,
    );
    // Return the response
    return NextResponse.json({
      success: response.data.success,
      message: response.data.message,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error);
  }
}
