import TopPicks from './top-picks';
import TrendingCourses from './trending-courses';
import SuggestedCourses from './suggested-courses';
import NewlyReleasedCourses from './newly-released-courses';
import ShortCourses from './short-courses';
import AITopPicks from './ai-top-picks';
import useBooleanFeatureFlag from '@/hooks/feature-flags/useBooleanFeatureFlag';
import useFeatureAccessStore from '@/stores/feature-access';
import { Features } from '@/types/domain/feature-access/featureAccess';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';
// import Scholarships from '@/components/common/scholarships';

const DiscoverLandingPage = () => {
  const AIEnabled = useBooleanFeatureFlag('aiFeature');
  const checkFeatureAccess = useFeatureAccessStore((x) => x.checkAccess);
  const isFetchingFeatureAccess = useFeatureAccessStore((x) => x.isFetching);

  const userAIAccess = checkFeatureAccess(Features.AI);

  return (
    <>
      <WithLoader loader={<Loading />} loading={isFetchingFeatureAccess}>
        <TopPicks />
        <TrendingCourses />
        <SuggestedCourses />
        <NewlyReleasedCourses />
        {(AIEnabled || userAIAccess) && <AITopPicks />}
        <ShortCourses />
        {/* <Scholarships /> */}
      </WithLoader>
    </>
  );
};

export default DiscoverLandingPage;
