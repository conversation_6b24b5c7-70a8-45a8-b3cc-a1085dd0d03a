import { NextRequest, NextResponse } from 'next/server';
import CONFIG from '@/config';
import { getUAEPassAuthReturnURL } from '@/utils/auth/uae-pass';
import { generateApplicationURL } from '@/utils/url';
import { getUserLocale, removeUserSession } from '@/utils/auth';
import Authenticator from '@/utils/auth/authenticator';
import { AxiosError } from 'axios';

const login = (route: string, error: string = '') => {
  // Redirecting user to uae pass screen
  return NextResponse.redirect(route + '?message=' + error);
};

// Method to handle user after successfull login form UAE pass
export async function GET(request: NextRequest) {
  const from = request.nextUrl.searchParams.get('from');
  const code = request.nextUrl.searchParams.get('code') || '';
  let error = request.nextUrl.searchParams.get('error') || '';

  // getting user locale
  const locale = await getUserLocale();

  //Creating return URL
  const returnURL = await getUAEPassAuthReturnURL(from);

  // Creating login success URL
  const successURL = await generateApplicationURL(
    from ?? CONFIG.routes.home,
    locale,
  );

  // Creating login page URL
  const loginURL = await generateApplicationURL(
    CONFIG.routes.login.uaePass,
    locale,
    from,
  );

  // If code not exist
  if (!code) {
    // Setting error message
    error = 'Unable to verify from UAE Pass. Please try again';
    // Redirecting user to uae pass screen
    return login(loginURL, error);
  }

  // If login cancelled on application
  if (error === 'cancelledOnApp') {
    // Setting error message
    error = 'Login declined on application.';
    // Redirecting user to uae pass screen
    return login(loginURL, error);
  }

  try {
    // Getting workspace instance
    const workspace = Authenticator.workspace();
    // Attempting login with workspace
    await workspace.UAEPassAttempt(code, returnURL);
    // Saving workspace session
    await workspace.save();

    // Getting ilearn instance
    const ilearn = Authenticator.ilearn();
    // Setting up ilearn login
    await ilearn.setup();
    // Attempting ilearn login
    await ilearn.attempt(workspace.auth);
    // Fetching ilearn secrets
    await ilearn.secrets();
    // Registering ilearn login device
    await ilearn.register();
    // Saving ilearn login data
    await ilearn.save();

    // Fetching ilearn user profile
    await ilearn.profile();
    // Update onboard status
    await workspace.updateOnboardingState(ilearn.data().user);

    // User and ilearn token exists
    if (workspace?.auth?.id && ilearn?.data()?.token) {
      // Return the response
      return NextResponse.redirect(successURL);
    }
    //Remove user sessions
    await removeUserSession();
    // Setting error message
    error = 'Unable to login. Please try again';
  } catch (e) {
    // Masking exception
    const _error = e as AxiosError<{ title: string }>;
    // Setting error message for frontend
    error = _error?.response?.data?.title || _error?.message;
    // Log error
    console.log(e);
  }

  //redirect user to uae pass screen
  return login(loginURL, error);
}
