import { ICommunityLibrary } from '@/types/domain/my-network/communityDetails';
import genericFilePlaceholder from '@public/images/generic-file-placeholder.png';
import styles from './style.module.scss';
import Typography from '@/ui/typography';
import { Download } from '@/ui/icons';
import { Link } from '@/i18n/routing';
import { formatBytes } from '@/utils/helpers';

interface ILibrayItemProps {
  record: ICommunityLibrary;
}

const LibraryItem: React.FC<ILibrayItemProps> = ({ record }) => {
  return (
    <div className={styles.itemWrapper}>
      <img
        className={styles.genericDocument}
        src={genericFilePlaceholder.src}
        alt="generic document icon"
      />
      <div className={styles.itemDetails}>
        <Typography className={styles.fileName}>{record.filename}</Typography>
        <Link
          className={styles.downloadBtn}
          href={record.url}
          aria-label={record.filename}
        >
          <Download />{' '}
          <Typography className={styles.fileSize}>
            {formatBytes(record.filesize)}
          </Typography>
        </Link>
      </div>
    </div>
  );
};

export default LibraryItem;
