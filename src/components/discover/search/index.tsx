import Paper from '@/ui/paper';
import SearchField from './field';
import Popup from './popup';
import useToggleable from '@/hooks/helpers/useToggle';

// Styles
import styles from './style.module.scss';
import Typography from '@/ui/typography';

const Search = () => {
  // Popup state
  const popup = useToggleable(false);

  // Return JSX
  return (
    <Paper className={styles.discoverPaper}>
      <Typography as="h1" dictionary="Discover" className={styles.title} />
      <div className={styles.discover} onClick={popup.open}>
        <SearchField />
      </div>
      {popup.visible && <Popup onClose={popup.close} />}
    </Paper>
  );
};

export default Search;
