@use 'mixins' as *;

.bookedCoachItem {
  display: flex;
  flex-direction: column;
  border: size(1) solid var(--grey-400);
  @include borderRadius(size(12));
  @include forMarginPadding(padding, size(14), size(14), size(14), size(14));
  @include forMarginPadding(margin, size(12), size(0), size(0), size(0));

  .mainContent {
    display: flex;
    gap: size(12);

    .photo {
      width: size(76);
      height: size(76);
      @include borderRadius(size(6));
    }

    .description {
      flex-grow: 1;

      .nameAndRating {
        display: flex;
        justify-content: space-between;
        align-items: center;
        @include forMarginPadding(margin, size(0), size(0), size(10), size(0));

        .coachName {
          font-size: size(16);
          font-weight: 400;
          line-height: 1.2;
          letter-spacing: 0;
          color: var(--grey-900);
          @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
        }

        .rating {
          display: flex;
          align-items: center;
          align-self: flex-start;
          line-height: 1;
          @include rightToLeft(size(16));

          i {
            width: 16px;
            height: 16px;
            position: relative;
            @include forMarginPadding(
              margin,
              size(0),
              size(5),
              size(0),
              size(0)
            );
          }

          span {
            font-size: size(16);
            line-height: 1.2;
          }
        }
      }

      .designationWrapper {
        display: flex;
        gap: size(12);
        align-items: center;

        svg {
          fill: var(--grey-900);
          width: size(16);
        }
      }

      .designation {
        display: flex;
        align-items: center;
        font-size: size(12);
        @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
      }

      .coachExperience {
        display: flex;
        align-items: center;
        @include forMarginPadding(margin, size(15), size(0), size(16), size(0));

        .experienceLogos {
          display: flex;

          img {
            width: size(16);
            height: size(16);
            @include borderRadius(50%);
            border: 1px solid black;
          }

          .logoImgOverlap {
            @include forMarginPadding(
              margin,
              size(0),
              size(0),
              size(0),
              size(-15)
            );
          }
        }

        .experienceText {
          @include forMarginPadding(margin, size(0), size(0), size(0), size(8));
          font-size: size(12);
          font-weight: 400;
          line-height: 1.2;
        }
      }
    }
  }

  .skills {
    margin-top: auto;
    display: flex;
    flex-wrap: wrap;
    gap: size(10);
    border-top: size(1) solid var(--grey-400);
    @include forMarginPadding(padding, size(20), size(0), size(0), size(0));

    .skillItem {
      display: flex;
      justify-content: center;
      @include borderRadius(40px);
      @include forMarginPadding(padding, size(8), size(16), size(8), size(16));
      background-color: var(--bg-shade-10);
      font-size: size(16);

      .skill {
        color: var(--grey-900);
      }
    }

    .bookmark {
      display: flex;
      justify-content: center;
      align-items: center;
      border: size(1) solid var(--progressBg);
      @include borderRadius(50%);
      @include rightToLeft(size(22));
      background: transparent;
      cursor: pointer;
      width: size(36);
      height: size(36);

      svg {
        fill: var(--grey-900);
        width: size(24);
        height: size(24);
      }
    }
  }
}
