import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import Authenticator from '@/utils/auth/authenticator';
import { removeUserSession } from '@/utils/auth';

// Method to login user via email
export async function POST(request: NextRequest) {
  try {
    // Getting request body
    const body = await request.json();
    // Getting user login with credentials
    const email: string = body.email;
    const password: string = body.password;

    // Getting workspace instance
    const workspace = Authenticator.workspace();
    // Attempting login with workspace
    await workspace.attempt(email, password);
    // Saving workspace session
    await workspace.save();

    // Getting ilearn instance
    const ilearn = Authenticator.ilearn();
    // Setting up ilearn login
    await ilearn.setup();
    // Attempting ilearn login
    await ilearn.attempt(workspace.auth);
    // Fetching ilearn secrets
    await ilearn.secrets();
    // Registering ilearn login device
    await ilearn.register();
    // Saving ilearn login data
    await ilearn.save();

    // Fetching ilearn user profile
    await ilearn.profile();
    // Update onboard status
    await workspace.updateOnboardingState(ilearn.data().user);
    // Fetching user progress
    await workspace.fetchUserProgress();

    // User and ilearn token exists
    if (workspace?.auth?.id && ilearn?.data()?.token) {
      // Return the response
      return NextResponse.json({
        ilearn: ilearn.data(),
        progress: workspace.progress,
        auth: workspace.auth,
        user: workspace.user,
        totaraUser: ilearn.data().user,
      });
    }
    //Remove user sessions
    await removeUserSession();
    // Return Error response
    return ErrorResponse('Unable to login. Please try again');
  } catch (error) {
    // Return Error response
    return ErrorResponse(error);
  }
}
