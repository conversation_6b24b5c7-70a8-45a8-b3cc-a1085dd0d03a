import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import FilterButtons from '@/components/common/filter-buttons';
import useCoachesCategoriesStore from '@/stores/learning/coaches/categories';
import useCoachesStore from '@/stores/learning/coaches/list';
import { useTranslations } from 'next-intl';
import { hasTranslation } from '@/utils/translate/helpers';

import styles from './style.module.scss';

/**
 * Component to render the filters
 */
const Filters: FC = () => {
  const [categoryId, setCategoryId] = useState<string | undefined>('all');
  // Getting fetch action
  const onCoachesFetch = useCoachesStore((x) => x.fetch);

  // Getting fetch action
  const onFetch = useCoachesCategoriesStore((x) => x.fetch);

  // Geting the coaches list
  const list = useCoachesCategoriesStore((x) => x.list);
  const t = useTranslations();

  // Options for the filter buttons
  const options = useMemo(() => {
    // Return the options
    return list.map((x) => ({
      id: x?.toLowerCase()?.replaceAll(' ', '-'),
      title: hasTranslation(x) ? t(x) : x,
      original_title: x,
    }));
  }, [list, t]);

  /**
   * Method to handle change in filter
   */
  const handleChange = useCallback(
    (option: string) => {
      // Set the filter
      const category = options.find((x) => x.id === option);
      setCategoryId(category?.id);
      onCoachesFetch({ category: category?.original_title });
    },
    [onCoachesFetch, options, setCategoryId],
  );

  // Effect to fetch the coaches list
  useEffect(() => {
    // Fetch the coaches list
    onFetch();
  }, [onFetch]);

  // Return JSX
  return (
    <div className={styles.filtersWrapper}>
      <FilterButtons
        activeOption={categoryId}
        filterOptions={options}
        onChange={handleChange}
        isScrollable={true}
      />
    </div>
  );
};

export default Filters;
