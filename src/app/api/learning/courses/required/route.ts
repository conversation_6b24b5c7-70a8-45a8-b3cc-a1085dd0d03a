import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchRequiredCoursesService } from '@/services/totara/courses';
import IPagination from '@/types/domain/internal/base/pagination';

// Route to get the required courses
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const page = request.nextUrl.searchParams.get('page') || '1';
    const perPage = request.nextUrl.searchParams.get('perPage') || '10';

    // Fetch the data
    const response = await TotaraFetchRequiredCoursesService({
      page: parseInt(page),
      perPage: parseInt(perPage),
    });

    // TODO - Implement pagination from API response
    // Creating pagination object
    const pagination: IPagination = {
      total: response?.data?.requiredcourses?.length || 0,
      page: 1,
      perPage: response?.data?.requiredcourses?.length || 0,
      totalPages: 1,
      message: 'Pagination is not working with the API',
    };

    // Return the response
    return NextResponse.json({
      list: response?.data?.requiredcourses || [],
      pagination: pagination,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
