@use 'mixins' as *;

.resultHeading {
  align-items: baseline;
  position: relative;
  display: flex;
  @include forMarginPadding(padding, size(0), size(0), size(30), size(0));

  h4 {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }
}

.searchResultsWrapper {
  div[class*='paper'] {
    border-radius: 0;
    padding: 0;
  }

  div[class*='head'] {
    h4 {
      font-size: size(20);
    }
  }
}

.viewAllNew {
  background-color: transparent;
  border: 0;
  color: var(--grey-900);
  cursor: pointer;
  display: inline-flex;
  font-size: size(16);
  color: var(--grey-900);
  text-decoration: underline;
  position: absolute;
  right: size(25);
  z-index: 1;

  &:hover {
    text-decoration: none;
  }
}

.subcategoryScrollWrapper {
  position: relative;
  display: flex;
  align-items: center;
  overflow-x: hidden;
  margin-bottom: size(30);
}

.subcategoryWrapper {
  display: flex;
  gap: size(8);
  flex-direction: row;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
  white-space: nowrap;
  overflow-x: auto;

  &::-webkit-scrollbar {
    display: none;
  }

  .subcategoryButton {
    flex-grow: 1;
    background-color: transparent;
    border: 1px solid var(--grey-400);
    border-radius: size(20);
    cursor: pointer;
    min-height: size(40);
    font-size: size(16);
    line-height: size(16);
    @include forMarginPadding(padding, size(12), size(16), size(12), size(16));
    transition: 0.2s all ease-in-out;
    white-space: nowrap;

    &:hover {
      background-color: var(--grey-900);
      border: 1px solid var(--grey-900);
      color: var(--white);
      transition: 0.2s all ease-in-out;
    }
  }
}

.scrollArrow {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  position: absolute;
  z-index: 1;
  display: flex;
  justify-content: center;
  height: size(34);
  width: size(34);
  border: 1px solid var(--grey-500);
  border-radius: 50%;
  background: var(--white);

  &:hover {
    opacity: 1;
  }

  &:first-child {
    left: 0;
  }

  &:last-child {
    right: 0;
  }

  svg {
    fill: var(--grey-900);
    height: size(14);
    width: size(14);
  }
}

.divider {
  border-bottom: 1px solid var(--grey-400);
  display: block;
  margin-bottom: size(40);
  width: 100%;
}

.categoryNameWrapper {
  display: flex;
  align-items: center;
  gap: size(8);
}

@include for-all-phone() {
  .categoryNameWrapper {
    align-items: flex-start;
    flex-direction: column;
  }
}

.totalCategories {
  color: var(--grey-800);
  margin-bottom: size(0);
  font-size: size(16);
  font-weight: 400;
}
