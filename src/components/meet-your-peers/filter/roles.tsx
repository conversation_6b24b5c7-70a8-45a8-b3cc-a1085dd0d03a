import { useCallback, useState } from 'react';
import ChipButton from '@/ui/form/chip-button';
import Typography from '@/ui/typography';

// Styles
import styles from './style.module.scss';
import dictionaries from '@/dictionaries';

const Roles = () => {
  // Getting the types
  const list = ['Director General', 'Undersecretary', 'Executive Director'];

  const [selected, setSelected] = useState('');

  // Method to handle click
  const onClick = useCallback(
    (x?: string) => {
      if (x) {
        setSelected(x);
      }
    },
    [setSelected],
  );

  // Return JSX
  return (
    <nav className={styles.filter}>
      <Typography as="h4" dictionary={dictionaries.byRole} />
      <ul>
        {list?.map((x, i) => (
          <li key={'type-' + i}>
            <ChipButton
              variant="rounded"
              active={selected === x}
              onClick={() => onClick(x)}
            >
              {x}
            </ChipButton>
          </li>
        ))}
      </ul>
    </nav>
  );
};

// Export the component
export default Roles;
