import { FC, useEffect } from 'react';
import WithLoader from '@/ui/skeleton/with-loader';
import Paper from '@/ui/paper';
import Item from './item';
import Loading from './loading';
import Filters from '../filters';
import useCoachesStore from '@/stores/learning/coaches/list';
// styles
import styles from './style.module.scss';
import EmptyMessage from '@/ui/empty-message';
import { useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';
import { coachExperience } from '../mock-data';

interface IList {
  onClick?: (id: string) => void;
}

/**
 * Coaches list component
 */
const List: FC<IList> = (props) => {
  // Getting click handler
  const { onClick } = props;

  // Getting loading state action
  const isFetching = useCoachesStore((x) => x.isFetching);

  // Getting fetch action
  const onFetch = useCoachesStore((x) => x.fetch);

  // Geting the coaches list
  const list = useCoachesStore((x) => x.list);
  const t = useTranslations();
  // Effect to fetch the coaches list
  useEffect(() => {
    // Fetch the coaches list
    onFetch({});
  }, [onFetch]);
  // Return JSX
  return (
    <Paper title="Discover Coaches" titleVariant="large">
      <Filters />
      <div
        className={styles.list + ` ${list?.length === 0 ? styles.isEmpty : ''}`}
      >
        <WithLoader loader={<Loading />} loading={isFetching}>
          {list?.map((coach) => (
            <Item
              key={coach?.id}
              coachId={coach?.id}
              photo={coach?.picture}
              title={coach?.firstname?.concat(' ', coach?.lastname || '')}
              designation={coach?.designation}
              rating={parseFloat(coach?.rating || '')}
              specialization={coach.my_specilization}
              coachExperience={coachExperience}
              onClick={onClick}
            />
          ))}
        </WithLoader>
        {list?.length === 0 && !isFetching ? (
          <EmptyMessage
            icon="search"
            title={t(dictionary.noCoachesFound)}
            description={t(dictionary.noCoachesYet)}
          />
        ) : null}
      </div>
    </Paper>
  );
};

export default List;
