@use 'mixins' as *;

.accordionSection {
  div[class*='formGroup'] {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }
  textarea {
    background: var(--white);
    border: solid 1px var(--grey-400);
    @include forMarginPadding(padding, size(12), size(12), size(12), size(12));
  }
}

.buttons {
  display: flex;
  flex-direction: row;
}

.containerToggleSwitch {
  @include forMarginPadding(margin, size(20), size(0), size(0), size(0));
  display: flex;
  justify-content: space-between;
}
