@use 'mixins' as *;

.timeline {
  padding-left: size(26);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: size(10);
    width: size(1);
    height: 100%;
    background-image: linear-gradient(
      to bottom,
      var(--grey-400) 50%,
      transparent 50%
    );
    background-size: 100% 10px;
  }

  h3[class*='calendar-list-day-header'] {
    font-size: size(18);
    font-weight: 700;
    margin: 0;
    position: relative;
    color: var(--grey-900);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: - size(20);
      width: size(10);
      height: size(10);
      border-radius: 50%;
      background-color: var(--grey-400);
    }
  }

  ul[class*='calendar-list-events'] {
    margin-top: size(24);
    margin-bottom: size(60);
    display: flex;
    flex-direction: column;
    li div[class*='calendar-list-event-item'] {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;

      [class*='event-details-wrapper'] {
        display: flex;
        flex-direction: row;
      }
      [class*='event-image'] img {
        width: 100%;
        height: 100%;
        border-radius: size(6);
        object-fit: cover;
      }

      h6 {
        font-size: size(16);
        margin-left: size(6);
        margin-right: size(6);
        //line-height: size(11);
        color: var(--grey-900);
        font-weight: 700;

        span {
          font-weight: 400;
        }
      }
      [class*='event-content'] {
        margin-left: size(24);
      }

      [class*='event-details'] {
        span {
          font-size: size(12);
          line-height: size(9);
          color: var(--grey-900);
          display: flex;
          flex-direction: row;
          align-items: center;
        }
      }

      div[class*='booking-status'] {
        margin-top: size(12);
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: size(12);
        margin-left: auto;

        [class*='tag'] {
          padding: size(7.5) size(12);
          background-color: var(--success-900);
          text-transform: uppercase;
          font-size: size(10);
          line-height: size(7);
          color: var(--white);
          border-radius: size(12);
        }
      }
    }

    button[class*='calendar-list-view-all-button'] {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: size(16);
      border: size(1) solid var(--grey-600);
      border-radius: size(8);
      padding: size(18.5) 0;
      font-size: size(16);
      line-height: size(11);
      color: var(--grey-800);
      justify-content: center;
      cursor: pointer;
    }
    div[class*='event-name'] {
      display: flex;
      flex-direction: column;
      gap: 3;
    }
    button[class*='event-actions'] {
      min-width: size(191);
      //margin-left: auto;
      padding: size(18.5) size(16);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: size(8);
      font-size: size(16);
      line-height: size(11);
      border: none;
      cursor: pointer;
    }
    button[class*='event-actions'][class*='open-to-enroll'] {
      background-color: var(--grey-900);
      color: var(--white);
    }
    button[class*='event-actions'][class*='enrolled'] {
      background-color: var(--disabled-border);
      color: var(--grey-700);
    }
    button[class*='event-actions'][class*='in-my-calendar'] {
      border: size(1) solid var(--grey-900);
      color: var(--grey-900);
      background-color: transparent;
    }
  }
}

.timeline.monthlyView {
  ul[class*='calendar-list-events'] {
    gap: size(16);

    li {
      list-style: none;
      cursor: pointer;

      &:not(:first-of-type) {
        padding-top: size(16);
        border-top: size(1) solid var(--grey-400);
      }

      div[class*='calendar-list-event-item'] {
        [class*='event-image'] {
          height: size(64);
          width: size(64);

          img {
            border-radius: size(6);
          }
        }
        [class*='event-content'] {
          flex: 1;
          display: flex;
          flex-direction: row;
          justify-content: center;
          margin-left: size(20);
        }
      }

      h6 {
        margin: 0;
      }

      [class*='event-details'] {
        margin-left: auto;
        display: flex;
        flex-direction: row;
        align-items: center;

        span {
          font-size: size(12);
          line-height: size(9);
          color: var(--grey-900);
          display: flex;
          flex-direction: row;
          align-items: center;
        }

        [class*='event-time'] {
          margin: 0 size(12) 0 size(28);
          position: relative;

          svg {
            display: none;
          }
        }

        [class*='event-date'],
        [class*='event-location'],
        [class*='event-time'] {
          svg {
            width: size(16);
            height: size(16);
            margin-right: size(6);
            margin-left: size(6);
            path {
              color: var(--grey-900);
              stroke: var(--grey-900);
            }
          }
        }

        [class*='event-location'] {
          position: relative;
          padding-left: size(10);
          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: size(1);
            height: 100%;
            background-color: var(--grey-400);
          }
        }
      }
    }
  }
}

.timeline.weeklyView,
.timeline.dailyView {
  ul[class*='calendar-list-events'] {
    gap: size(16);

    li {
      list-style: none;
      border: size(1) solid var(--grey-400);
      border-radius: size(12);
      padding: size(16) size(20);

      div[class*='calendar-list-event-item'] {
        display: flex;
        flex-direction: row;
        align-items: center;

        [class*='event-image'] {
          height: size(143);
          width: size(156);

          img {
            border-radius: size(10);
          }

          [class*='event-content'] {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
          }
        }
      }

      h6 {
        margin: size(16) 0 size(16) 0;

        span {
          display: none;
        }
      }

      [class*='event-details'] {
        display: flex;
        flex-direction: row;
        align-items: center;
        flex-wrap: wrap;
        gap: size(10);

        span {
          font-size: size(12);
          line-height: size(9);
          color: var(--grey-900);
          display: flex;
          flex-direction: row;
          align-items: center;
        }

        [class*='event-time'] {
          margin: 0 size(12) 0 size(16);
          position: relative;

          svg {
            margin-right: size(4);
          }
        }

        [class*='event-date'],
        [class*='event-location'],
        [class*='event-time'] {
          svg {
            width: size(16);
            height: size(16);
            margin-right: size(6);
            margin-left: size(6);
            path {
              color: var(--grey-900);
              stroke: var(--grey-900);
            }
          }
        }

        [class*='event-location'] {
          flex: 0 0 100%;
        }
      }
    }
  }
}
@media screen and (max-width: 768px) {
  .timeline {
    padding-left: size(16);

    &::before {
      left: size(4);
    }

    h3[class*='calendar-list-day-header'] {
      font-size: size(16);

      &::before {
        left: -size(16);
        width: size(8);
        height: size(8);
      }
    }

    ul[class*='calendar-list-events'] {
      margin-top: size(16);
      margin-bottom: size(32);

      li div[class*='calendar-list-event-item'] {
        flex-direction: column;
        align-items: flex-start;

        [class*='event-content'] {
          margin-left: 0;
          flex-direction: column;
          margin-top: size(12);
          width: 100%;
        }

        [class*='event-image'] {
          width: 100%;
          height: auto;
        }

        h6 {
          font-size: size(14);
          line-height: size(10);
        }

        div[class*='booking-status'] {
          flex-direction: column;
          align-items: flex-start;
          margin-left: 0;
          margin-top: size(16);
        }
      }

      button[class*='calendar-list-view-all-button'],
      button[class*='event-actions'] {
        width: 100%;
        font-size: size(14);
        padding: size(14) size(12);
        margin-left: 0;
      }
    }

    &.monthlyView,
    &.weeklyView,
    &.dailyView {
      ul[class*='calendar-list-events'] {
        li {
          padding: size(12);
          border-radius: size(8);

          div[class*='calendar-list-event-item'] {
            flex-direction: column;

            [class*='event-image'] {
              width: 100%;
              height: auto;
            }

            [class*='event-content'] {
              margin-left: 0;
              margin-top: size(10);
              justify-content: flex-start;
              flex-direction: column;
            }

            h6 {
              margin: size(8) 0;
            }

            [class*='event-details'] {
              flex-direction: column;
              align-items: flex-start;
              gap: size(8);
              margin-left: 0;

              [class*='event-time'] {
                margin: 0;
              }

              [class*='event-location'] {
                padding-left: 0;

                &::before {
                  display: none;
                }
              }
            }
          }
        }
      }
    }
  }
}
