@use 'mixins' as *;

.addToListDialog {
  display: none;
}

.addToListDialogOpened {
  border: 0;
  border-radius: size(24);
  display: flex;
  flex-direction: column;
  margin: auto;
  height: size(600);
  @include forMarginPadding(padding, size(24), size(24), size(24), size(24));
  width: size(600);

  .addToListModalListContainer {
    overflow-y: auto;
  }
  .addToListModalListContainer.hasScrollBar {
    padding-right: size(8);
  }

  .closeModalButton {
    @include forMarginPadding(margin, size(24), size(0), size(0), size(0));
  }
}

.addToListModalHeader {
  .addToListModalHeaderCloseContainer {
    display: flex;
    justify-content: flex-end;

    .closeAddToListModalButton {
      display: flex;
      align-items: center;
      justify-content: center;
      justify-self: flex-end;
      background-color: transparent;
      border: 1px solid var(--grey-900);
      border-radius: 50%;
      cursor: pointer;
      height: size(30);
      width: size(30);
      min-height: size(30);
      min-width: size(30);

      svg {
        fill: var(--grey-900);
        height: size(12);
        width: size(12);
      }
    }
  }

  .addToListModalTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    @include forMarginPadding(padding, size(14), size(0), size(24), size(0));

    h3 {
      margin-bottom: 0;
      font-size: size(28);
    }

    .createListButtonContainer {
      i {
        width: size(20);
        height: size(20);
        vertical-align: middle;

        svg {
          width: size(10);
          height: size(10);
          fill: var(--text-link-blue);
        }
      }

      .createListButton {
        width: 15%;
        @include forMarginPadding(padding, size(0), size(0), size(0), size(8));
        font-size: size(16);
        color: var(--text-link-blue);
        text-decoration: underline;
      }

      &:hover {
        cursor: pointer;
        .createListButton {
          text-decoration: none;
        }
      }
    }
  }
}
