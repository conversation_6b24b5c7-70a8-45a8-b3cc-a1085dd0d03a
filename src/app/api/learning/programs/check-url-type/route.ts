import { ErrorResponse } from '@/utils/fetcher';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const url = request.nextUrl.searchParams.get('url');

  if (!url || typeof url !== 'string') {
    return NextResponse.json({
      error: 'Invalid URL!',
    });
  }

  try {
    const response = await fetch(url as string, { method: 'HEAD' });
    const contentType = response.headers.get('Content-Type');

    if (contentType?.includes('image')) {
      return NextResponse.json({ type: 'image' });
    } else if (contentType?.includes('pdf')) {
      return NextResponse.json({ type: 'pdf' });
    } else {
      return NextResponse.json({ type: 'Unknown type' });
    }
  } catch (error) {
    return ErrorResponse(error);
  }
}
