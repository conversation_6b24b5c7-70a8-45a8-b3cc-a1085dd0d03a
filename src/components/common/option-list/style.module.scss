@use 'mixins' as *;

.optionList {
  list-style-type: none;
  .optionListItem {
    display: flex;
    justify-content: space-between;
    @include forMarginPadding(padding, size(18), size(0), size(18), size(0));
    border-bottom: solid 1px var(--grey-400);
    .image {
      img {
        width: size(24);
        height: size(24);
        @include forMarginPadding(margin, size(0), size(12), size(0), size(0));
      }
    }
    div {
      display: flex;
    }
    .optionContentContainer {
      .leftIcon {
        display: block;
        width: size(20);
        height: size(20);
        @include forMarginPadding(margin, size(0), size(12), size(0), size(0));
        fill: var(--grey-900);

        svg {
          width: 100%;
          height: 100%;
          vertical-align: middle;
          fill-rule: evenodd;
        }
      }
      .rightIcon {
        display: block;
        width: size(20);
        height: size(20);
        fill: var(--grey-900);

        svg {
          width: 100%;
          height: 100%;
          vertical-align: middle;
          fill-rule: evenodd;
        }
      }
    }
    a {
      width: 100%;
      display: flex;
      justify-content: space-between;
      .optionListItem {
        display: flex;
        justify-content: space-between;
        @include forMarginPadding(
          padding,
          size(18),
          size(0),
          size(18),
          size(0)
        );
        border-bottom: solid 1px var(--grey-400);
        .image {
          img {
            width: size(24);
            height: size(24);
            @include forMarginPadding(
              margin,
              size(0),
              size(12),
              size(0),
              size(0)
            );
          }
        }
        div {
          display: flex;
        }
        .optionContentContainer {
          .leftIcon {
            display: block;
            width: size(20);
            height: size(20);
            @include forMarginPadding(
              margin,
              size(0),
              size(12),
              size(0),
              size(0)
            );
            fill: var(--grey-900);

            svg {
              width: 100%;
              height: 100%;
              vertical-align: middle;
            }
          }
          .rightIcon {
            display: block;
            width: size(20);
            height: size(20);
            fill: var(--grey-900);

            svg {
              width: 100%;
              height: 100%;
              vertical-align: middle;
            }
          }
        }
      }
    }
  }
  .optionListItem:hover {
    cursor: pointer;
  }
  .removeDivider {
    border: none;
  }
}
.viewAllContainer {
  @include forMarginPadding(margin, size(18), size(0), size(0), size(0));
  span {
    display: flex;
    text-decoration: underline;
    span {
      .icon {
        display: block;
        margin-left: 6px;
        width: size(16);
        height: size(16);
        fill: var(--grey-900);

        svg {
          width: 100%;
          height: 100%;
          vertical-align: middle;
        }
      }
    }
    span:hover {
      cursor: pointer;
    }
  }
}
