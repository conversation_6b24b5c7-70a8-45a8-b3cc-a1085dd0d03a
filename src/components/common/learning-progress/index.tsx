import Paper from '@/ui/paper';
import LinearProgress from '@/ui/progress/linear';
import Typography from '@/ui/typography';

// Styles
import styles from './style.module.scss';
import useLearningPathwaysStore from '@/stores/learning/pathways';
import { useEffect } from 'react';
import dictionaries from '@/dictionaries';
import { useLocale, useTranslations } from 'next-intl';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '../loading';

const LearningProgress = () => {
  // Getting method to fetch progress
  const fetch = useLearningPathwaysStore((x) => x.fetchProgress);
  const t = useTranslations();
  // Getting progress
  const progress = useLearningPathwaysStore((x) => x.progress);

  const locale = useLocale();

  // Getting progress loading state
  const isFetchingProgress = useLearningPathwaysStore(
    (x) => x.isFetchingProgress,
  );

  const renderNumberOfCourses =
    progress?.completedCourses !== undefined &&
    progress?.totalCourses !== undefined;

  // Fetch progress
  useEffect(() => {
    fetch();
  }, [fetch]);

  // Return JSX
  return (
    <Paper
      variant="sidePanel"
      title={dictionaries.yourCurrentLearningProgress}
      titleVariant="small"
    >
      <WithLoader
        loader={<Loading />}
        loading={isFetchingProgress !== null && isFetchingProgress}
      >
        <Typography as="h4" className={styles.percent}>
          {Math.round(progress?.progress || 0)}%
        </Typography>
        <LinearProgress progress={progress?.progress || 0} height={6} border />
        {renderNumberOfCourses &&
          (locale === 'en' ? (
            <Typography as="p" className={styles.numbers}>
              {progress?.completedCourses || 0} {t(dictionaries.outOf)}{' '}
              {progress?.totalCourses || 0}{' '}
              {t(dictionaries.requiredTrainingCoursesCompleted)}{' '}
            </Typography>
          ) : (
            <Typography as="p" className={styles.numbers}>
              {progress?.completedCourses || 0} {t(dictionaries.outOf)}{' '}
              {progress?.totalCourses || 0}{' '}
              {t(dictionaries.requiredTrainingCoursesCompleted)}{' '}
            </Typography>
          ))}
      </WithLoader>
    </Paper>
  );
};

export default LearningProgress;
