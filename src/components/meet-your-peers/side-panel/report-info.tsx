import styles from './style.module.scss';
import Typography from '@/ui/typography';
import dictionary from '@/dictionaries';
import { Corporate } from '@/ui/icons';
import Image from 'next/image';
import profileImage from '@public/images/job-profile-image.jpg';
import DialogDrawer from '@/ui/dialog-drawer';
import { useState } from 'react';
import OrganizationChart from './organization-chart';

const ReportInfo = () => {
  const [openOrgCharts, setOpenOrgCharts] = useState(false);
  const viewOrganizationChart = () => {
    setOpenOrgCharts(true);
  };

  const closeOrgCharts = () => {
    setOpenOrgCharts(false);
  };
  return (
    <>
      <div className={styles.profileReportsInfoContainer}>
        <Typography as="h4" dictionary={dictionary.reportsTo} />
        <div className={styles.reportsWrapper}>
          <div className={styles.pic}>
            <Image
              src={profileImage}
              alt=""
              className="img-fluid"
              height={100}
              width={100}
            />
          </div>
          <div className={styles.data}>
            <Typography as="h4">Ma<PERSON>a <PERSON>y</Typography>
            <Typography as="p" className={styles.profileInfoIcon}>
              <Corporate />
              <span>Dept. of Government Enablement</span>
            </Typography>
          </div>
        </div>
      </div>
      <div className={styles.viewProfile}>
        <button
          className={styles.viewProfileButton}
          onClick={viewOrganizationChart}
        >
          <Typography dictionary={dictionary.showOrgChart} />
        </button>
      </div>
      <DialogDrawer isOpened={openOrgCharts} onClose={closeOrgCharts}>
        <OrganizationChart />
      </DialogDrawer>
    </>
  );
};
export default ReportInfo;
