@use 'mixins' as *;

.popup {
  top: 0;
  @include leftToRight(0);
  display: flex;
  align-items: center;
  justify-content: center;
}

.inner {
  width: size(500);
  @include borderRadius(var(--radius));
  background: var(--white);
  overflow: hidden;
  position: relative;
  z-index: 9;
}

.content {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  @include forMarginPadding(padding, size(40), size(25), size(40), size(25));
}

.criterias {
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  @include borderRadius(var(--radius));
  @include forMarginPadding(padding, size(25), size(30), size(25), size(30));
  border: solid 1px var(--grey-400);
}

.closeBtn {
  position: absolute;
  @include rightToLeft(size(10));
  top: size(10);
  width: size(30);
  height: size(30);
  color: var(--grey-900);
  background: none;
  border: none;
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  cursor: pointer;
  @include transitions(0.5s);

  i {
    width: size(20);
    height: size(28);

    svg {
      fill: var(--grey-900);
      @include transitions(0.5s);
    }
  }
}
.helpTxt {
  font-size: size(16);
  font-weight: 600;
  line-height: size(12);
  display: block;
  margin-bottom: size(20);
  &:hover {
    text-decoration: underline;
  }
}
.badge {
  width: size(150);
  height: size(150);
  @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
  @include borderRadius(50%);
  overflow: hidden;
  background: var(--grey-500);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: size(20);

  img {
    width: 100%;
  }
}

.badgeTitle {
  @include forMarginPadding(margin, size(0), size(0), size(30), size(0));

  h4 {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    text-transform: uppercase;
  }
}

.detail {
  margin-bottom: size(20);

  width: 100%;

  &:last-of-type {
    margin-bottom: size(0);
  }
}

.title {
  display: flex;
  align-items: center;
  @include forMarginPadding(margin, size(0), size(0), size(10), size(0));

  p {
    @include forMarginPadding(margin, size(0), size(5), size(0), size(0));

    + p {
      font-weight: 700;
      @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    }
  }
}

.listPoints {
  ol {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
    counter-reset: listCounter;

    li {
      @include forMarginPadding(padding, size(0), size(0), size(30), size(40));
      position: relative;
      list-style: none;
      counter-increment: listCounter;
      border-bottom: solid 1px var(--grey-500);

      &:before {
        content: counter(listCounter);
        position: absolute;
        @include leftToRight(0);
        top: 0;
        width: size(20);
        height: size(20);
        font-size: size(12);
        display: flex;
        align-items: center;
        justify-content: center;
        @include borderRadius(50%);
        background: var(--text-link-blue);
        overflow: hidden;
        color: var(--white);
      }

      + li {
        @include forMarginPadding(
          padding,
          size(40),
          size(0),
          size(30),
          size(40)
        );

        &:before {
          top: size(40);
        }
      }

      &:last-child {
        @include forMarginPadding(
          padding,
          size(40),
          size(0),
          size(0),
          size(40)
        );
        border: none;
      }

      &:first-child {
        @include forMarginPadding(
          padding,
          size(0),
          size(0),
          size(30),
          size(40)
        );
      }
    }
  }
}

.orTxt {
  position: absolute;
  @include leftToRight(0);
  @include rightToLeft(0);
  bottom: size(-15);
  @include forMarginPadding(margin, auto, auto, auto, auto);
  font-size: size(11);
  width: size(30);
  height: size(30);
  display: flex;
  justify-content: center;
  align-items: center;
  border: solid 1px var(--grey-400);
  @include borderRadius(50%);
  z-index: 1;
  background: var(--white);
}

.andTxt {
  position: absolute;
  @include leftToRight(0);
  @include rightToLeft(0);
  bottom: size(-15);
  @include forMarginPadding(margin, auto, auto, auto, auto);
  @include forMarginPadding(padding, size(10), size(6), size(10), size(6));
  justify-self: anchor-center;
  font-size: size(11);
  width: fit-content;
  min-width: size(30);
  height: size(30);
  display: flex;
  justify-content: center;
  align-items: center;
  border: solid 1px var(--grey-400);
  @include borderRadius(24px);
  z-index: 1;
  background: var(--white);
}

.awardOperator {
  @include forMarginPadding(padding, size(10), size(0), size(30), size(0));
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  .awardOperatorTxt {
    justify-self: anchor-center;
    font-size: size(11);

    display: flex;
    justify-content: center;
    align-items: center;
    border: solid 1px var(--grey-400);
    background: var(--white);
    min-width: fit-content;
    height: size(30);
    @include borderRadius(24px);
    @include forMarginPadding(padding, size(10), size(6), size(10), size(6));
    width: fit-content;
  }
  .awardOperatorLine {
    height: 1px;
    width: 100%;
    background-color: var(--grey-500);
  }
}

@include hover() {
  .closeBtn {
    &:hover {
      color: var(--gun-smoke-900);

      svg {
        fill: var(--gun-smoke-900);
      }
    }
  }
}

@include for-all-phone() {
  .popup {
    align-items: flex-end;
  }

  .inner {
    min-width: initial;
    min-height: size(300);
    -webkit-border-top-left-radius: 16px;
    -webkit-border-top-right-radius: 16px;
    -moz-border-radius-topleft: 16px;
    -moz-border-radius-topright: 16px;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;

    -webkit-border-bottom-right-radius: 0;
    -webkit-border-bottom-left-radius: 0;
    -moz-border-radius-bottomright: 0;
    -moz-border-radius-bottomleft: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    width: 100%;
  }

  .badge {
    width: size(118);
    height: size(118);
  }

  .badgeTitle {
    h4 {
      font-size: size(16);
      font-weight: 700;
    }
  }

  .detail {
    @include forMarginPadding(padding, size(20), size(20), size(20), size(20));

    h5 {
      font-size: size(16);
    }
  }

  .title {
    p {
      font-size: size(14);
    }
  }
}
