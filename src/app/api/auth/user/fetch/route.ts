import { NextResponse } from 'next/server';
import {
  ExternalFetchUserProgressService,
  ExternalFetchUserService,
} from '@/services/external/user';
import { getUserId } from '@/utils/auth';
import { AxiosError } from 'axios';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchProfileService } from '@/services/totara/profile';

/**
 * Method to get the logged in
 * user profile
 */
export async function GET() {
  try {
    // Getting user id
    const id = await getUserId();
    //Getting user form liferay
    const response = await ExternalFetchUserService(id || '');
    // Getting user profile progress
    const progress = await ExternalFetchUserProgressService(id || '');
    // Getting totara user profile
    const totaraUser = await TotaraFetchProfileService();
    // Return the response
    return NextResponse.json({
      user: response?.data?.items?.[0],
      totaraProfile: totaraUser?.data,
      progress: progress?.data,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error as AxiosError);
  }
}
