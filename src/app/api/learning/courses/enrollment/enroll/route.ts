import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import serverFetcher from '@/utils/fetcher/server-fetcher';

const query = `
  mutation mobile_findlearning_attempt_self_enrolment(
    $input: core_enrol_attempt_self_enrolment_input!
  ) {
    mobile_findlearning_enrolment_result: core_enrol_attempt_self_enrolment(input: $input) {
      success
      msgKey: msg_key
      __typename
    }
  }
`;

// Route to get the favorite courses
export async function POST(request: NextRequest) {
  try {
    // Getting request data
    const data = await request.json();

    // Fetch the data
    const response = await serverFetcher.query({
      operationName: 'mobile_findlearning_attempt_self_enrolment',
      query: query,
      variables: data,
    });

    // Return the response
    return NextResponse.json({
      ...response?.data?.data,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
