import CONFIG from '@/config';
import { FC, ReactNode, useMemo } from 'react';
import { Link as I18NLink } from '@/i18n/routing';
import ICourse from '@/types/domain/learning/course';

interface ILink {
  course: ICourse;
  className?: string;
  children?: ReactNode;
  additionalOnClick?: () => void;
}

// React component to handle course link
const Link: FC<ILink> = (props) => {
  // Deconstructing props
  const { className, course, children, additionalOnClick } = props;

  // Getting course id
  const id = useMemo(() => {
    return course?.id || course?.courseid || '';
  }, [course?.id, course?.courseid]);

  // Getting course type
  const type = useMemo(() => {
    return course?.type || course?.component_type || '';
  }, [course?.component_type, course?.type]);

  // Creating detail link based on type
  const href = useMemo(() => {
    // If type is program
    if (type.toUpperCase() === 'PROGRAM') {
      return CONFIG.routes.program.replace('{id}', id as string);
    }
    // If type is anything else
    return CONFIG.routes.course.replace('{id}', id as string);
  }, [type, id]);

  // Return JSX
  return (
    <I18NLink
      href={href}
      aria-label={course?.title}
      className={className}
      onClick={additionalOnClick}
    >
      {children}
    </I18NLink>
  );
};

// Exporting course component
export default Link;
