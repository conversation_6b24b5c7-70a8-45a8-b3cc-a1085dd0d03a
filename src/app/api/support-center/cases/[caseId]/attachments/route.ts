import { addAttachment } from '@/services/external/support-center';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';
import { NextRequest, NextResponse } from 'next/server';

// Server side api that adds the attachment.
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ caseId: string }> },
) {
  try {
    const data = await request.json();
    const { caseId: caseNumber } = await params;

    const response = await addAttachment({
      ...data,
      caseNumber,
    });

    return NextResponse.json({
      data: response.data,
    });
  } catch (error) {
    return ErrorResponse(error as AxiosError);
  }
}
