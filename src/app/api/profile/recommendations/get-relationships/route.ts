import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';
import { ExternalFetchPicklistService } from '@/services/external/picklist';

/**
 * Route to get the learning types
 */
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const id = request.nextUrl.searchParams.get('id') || '';
    // Fetching the picklist
    const response = await ExternalFetchPicklistService(id);
    // Getting entries
    const entries = response?.data?.items?.[0]?.listTypeEntries || [];
    // Return the response
    return NextResponse.json({ entries });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error as AxiosError);
  }
}
