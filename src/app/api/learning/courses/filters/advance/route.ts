import { TotaraFetchCoursesFiltersService } from '@/services/totara/courses';
import { ErrorResponse } from '@/utils/fetcher';
import { NextResponse } from 'next/server';

/**
 * Route to get the popular searches
 */
export async function GET() {
  try {
    // Fetching data
    const response = await TotaraFetchCoursesFiltersService();
    // Return the response
    return NextResponse.json({
      types: JSON.parse(response?.data?.filters?.learningtype || '[]'),
      products: JSON.parse(response?.data?.filters?.product || '[]'),
      durations: JSON.parse(response?.data?.filters?.duration || '[]'),
      providers: JSON.parse(response?.data?.filters?.coursecreator || '[]'),
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
