@use 'mixins' as *;

.filter {
  + .filter {
    @include forMarginPadding(margin, size(20), size(0), size(0), size(0));
  }

  h4 {
    @include forMarginPadding(margin, size(0), size(0), size(20), size(0));
    @include forMarginPadding(padding, size(0), size(0), size(10), size(0));
    font-size: size(22);
    font-weight: 400;
    line-height: 1.2;
    color: var(--grey-800);
  }

  ul {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
    display: flex;
    flex-wrap: wrap;

    li {
      list-style: none;
      @include forMarginPadding(margin, size(0), size(10), size(10), size(0));
    }
  }
}
