import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchRecentSearchesService } from '@/services/totara/courses';

/**
 * Route to get the popular searches
 */
export async function GET() {
  try {
    // Fetching data
    const response = await TotaraFetchRecentSearchesService();

    // Return the response
    return NextResponse.json({
      list: response?.data?.popular_search,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
