import { FC, useCallback } from 'react';
import Image from 'next/image';
import Typography from '@/ui/typography';
import star from '@public/images/star.svg';
import styles from './style.module.scss';

interface IItem {
  coachId?: string;
  title?: string;
  image?: string;
  designation?: string;
  rating?: string;
  ratingCount?: string;
  onClick?: (id: string) => void;
}

const Item: FC<IItem> = (props) => {
  // Deconstruct props
  const { coachId, image, title, designation, rating, ratingCount, onClick } =
    props;

  /**
   * Method to handle click on item
   */
  const handleClick = useCallback(() => {
    // Check if onClick is defined
    if (onClick) {
      // Call onClick
      onClick(coachId || '');
    }
  }, [onClick, coachId]);

  // Return JSX
  return (
    <div className={styles.profile} onClick={handleClick}>
      <div className={styles.pic}>
        <img
          src={image}
          alt=""
          className="img-fluid"
          height={100}
          width={100}
        />
      </div>
      <div className={styles.data}>
        <div className={styles.title}>
          <span>{title}</span>
        </div>
        <Typography as="p" className={styles.designation}>
          {designation}
        </Typography>
        {Number(rating) != 0 && rating ? (
          <div className={styles.rating}>
            <span className={styles.starRating}>
              <Image src={star} alt="" className="img-fluid" />
            </span>

            <Typography as="span" className={styles.ratingText}>
              {`${rating ? rating : 0} (${ratingCount})`}
            </Typography>
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default Item;
