@use 'mixins' as *;

@use 'mixins' as *;

.availableSeatsWrapper {
  background-color: var(--bg-shade-4);
  border-radius: size(12);
  padding: size(10);
  margin-bottom: size(16);
  max-width: size(336);
  width: 100%;
}

.availableSeatsHeader {
  align-items: center;
  display: flex;
  gap: size(8);

  p {
    margin: 0;
  }
}

.availableSeatsIconWrapper {
  align-items: center;
  border: 1px solid var(--disabled);
  border-radius: 50%;
  display: flex;
  height: size(32);
  justify-content: center;
  padding: size(5);
  width: size(32);

  svg {
    fill: var(--grey-900);
    height: size(17);
    width: size(17);
  }
}

@include for-dark-theme {
  .availableSeatsWrapper {
    background-color: var(--bg-card-1);
  }

  .availableSeatsIconWrapper {
    border: 1px solid var(--grey-500);
  }

  .availableSeatsText {
    opacity: 0.8;
  }
}

.dateText {
  font-size: size(14);
}
