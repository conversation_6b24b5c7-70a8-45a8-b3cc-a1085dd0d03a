import { ChangeEvent, useCallback, useState } from 'react';
import Typography from '@/ui/typography';

// Styles
import styles from './style.module.scss';
import dictionaries from '@/dictionaries';
import InputField from '@/ui/form/input-field';
import { Close, SearchIcon } from '@/ui/icons';

const Entity = () => {
  const [searchTerm, setSearchTerm] = useState<string>('');

  const clear = useCallback(() => {
    setSearchTerm('');
  }, []);

  const onChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.currentTarget.value);
  }, []);

  // Return JSX
  return (
    <nav className={styles.filter}>
      <Typography as="h4" dictionary={dictionaries.byEntity} />
      <div className={styles.searchInput}>
        <InputField
          placeholder={dictionaries.searchEntity}
          ariaLabel="search"
          value={searchTerm || ''}
          onChange={onChange}
        />
        <button className={styles.searchButton} onClick={clear}>
          <i>
            {searchTerm && searchTerm?.length > 0 ? <Close /> : <SearchIcon />}
          </i>
        </button>
      </div>
    </nav>
  );
};

export default Entity;
