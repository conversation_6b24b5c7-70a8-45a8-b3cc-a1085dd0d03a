@use 'mixins' as *;

.container {
  border-left: 1px solid var(--grey-700);
  overflow: visible;

  @include rtl {
    border-left: none;
    border-right: 1px solid var(--grey-700);
  }

  &:not(:first-of-type) {
    @include forMarginPadding(padding, size(30), size(0), size(0), size(30));
    @include forMarginPadding(margin, size(0), size(0), size(0), size(30));
  }

  &:first-of-type {
    @include forMarginPadding(padding, size(0), size(0), size(0), size(30));
    @include forMarginPadding(margin, size(30), size(0), size(0), size(30));
  }

  .inner {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid var(--grey-700);
    position: relative;

    & > div {
      @include forMarginPadding(padding, size(0), size(0), size(30), size(0));

      & > span {
        color: var(--grey-700);
      }
    }

    & h5::before {
      content: '';
      width: size(30);
      height: size(30);
      @include borderRadius(50%);
      border: 1px solid var(--grey-700);
      position: absolute;
      top: size(-2);
      left: size(-48);
      background: var(--white);

      @include for-all-phone() {
        top: size(-8);
      }

      @include rtl {
        right: size(-48);
        left: auto;
      }
    }
  }

  .duration {
    align-self: center;
    @include forMarginPadding(padding, size(0), size(0), size(30), size(0));
    color: var(--grey-700);
  }
}
