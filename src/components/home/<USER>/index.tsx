import { useEffect, useState } from 'react';
import CONFIG from '@/config';
import Paper from '@/ui/paper';
import Card from '@/ui/cards/announcement';
import WithLoader from '@/ui/skeleton/with-loader';
import IAnnouncement from '@/types/domain/learning/announcement';
import useLearningAnnouncementsStore from '@/stores/learning/announcements';
import Loading from '@/components/common/loading';
import EmptyMessage from '@/ui/empty-message';
import AnnouncementDetail from '@/components/announcements/detail';
import DialogDrawer from '@/ui/dialog-drawer';
import dictionary from '@/dictionaries';
import styles from './style.module.scss';
import { motion, cubicBezier } from 'motion/react';

const Announcements = () => {
  const [isDialogOpened, setIsDialogOpened] = useState(false);
  const [announcementId, setAnnouncementId] = useState<string>('');
  const [isDialogContentVisible, setIsDialogContentVisible] =
    useState<boolean>(false);
  // Getting fetch action
  const onFetch = useLearningAnnouncementsStore((x) => x.fetch);

  // Getting fetching state
  const isFetching = useLearningAnnouncementsStore((x) => x.isFetching);

  // Geting the announcements
  const announcements = useLearningAnnouncementsStore((x) => x.list);

  // Has more announcements
  const hasMore = announcements?.length > 3;

  const handleDialogOpen = (id: string) => {
    setIsDialogContentVisible(true);
    setIsDialogOpened(true);
    setAnnouncementId(id);
  };

  const handleDialogClose = () => {
    setIsDialogOpened(false);
    setTimeout(function () {
      setIsDialogContentVisible(false);
    }, 400); // Dialog animation transition time.
  };

  // Effect to fetch the announcements
  useEffect(() => {
    // Fetch the announcements
    onFetch();
  }, [onFetch]);
  // Return JSX
  return (
    <>
      <Paper
        title={dictionary.announcements}
        linkTitle={hasMore && !isFetching ? dictionary.viewAll : ''}
        link={hasMore ? CONFIG.routes.announcements.list : ''}
        className={styles.announcementsPaper}
      >
        <WithLoader
          loader={<Loading className={styles.indentLoading} />}
          loading={isFetching}
        >
          <div>
            {announcements
              ?.slice(0, 3)
              ?.map((announcement: IAnnouncement, i) => {
                const delay = i % 10;
                return (
                  <motion.div
                    key={announcement?.id}
                    initial={{
                      opacity: 0,
                      y: 5,
                      position: 'relative',
                    }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{
                      delay: delay * 0.06,
                      ease: cubicBezier(0.71, 0.23, 0.31, 0.86),
                      duration: 0.4,
                    }}
                    className={styles.announcementItem}
                  >
                    <Card
                      key={announcement?.id}
                      announcement={announcement}
                      onClick={() => handleDialogOpen(announcement?.id)}
                    />
                  </motion.div>
                );
              })}
            {!announcements?.length && !isFetching && (
              <EmptyMessage
                icon="notification"
                title={dictionary.noAnnouncements}
                description={dictionary.thereAreNoNewAnnouncements}
              />
            )}
          </div>
        </WithLoader>
      </Paper>
      <DialogDrawer isOpened={isDialogOpened} onClose={handleDialogClose}>
        {isDialogContentVisible ? (
          <div className="dialog-content-no-cta">
            <AnnouncementDetail id={announcementId} />
          </div>
        ) : null}
      </DialogDrawer>
    </>
  );
};

export default Announcements;
