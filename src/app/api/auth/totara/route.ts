import { NextRequest, NextResponse } from 'next/server';
import { TotaraAuthService } from '@/services/totara/totara-auth';
import { ErrorResponse } from '@/utils/fetcher';

/**
 * Method to get the logged in
 * user profile
 */
export async function GET(request: NextRequest) {
  const idp = request.nextUrl.searchParams.get('idp');

  try {
    const response = await TotaraAuthService(idp as string);

    // Return the response
    return NextResponse.json({
      loginUrl: response.loginurl,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error);
  }
}
