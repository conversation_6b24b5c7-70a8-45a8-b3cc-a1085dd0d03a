import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchAnnouncementsService } from '@/services/totara/announcements';

/**
 * Route to get the announcements
 */
export async function GET() {
  try {
    // Updating user profile
    const response = await TotaraFetchAnnouncementsService();
    // Return the response
    return NextResponse.json({
      total: response?.data?.total_posts || 0,
      list: response?.data?.records || [],
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
