@use 'mixins' as *;

.dayWithSlots {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: size(10);

  border: size(1) solid var(--progressBg);
  @include borderRadius(size(12));
  @include forMarginPadding(padding, size(15), size(13), size(15), size(13));

  width: size(80);
  height: size(90);

  cursor: pointer;

  .day {
    font-size: size(12);
    line-height: 1.2;
  }

  .date {
    font-size: size(16);
    font-weight: 700;
  }

  .slotCount {
    font-size: size(12);
  }
}

.isSelected {
  border-color: var(--tag-blue);
}
