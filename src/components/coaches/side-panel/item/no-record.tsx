import Typography from '@/ui/typography';
import { FC } from 'react';
import NoRecordNew from '@public/images/no-record-new.png';
import NoRecordNewDark from '@public/images/no-record-new-dark.png';

// Styles
import styles from '../style.module.scss';
import Image from 'next/image';
import { useTheme } from 'next-themes';

interface INoRecord {
  text?: string;
  subText?: string;
}
const NoRecord: FC<INoRecord> = (props) => {
  const { theme } = useTheme();
  const { text, subText } = props;
  // Return JSX
  return (
    <div className={styles.noRecord}>
      <div className={styles.noImage}>
        {theme === 'light' ? (
          <Image src={NoRecordNew} alt="No Record Found" />
        ) : (
          <Image src={NoRecordNewDark} alt="No Record Found" />
        )}
      </div>
      <Typography as="span" className={styles.heading}>
        {text}
      </Typography>
      <Typography as="span" className={styles.text}>
        {subText}
      </Typography>
    </div>
  );
};

export default NoRecord;
