import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraCommunityDetailsUploadPostImageService } from '@/services/totara/my-network';

/**
 * Route to upload image in POST in community
 */
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();

    const file = formData.get('file');
    // Check if a file is received
    if (!file) {
      // If no file is received, return a JSON response with an error and a 400 status code
      return NextResponse.json(
        { error: 'No files received.' },
        { status: 400 },
      );
    }

    // call API to upload image in community POST
    const response =
      await TotaraCommunityDetailsUploadPostImageService(formData);
    // Return the response
    return NextResponse.json({
      success: response.data.success,
      message: response.data.message,
      image: response.data[0],
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error);
  }
}
