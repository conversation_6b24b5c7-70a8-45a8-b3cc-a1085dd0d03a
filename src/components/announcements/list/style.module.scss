@use 'mixins' as *;

div.announcementsPaper {
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));

  div[class*='head'] {
    margin-bottom: size(14);
    @include forMarginPadding(padding, size(0), size(25), size(0), size(25));
  }
}

div.indentLoading {
  min-height: size(771);
  @include forMarginPadding(margin, size(0), size(0), size(200), size(0));
}

.announcementItem {
  &:first-child {
    > div[class*='item'] {
      &::before {
        display: none;
      }
    }
  }

  &:last-child {
    > div[class*='item'] {
      &::after {
        display: none;
      }
    }
  }
}

.showMoreWrapper {
  text-align: center;
  padding-top: size(24);
  width: 100%;
}

.showMoreButton {
  background-color: transparent;
  border: 0;
  color: var(--text-link-blue);
  cursor: pointer;
}

.popup {
  position: fixed;
  top: 0;
  @include rightToLeft(0);
  width: 100%;
  height: 100vh;
  z-index: 11;
}

.inner {
  width: size(580);
  height: 100vh;
  position: relative;
  z-index: 9;
  background: var(--white);
  margin-inline-start: auto;

  > div[class*='skeleton'] {
    z-index: 3;
  }
}

@include for-all-phone() {
  .inner {
    width: 100%;
  }
}

.modalHeader {
  display: flex;
  justify-content: flex-end;
  height: size(72);
  position: relative;
  padding: size(20);
}

.modalBody {
  width: 100%;
  @include forMarginPadding(padding, size(0), size(24), size(24), size(24));
  overflow-y: auto;
  height: calc(100vh - size(72));
}

.backBtn {
  @include leftToRight(size(24));
  top: size(24);
  width: size(30);
  height: size(30);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 7;
  background: var(--white);
  border: 1px solid var(--grey-900);
  @include borderRadius(50%);
  cursor: pointer;
  @include transitions(0.5s);
  fill: var(--grey-900);

  i {
    width: size(20);
    height: size(20);
    display: flex;
    padding: size(5);

    svg {
      fill-rule: evenodd;
      width: 100%;
      height: 100%;
    }
  }
}

@include hover() {
  .backBtn {
    &:hover {
      background: var(--grey-900);
      fill: var(--white);
    }
  }
}
