@use 'mixins' as *;

.monthlyView.calendar {
  display: grid;
  grid-template-columns: repeat(7, calc(100% / 7));

  div[class*='day-name'] {
    padding: size(18) size(8);
    font-size: size(14);
    line-height: size(10);
    color: var(--grey-900);
    background-color: var(--grey-300);
    border: size(1) solid var(--grey-400);
    border-left: none;
  }
  div[class*='first-week-day'] {
    border-top-left-radius: size(9);
    border-left: size(1) solid var(--grey-400);
  }
  div[class*='first-week-day'][class*='rtl'] {
    border-top-right-radius: size(9);
    border-right: size(1) solid var(--grey-400);
  }

  div[class*='last-week-day'] {
    border-top-right-radius: size(9);
  }
  div[class*='last-week-day'][class*='rtl'] {
    border-top-left-radius: size(9);
  }

  div[class*='box'] {
    height: size(150);
    padding: size(14) size(8) 0 size(8);
    box-shadow: inset -1px -1px 0px #e0e0e0;
    display: flex;
    flex-direction: column;

    &:nth-of-type(7n + 1) {
      border-left: size(1) solid var(--grey-400);
    }

    span[class*='day-number'] {
      font-size: size(10);
      line-height: size(7);
      font-weight: 700;
    }

    div[class*='day-data'] {
      position: relative;
      flex: 1;
      div[class*='event-item'] {
        margin-top: size(6);
        background-color: var(--bg-shade-5);
        padding: size(9.5) size(6);
        border: size(1) solid #74bbff;
        border-radius: size(4);
        border-left-width: size(3);
        position: relative;

        p {
          font-size: size(10);
          line-height: size(7);
          color: var(--grey-900);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 0;
        }
        span {
          font-size: size(10);
          line-height: size(7);
          color: var(--grey-900);
        }
        span[class*='start-time'] {
          margin-right: size(6);
          color: var(--grey-800);
        }
      }
      div[class*='more-events'] {
        position: absolute;
        bottom: 0;
        right: 0;
        padding: size(7) size(4);
        background-color: #c9e0f6;
        border-radius: size(4) size(4) 0 0;
        color: var(--text-link-blue);
        font-size: size(10);
        line-height: size(7);
      }
    }
  }

  div[class*='disable'] {
    span[class*='day-number'] {
      color: var(--grey-600);
    }
  }

  [class*='eventTooltip'] {
    width: size(350);
  }
}

.dailyView.calendar {
  div[class*='daily-grid-container'] {
    display: grid;
    grid-template-columns: auto 1fr;
  }
  div[class*='daily-grid-corner-empty'],
  div[class*='daily-day-header'] {
    background-color: var(--grey-300);
  }

  div[class*='daily-grid-corner-empty'] {
    border-left: size(1) solid var(--grey-400);
    border-top: size(1) solid var(--grey-400);
    border-bottom: size(1) dashed var(--grey-400);
    border-top-left-radius: size(9);
  }

  div[class*='daily-day-header'] {
    padding-top: size(18);
    border-right: size(1) solid var(--grey-400);
    border-top: size(1) solid var(--grey-400);
    border-bottom: size(1) dashed var(--grey-400);
    border-top-right-radius: size(9);
    div {
      border-left: size(1) solid #e4e7eb;
      padding: size(0) size(0) size(36) size(8);
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: size(15);

      span[class*='week-day'] {
        font-size: size(14);
        line-height: size(10);
        color: var(--tag-blue);
        font-weight: 400;
      }

      span[class*='month-day'] {
        font-size: size(16);
        line-height: size(11);
        color: var(--text-link-blue);
        font-weight: 700;
      }
    }
  }

  div[class*='daily-hour-label'] {
    height: size(72);
    padding-top: size(8);
    width: size(83);
    text-align: center;
    font-size: size(12);
    line-height: size(12);
    color: var(--grey-800);
    border-left: size(1) solid var(--grey-400);
  }

  div[class*='last-hour'] {
    border-bottom: size(1) solid var(--grey-400);
  }

  div[class*='hour-slot'] {
    border-right: size(1) solid var(--grey-400);
    border-left: size(1) solid var(--grey-400);
    border-bottom: size(1) dashed var(--grey-400);
    position: relative;

    div[class*='current-time-indicator'] {
      position: absolute;
      top: 0;
      left: - size(15);
      width: calc(100% + size(15));
      height: size(2);
      background-color: var(--warning-900);
      z-index: 1;

      &::before {
        content: '';
        position: absolute;
        top: - size(1.5);
        left: 0;
        width: size(5);
        height: size(5);
        background-color: var(--warning-900);
        border-radius: 50%;
      }
    }
  }

  div[class*='hour-slot']:nth-last-of-type(1) {
    border-bottom-style: solid;
  }

  div[class*='has-event'] [class*='hour-events'] {
    position: relative;
    display: flex;
    flex-direction: row;
    gap: 2px;
    flex-wrap: wrap;

    [class*='event-item'] {
      position: relative;
      top: size(2);
      left: size(4);
      width: calc(100% - size(8));
      background-color: var(--bg-shade-5);
      border: size(1) solid #74bbff;
      border-left-width: size(3);
      border-radius: size(4);
      padding: size(12);
      display: flex;
      flex-direction: column;
      gap: size(12);

      span[class*='course-name'] {
        font-size: size(16);
        line-height: size(11);
      }

      div[class*='course-details'] {
        span {
          font-size: size(12);
          line-height: size(9);
          color: var(--grey-800);
        }
      }

      div[class*='course-details'][class*='row'] {
        display: flex;
        flex-direction: row;
        gap: size(12);

        span[class*='event-location'] {
          &:before {
            content: '•';
            width: size(6);
            height: size(6);
            color: var(--grey-900);
            margin-right: size(10);
          }
        }
      }

      div[class*='course-details'][class*='column'] {
        display: flex;
        flex-direction: column;
        gap: size(12);

        span[class*='event-location'] {
          &:before {
            display: none;
          }
        }
      }
    }
  }
}

.weeklyView.calendar {
  position: relative;
  div[class*='weekly-grid-container'] {
    display: grid;
    grid-template-columns: auto repeat(7, 1fr);
    border-bottom: size(1) solid var(--grey-400);

    div[class*='weekly-grid-corner-empty'],
    div[class*='weekly-day-header'] {
      background-color: var(--grey-300);
      border-top: size(1) solid var(--grey-400);
      border-bottom: size(1) dashed var(--grey-400);
    }

    div[class*='weekly-grid-corner-empty'] {
      border-top-left-radius: size(10);
      border-left: size(1) solid #e4e7eb;

      font-size: size(12);
      line-height: size(12);
      color: var(--grey-600);
      text-align: center;
      padding: size(56) size(13) 0 size(18);
    }

    div[class*='weekly-day-header'] {
      display: flex;
      flex-direction: column;
      span[class*='empty-space'] {
        height: size(18);
      }
      div {
        display: flex;
        flex-direction: column;
        gap: size(15);
        padding: size(0) size(0) size(36) size(8);
        border-left: size(1) solid #e4e7eb;

        span[class*='week-day'] {
          font-size: size(14);
          line-height: size(10);
          color: var(--grey-600);
          font-weight: 400;
        }

        span[class*='month-day'] {
          font-size: size(16);
          line-height: size(11);
          color: var(--grey-800);
          font-weight: 700;
        }
      }
    }

    div[class*='day-6'] {
      border-top-right-radius: size(10);
      border-right: size(1) solid #e4e7eb;
    }

    div[class*='weekly-hour-label'] {
      height: size(72);
      padding-top: size(8);
      width: size(83);
      text-align: center;
      font-size: size(12);
      line-height: size(12);
      color: var(--grey-800);
      border-left: size(1) solid var(--grey-400);

      div[class*='current-time-indicator'] {
        position: absolute;
        top: 0;
        left: size(70);
        width: calc(100% - size(70));
        height: size(2);
        background-color: var(--warning-900);
        z-index: 1;

        &::before {
          content: '';
          position: absolute;
          top: - size(1.5);
          left: 0;
          width: size(5);
          height: size(5);
          background-color: var(--warning-900);
          border-radius: 50%;
        }
      }
    }

    div[class*='weekly-hour-label']:last-of-type {
      border-bottom: size(1) solid var(--grey-400);
    }

    div[class*='last-hour'] {
      border-bottom: size(1) solid var(--grey-400);
    }

    div[class*='hour-slot'] {
      border-left: size(1) solid var(--grey-400);
      border-bottom: size(1) dashed var(--grey-400);
    }

    div[class*='hour-slot']:nth-of-type(8n) {
      border-right: size(1) solid var(--grey-400);
    }

    div[class*='hour-slot']:nth-last-of-type(7),
    div[class*='hour-slot']:nth-last-of-type(6),
    div[class*='hour-slot']:nth-last-of-type(5),
    div[class*='hour-slot']:nth-last-of-type(4),
    div[class*='hour-slot']:nth-last-of-type(3),
    div[class*='hour-slot']:nth-last-of-type(2),
    div[class*='hour-slot']:nth-last-of-type(1) {
      border-bottom: none;
    }

    div[class*='has-event'] [class*='hour-events'] {
      position: relative;
      display: flex;
      flex-direction: row;
      gap: 2px;
      flex-wrap: wrap;
      [class*='event-item'] {
        position: relative;
        top: size(2);
        left: size(4);
        width: calc(100% - size(8));
        background-color: var(--bg-shade-5);
        border: size(1) solid #74bbff;
        border-left-width: size(3);
        border-radius: size(4);
        padding: size(8);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        gap: size(12);

        span[class*='course-name'] {
          font-size: size(14);
          line-height: size(13);
          max-height: size(27);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: break-spaces;
        }

        div[class*='course-details'] {
          display: flex;
          flex-direction: column;
          gap: size(10);
          span {
            font-size: size(12);
            line-height: size(10);
            color: var(--grey-800);
          }
        }

        span[class*='start-time'] {
          font-size: size(12);
          line-height: size(9);
          color: var(--grey-800);
        }
      }

      [class*='event-item'][class*='event-duration-1'] {
        span[class*='course-name'] {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          overflow: hidden;
        }
      }

      [class*='event-item'][class*='event-duration-2'] {
        span[class*='course-name'],
        div[class*='course-details'] {
          max-height: calc(size(72) - size(5));
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: break-spaces;
        }
        div[class*='course-details'] span[class*='event-location'] {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          line-clamp: 3;
          overflow: hidden;
        }
      }

      [class*='event-item']:not([class*='event-duration-1']):not(
          [class*='event-duration-2']
        ) {
        span[class*='course-name'],
        div[class*='course-details'] {
          max-height: calc(50% - size(5));
        }
      }
    }
  }
}
@media screen and (max-width: 768px) {
  .monthlyView.calendar,
  .weeklyView.calendar,
  .dailyView.calendar {
    overflow-x: auto;
    overflow-y: hidden;
    width: 100%;

    .weekly-grid-container,
    .daily-grid-container,
    & {
      min-width: size(700);
    }
  }

  .monthlyView.calendar div[class*='box'] {
    height: size(120);
    padding: size(8) size(4) 0 size(4);

    span[class*='day-number'] {
      font-size: size(9);
    }

    div[class*='event-item'] {
      padding: size(6) size(4);

      p,
      span {
        font-size: size(9);
      }
    }

    div[class*='more-events'] {
      font-size: size(9);
      padding: size(5) size(4);
    }
  }

  .weeklyView.calendar div[class*='event-item'],
  .dailyView.calendar div[class*='event-item'] {
    padding: size(8);
    span[class*='course-name'] {
      font-size: size(13);
    }

    div[class*='course-details'] span {
      font-size: size(11);
    }
  }

  .weeklyView.calendar .weekly-grid-container,
  .dailyView.calendar .daily-grid-container {
    grid-template-columns: auto repeat(7, minmax(size(90), 1fr)); // tighter grid on mobile
  }

  .weeklyView.calendar div[class*='weekly-hour-label'],
  .dailyView.calendar div[class*='daily-hour-label'] {
    font-size: size(11);
    width: size(70);
  }

  .weeklyView.calendar div[class*='hour-slot'],
  .dailyView.calendar div[class*='hour-slot'] {
    min-width: size(90); // allow wrapping
  }

  .weeklyView.calendar div[class*='weekly-day-header'] div,
  .dailyView.calendar div[class*='daily-day-header'] div {
    padding: 0 size(6);
    gap: size(10);

    span[class*='week-day'] {
      font-size: size(12);
    }

    span[class*='month-day'] {
      font-size: size(13);
    }
  }
  .monthlyView.calendar,
  .weeklyView.calendar,
  .dailyView.calendar {
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;

    div[class*='weekly-grid-container'],
    div[class*='daily-grid-container'],
    & {
      min-width: 900px;
    }

    scrollbar-width: thin;
    scrollbar-color: var(--grey-400) transparent;

    &::-webkit-scrollbar {
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--grey-400);
      border-radius: 3px;
    }
  }
  .monthlyView.calendar {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    width: 100%;

    display: grid;
    grid-template-columns: repeat(7, minmax(100px, 1fr));
    min-width: 700px;
  }
}
