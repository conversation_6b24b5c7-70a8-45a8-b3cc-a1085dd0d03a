import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import serverFetcher from '@/utils/fetcher/server-fetcher';

const query = `
  query totara_mobile_course($courseid: core_id!, $guestpw: String, $language: String) {
    mobile_course: totara_mobile_course(
      courseid: $courseid
      guestpw: $guestpw
      language: $language
    ) {
      course(courseid: $courseid, language: $language) {
        id
        format
        fullname
        shortname
        summary(format: PLAIN)
        summaryformat
        startdate(format: ISO8601)
        enddate(format: ISO8601)
        lang
        image
        sections {
          id
          title
          available
          availablereason(format: PLAIN)
          summary(format: PLAIN)
          summaryformat
          data: modules {
            id
            instanceid
            modtype
            name
            available
            availablereason(format: PLAIN)
            viewurl
            completion
            completionstatus
            showdescription
            description(format: PLAIN)
            gradefinal
            gradepercentage
            descriptionformat
            __typename
          }
          __typename
        }
        criteriaaggregation
        criteria {
          id
          type
          typeaggregation
          criteria
          requirement
          status
          complete
          completiondate(format: ISO8601)
          __typename
        }
        showGrades: showgrades
        completionEnabled: completionenabled
        completion {
          id
          statuskey
          progress
          timecompleted(format: ISO8601)
          __typename
        }
        __typename
      }
      native: mobile_coursecompat
      imageSrc: mobile_image
      gradeFinal: formatted_gradefinal
      gradeMax: formatted_grademax
      __typename
    }
  }
`;

// Route to get the favorite courses
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const id = request.nextUrl.searchParams.get('id') || '';

    // Fetch the data
    const response = await serverFetcher.query({
      operationName: 'totara_mobile_course',
      query: query,
      variables: {
        courseid: id,
      },
    });

    // Return the response
    return NextResponse.json({
      ...response?.data?.data,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
