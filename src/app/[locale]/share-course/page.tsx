import { Metadata } from 'next';
import { redirect } from 'next/navigation';

type Props = {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{
    courseId: string;
    title: string;
    description: string;
    img: string;
    isProgram?: boolean;
  }>;
};

export const generateMetadata = async ({
  searchParams,
}: Props): Promise<Metadata> => {
  const { title, description, img } = await searchParams;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      images: [{ url: img, secureUrl: img }],
    },
    twitter: {
      title,
      description,
      images: [{ url: img, secureUrl: img }],
    },
  };
};

export default async function ShareCoursePage({ params, searchParams }: Props) {
  const { locale } = await params;
  const { courseId, isProgram } = await searchParams;

  if (isProgram) {
    return redirect(`${process.env.BASE_URL}/${locale}/program/${courseId}`);
  } else {
    return redirect(`${process.env.BASE_URL}/${locale}/course/${courseId}`);
  }
}
