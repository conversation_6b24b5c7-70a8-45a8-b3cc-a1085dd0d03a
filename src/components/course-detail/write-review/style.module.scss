@use 'mixins' as *;

.popup {
  position: fixed;
  @include leftToRight(0);
  top: 0;
  width: 100%;
  height: 100%;
  @include forMarginPadding(margin, size(0), size(0), size(30), size(0));
  z-index: 99;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.wrapper {
  position: relative;
  width: size(579);
  height: 100%;
  overflow-y: auto;
  background: var(--white);
  @include forMarginPadding(padding, size(96), size(40), size(52), size(40));
  z-index: 999;
}

.close {
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  background: none;
  cursor: pointer;
  width: size(30);
  height: size(30);
  border: solid 1px var(--grey-700);
  fill: var(--grey-700);
  color: var(--grey-700);
  @include borderRadius(50%);
  display: flex;
  justify-content: center;
  align-items: center;
  @include transitions(0.5s);
  position: absolute;
  top: size(24);
  @include rightToLeft(size(24));

  i {
    display: block;
    width: size(10);
    height: size(10);

    svg {
      fill-rule: evenodd;
      width: 100%;
      vertical-align: top;
    }
  }
}

.heading {
  @include forMarginPadding(margin, size(0), size(0), size(30), size(0));
  font-size: size(40);
  font-weight: 400;
}

.subTitle {
  font-size: size(22);
  font-weight: 400;
  @include forMarginPadding(margin, size(0), size(0), size(12), size(0));
}

.subDescription {
  font-size: size(18);
  @include forMarginPadding(margin, size(0), size(0), size(48), size(0));
}

.ratingTitle {
  font-size: size(32);
  font-weight: 400;
  @include forMarginPadding(margin, size(0), size(0), size(27), size(0));
}

.rating {
  ul {
    @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    display: flex;

    li {
      @include forMarginPadding(margin, size(0), size(10), size(0), size(0));
      list-style: none;
      width: size(30);
      height: size(30);

      button {
        @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
        @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
        background: none;
        border: none;
        cursor: pointer;
        @include transitions(0.5s);
        width: 100%;
        height: 100%;
        display: block;

        i {
          position: relative;
          display: block;
          width: 100%;
          height: 100%;

          img {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            @include leftToRight(0);
            @include transitions(0.5s);

            &.outline {
            }

            &.filled {
              opacity: 0;
              visibility: hidden;
            }
          }
        }

        &.active {
          img {
            &.outline {
              opacity: 0;
              visibility: hidden;
            }

            &.filled {
              opacity: 1;
              visibility: visible;
            }
          }
        }
      }
    }
  }
}

.buttonWrap {
  position: absolute;
  bottom: size(52);
  width: 100%;
  @include leftToRight(0);
  @include forMarginPadding(padding, size(0), size(40), size(0), size(40));
}

.popupIcon {
  border-radius: 50%;
  display: block;
  @include forMarginPadding(margin, size(0), size(0), size(30), size(0));
  height: size(48);
  width: size(48);

  svg {
    fill: var(--danger-900);
  }
}

.isSuccess {
  background-color: var(--success-900);
  padding: size(8);

  svg {
    fill: var(--white);
  }
}

.popupTitle {
  font-size: size(28);
}

.popupMessage {
  font-size: size(18);
  @include forMarginPadding(margin, size(0), size(0), size(57), size(0));
}

@include hover {
  .close {
    &:hover {
      border-color: var(--grey-900);
      fill: var(--grey-900);
      color: var(--grey-900);
    }
  }
}

@include for-all-phone() {
  .wrapper {
    width: 100%;
  }
}
