import { ErrorResponse } from '@/utils/fetcher';
import { NextRequest, NextResponse } from 'next/server';
import { AxiosError } from 'axios';
import { TotaraCancelBookedSlotService } from '@/services/totara/courses/physical';

export async function POST(request: NextRequest) {
  try {
    // Getting request body
    const { courseId, slotId } = await request.json();

    const response = await TotaraCancelBookedSlotService(courseId, slotId);
    // Return the response
    return NextResponse.json({
      cancelingResponse: response?.data,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error as AxiosError);
  }
}
