import { FC, useCallback, useEffect, useState } from 'react';
import styles from './style.module.scss';
import Entry from './entry';
import useChatStore from '@/stores/ai/chat';
import useSessionStorageStore from '@/stores/ai/session-storage';
import { motion, cubicBezier, AnimatePresence } from 'motion/react';
import History from './history';
import Body from './body';
import ISessionStorage from '@/types/domain/ai/sessionStorage';
import { Bars, CloseThin, Maximize, Minimize } from '@/ui/icons';
import Media from '@/ui/media';
import { useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';

interface IAssistant {
  onCloseChat?: (isActive: boolean) => void;
}

const Chat: FC<IAssistant> = ({ onCloseChat }) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const [isHistoryVisible, setIsHistoryVisible] = useState<boolean>(false);
  const [isActive, setIsActive] = useState<boolean>(true);
  const { aiContext, currentSession, setCurrentSession, setAiContext } =
    useChatStore();
  const { setStoredSession, storedSession, setActiveHistory } =
    useSessionStorageStore();
  const t = useTranslations();

  useEffect(() => {
    if (storedSession?.id) {
      setStoredSession({
        ...storedSession,
        context: aiContext,
        messages: currentSession,
      } as ISessionStorage);
    }
  }, [aiContext, currentSession]); //eslint-disable-line

  const onClickAction = useCallback(() => {
    if (onCloseChat) {
      if (typeof window !== 'undefined' && storedSession?.id) {
        let isNewSession = true;
        let updatedSessions;
        const sessions = localStorage.getItem('CHAT_SESSION_LIST') || [];
        const parsedSessions = sessions.length
          ? JSON.parse(sessions as string)
          : [];
        updatedSessions = parsedSessions.map(
          (parsedSession: ISessionStorage) => {
            if (parsedSession.id === storedSession?.id) {
              isNewSession = false;
              return storedSession;
            }
            return parsedSession;
          },
        );

        if (isNewSession) {
          updatedSessions = [...parsedSessions, storedSession];
        }

        localStorage.setItem(
          'CHAT_SESSION_LIST',
          JSON.stringify(updatedSessions),
        );
      }
      setStoredSession(null);
      setCurrentSession([]);
      setAiContext([]);
      setIsActive(false);
      setTimeout(function () {
        onCloseChat(false);
        setActiveHistory(false);
      }, 400);
    }
  }, [
    onCloseChat,
    storedSession,
    setAiContext,
    setCurrentSession,
    setStoredSession,
    setActiveHistory,
  ]);

  const onExpandAction = useCallback(() => {
    setIsExpanded(!isExpanded);
  }, [isExpanded]);

  const onShowHistory = useCallback(() => {
    setIsHistoryVisible(!isHistoryVisible);
  }, [isHistoryVisible]);

  const hideHistory = useCallback(() => {
    setIsHistoryVisible(false);
  }, []);

  return (
    <>
      <span
        className={
          styles.chatBackground + ` ${isActive ? styles.isActiveClass : ''}`
        }
      ></span>
      <div
        className={
          styles.chatWrapper +
          ` ${isExpanded ? styles.isExpanded : ''}` +
          ` ${isActive ? styles.isActiveClass : ''}`
        }
      >
        <Media mobile={false} tablet={true} desktop={true}>
          <AnimatePresence mode="wait">
            {isExpanded ? (
              <motion.div
                initial={{
                  width: 0,
                  position: 'relative',
                }}
                animate={{
                  width: 450,
                }}
                exit={{
                  width: 0,
                }}
                transition={{
                  ease: cubicBezier(0.71, 0.23, 0.31, 0.86),
                  duration: 0.5,
                }}
                className={styles.historyWrapper}
              >
                <History />
              </motion.div>
            ) : null}
          </AnimatePresence>
        </Media>
        <Media mobile={true} tablet={false} desktop={false}>
          {isHistoryVisible ? <History onCloseAction={hideHistory} /> : null}
        </Media>
        <div className={styles.chatBodyWrapper}>
          <div className={styles.chatActions}>
            <div
              className={
                styles.titleWrapper + ` ${isExpanded ? styles.titleHidden : ''}`
              }
            >
              {t(dictionary.aiAssistant)}
            </div>
            <div className={styles.actionsWrapper}>
              <Media mobile={true} tablet={false} desktop={false}>
                <button onClick={() => onShowHistory()}>
                  <Bars />
                </button>
              </Media>
              <Media mobile={false} tablet={true} desktop={true}>
                <button onClick={() => onExpandAction()}>
                  {!isExpanded ? <Maximize /> : <Minimize />}
                </button>
              </Media>
              <button onClick={() => onClickAction()}>
                <CloseThin />
              </button>
            </div>
          </div>
          <div
            className={
              styles.chatBody +
              ` ${currentSession.length ? styles.activeArea : ''}`
            }
          >
            <Body isExpanded={isExpanded} />
          </div>
          <div className={styles.bottomInput}>
            <Entry isExpanded={isExpanded} />
          </div>
        </div>
      </div>
    </>
  );
};

export default Chat;
