import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraCommunityDetailsCreatePostService } from '@/services/totara/my-network';

/**
 * Route to create new POST in community
 */
export async function POST(request: NextRequest) {
  try {
    // Getting request params
    const urlSplit = request.url.split('/');
    const communityId = urlSplit.filter((x) => !isNaN(parseInt(x)))[0];

    const { postType, content, draftid } = await request.json();

    // call API to create new community POST
    const response = await TotaraCommunityDetailsCreatePostService(
      Number(communityId),
      postType,
      content,
      draftid,
    );
    // Return the response
    return NextResponse.json({
      success: response.data.success,
      message: response.data.message,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error);
  }
}
