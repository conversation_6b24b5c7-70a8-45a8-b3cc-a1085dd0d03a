import ChipButton from '@/ui/form/chip-button';
import Typography from '@/ui/typography';
import { FC, useCallback } from 'react';

// Styles
import styles from './style.module.scss';

interface ICoachLevelFilter {
  onSelectChange: (
    id: string,
    name: string | undefined,
    fieldName?: string,
  ) => void;
  fieldName: string;
  selected: string;
}

const CoachLevelFilter: FC<ICoachLevelFilter> = ({
  onSelectChange,
  fieldName,
  selected,
}) => {
  // Method to handle click
  const onClick = useCallback(
    (x?: string) => {
      if (x) {
        onSelectChange(x, x, fieldName);
      }
    },
    [onSelectChange, fieldName],
  );

  const list = ['Beginner', 'Intermediate', 'Advanced'];

  // Return JSX
  return (
    <nav className={styles.filter}>
      <Typography as="h4">Level</Typography>
      <ul>
        {list?.map((x, i) => (
          <li key={'type-' + i}>
            <ChipButton
              variant="rounded"
              active={selected === x}
              onClick={() => onClick(x)}
            >
              {x}
            </ChipButton>
          </li>
        ))}
      </ul>
    </nav>
  );
};

export default CoachLevelFilter;
