@use 'mixins' as *;

.carouselItemImage {
  border-radius: size(16) size(16) 0 0;
  height: size(150);
  position: relative;
  overflow: hidden;

  img {
    height: 100%;
    object-fit: cover;
    position: absolute;
    width: 100%;
  }
}

.carouselItemInfo {
  align-items: flex-start;
  background-color: var(--white);
  border-radius: 0 0 size(16) size(16);
  display: flex;
  flex-direction: column;
  height: calc(100% - size(150));
  padding: size(24) size(20);
}

.infoType {
  border: 1px solid var(--grey-500);
  border-radius: size(24);
  color: var(--grey-900);
  font-size: size(10);
  line-height: 1;
  letter-spacing: size(1);
  margin-bottom: size(12);
  padding: size(8);
  text-transform: uppercase;
}

.infoName {
  color: var(--grey-900);
  font-size: size(16);
  font-weight: 700;
  margin-bottom: size(16);
}

.infoMeta {
  display: flex;
  font-size: size(14);
  margin-bottom: size(16);
  width: 100%;

  svg {
    fill: var(--grey-900);
    height: size(12);
    width: size(12);
  }
}

.infoDuration {
  align-items: center;
  display: flex;
  gap: size(4);
  flex: 1;
}

.infoProficiency {
  align-items: center;
  display: flex;
  gap: size(4);
  flex: 1;

  path[class='light'] {
    fill: var(--grey-500);
  }
}

.infoSummary {
  font-size: size(14);
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  margin-bottom: size(16);
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  width: 100%;
}

.infoSkills {
  display: flex;
  gap: size(8);
  flex-wrap: wrap;
  font-size: size(10);
}

.infoSkill {
  background-color: var(--bg-shade-5);
  border-radius: size(40);
  padding: size(8) size(12);
}

.nameLink {
  &:hover {
    text-decoration: underline;
  }
}
