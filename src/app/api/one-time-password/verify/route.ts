import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';
import { ExternalVerifyOTPService } from '@/services/external/one-time-password';
import IVerifyOTP from '@/types/domain/external/one-time-password/verify';

// To handle a POST request to /api
export async function POST(request: NextRequest) {
  try {
    // Getting request body
    const data: IVerifyOTP = await request.json();
    // Generating OTP
    const response = await ExternalVerifyOTPService(data);
    // Return the response
    return NextResponse.json({
      status: response?.data?.status,
      message: response?.data?.description,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error as AxiosError);
  }
}
