@use 'mixins' as *;

.recommended {
  @include forMarginPadding(padding, size(30), size(16), size(30), size(16));
  @include borderRadius(var(--radius));
  background: var(--white);
  @include forMarginPadding(margin, size(0), size(0), size(30), size(0));

  h2 {
    font-size: size(20);
    font-weight: 700;
    @include forMarginPadding(margin, size(0), size(0), size(20), size(0));
  }
}

.card {
  display: flex;
  align-items: center;
  border-bottom: solid 1px var(--grey-500);
  @include forMarginPadding(padding, size(15), size(0), size(15), size(0));

  &:first-child {
    @include forMarginPadding(padding, size(0), size(0), size(15), size(0));
    border-bottom: none;
  }

  &:last-child {
    @include forMarginPadding(padding, size(15), size(0), size(0), size(0));
    border-bottom: none;
  }
}

.photo {
  width: size(60);
  height: size(60);
  @include borderRadius(8px);
  overflow: hidden;
  @include forMarginPadding(margin, size(0), size(16), size(0), size(0));
}

.data {
  position: relative;
  @include forMarginPadding(margin, size(0), size(40), size(0), size(0));
  flex: 1;
}

.titleLink {
  font-size: size(16);
  text-decoration: none;
  color: var(--grey-900);

  span {
    display: inline;
    padding-bottom: 0;
    transition: all 0.5s linear;
    background: linear-gradient(
      to bottom,
      var(--grey-900) 0%,
      var(--grey-900) 98%
    );
    background-size: 0 1px;
    background-repeat: no-repeat;
    background-position: left 100%;

    @include rtl {
      background-position: right 100%;
    }
  }
}

.siteLink {
  color: var(--grey-900);
  font-size: size(14);
  text-decoration: none;
  @include transitions(0.5s);
  display: inline-flex;
  align-items: center;

  span {
    display: inline;
    padding-bottom: 0;
    transition: all 0.5s linear;
    background: linear-gradient(
      to bottom,
      var(--grey-900) 0%,
      var(--grey-900) 98%
    );
    background-size: 0 1px;
    background-repeat: no-repeat;
    background-position: left 100%;

    @include rtl {
      background-position: right 100%;
    }
  }
}

.linkLogo {
  width: size(16);
  height: size(16);
  @include borderRadius(50%);
  overflow: hidden;
  @include forMarginPadding(margin, size(0), size(10), size(0), size(0));
}

.menuWrap {
  position: absolute;
  @include rightToLeft(size(-40));
  top: size(0);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  z-index: 9;

  div[class*='container'] > button {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    width: size(12);
    height: size(12);

    &:before {
      display: none;
    }

    i {
      width: 100%;
      height: 100%;

      svg {
        width: 100%;
        height: 100%;
      }
    }

    + button {
      @include forMarginPadding(margin, size(0), size(0), size(0), size(10));
    }
  }
}

.menuList {
  background-color: var(--cool-grey-500);
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));

  li {
    list-style: none;
    border-bottom: solid 1px var(--cool-grey-800);

    &:last-child {
      border: none;
    }

    button {
      border: none;
      background: none;
      @include forMarginPadding(
        padding,
        size(12),
        size(12),
        size(12),
        size(12)
      );
      line-height: 1;
      display: flex;
      align-items: center;
      width: 100%;
      cursor: pointer;

      &:hover {
        background-color: var(--cool-grey-700);
        transition: 0.2s all ease-in-out;
      }

      i {
        width: size(24);
        height: size(24);
        @include forMarginPadding(margin, size(0), size(10), size(0), size(0));

        svg {
          fill: var(--grey-900);
          fill-rule: evenodd;
        }

        &.plus {
          background: var(--grey-900);
          fill: var(--white);
          @include borderRadius(50%);
          display: flex;
          justify-content: center;
          align-items: center;

          svg {
            width: size(11);
            height: size(11);
          }
        }
      }

      span {
        font-size: size(14);
        display: inline;
        padding-bottom: 0;
        transition: all 0.5s linear;
        background: linear-gradient(
          to bottom,
          var(--grey-900) 0%,
          var(--grey-900) 98%
        );
        background-size: 0 1px;
        background-repeat: no-repeat;
        background-position: left 100%;

        @include rtl {
          background-position: right 100%;
        }
      }
    }
  }
}

.viewAll {
  display: flex;
  justify-content: center;
  @include forMarginPadding(margin, size(24), size(0), size(0), size(0));
  font-size: size(16);
  text-decoration: underline;
}

@include hover() {
  .menuList {
    li {
      button {
        &:hover {
          span {
            background-size: 100% 1px;
          }
        }
      }
    }
  }

  .titleLink {
    &:hover {
      span {
        background-size: 100% 1px;
      }
    }
  }
}

@include for-dark-theme() {
  .menuList {
    background-color: var(--grey-300);

    li {
      border-bottom: solid 1px var(--grey-400);

      &:last-child {
        border: none;
      }

      button {
        &:hover {
          background-color: #141414;
        }
      }
    }
  }
}
