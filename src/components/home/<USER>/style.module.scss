@use 'mixins' as *;

.mylearningWrapper {
  > div[class*='paper'] {
    min-height: size(450);

    @include for-all-phone() {
      min-height: size(380);
      @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    }
  }

  > div[class*='head'] {
    a {
      text-decoration: underline;

      &:hover {
        text-decoration: none;
      }

      span {
        background: none;
      }
    }
  }
}
