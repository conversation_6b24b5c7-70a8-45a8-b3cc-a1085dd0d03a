import { FC, useCallback, useEffect, useState } from 'react';
import useCourseEnrollmentStore from '@/stores/learning/courses/enrollment/enroll';
import useCheckCourseEnrollmentStore from '@/stores/learning/courses/enrollment/check';
import useCourseDetailStore from '@/stores/learning/courses/detail';
import useCoreCourseStore from '@/stores/learning/courses/core-course';
import IEnrollmentOptions from '@/types/domain/learning/course/enrollmentOptions';
import Button from '@/ui/form/button';
import dictionary from '@/dictionaries';

interface IEnrollCourseAction {
  courseId?: string | number;
}

const EnrollCourseAction: FC<IEnrollCourseAction> = ({ courseId }) => {
  const [enrollementInfo, setEnrollementInfo] =
    useState<IEnrollmentOptions[]>();
  const enrolmentDetails = useCheckCourseEnrollmentStore(
    (x) => x.enrolmentDetails,
  );
  const onFetch = useCourseDetailStore((state) => state.fetch);
  const onCoreCourseFetch = useCoreCourseStore((state) => state.fetch);
  const onCourseEnrollement = useCourseEnrollmentStore((x) => x.fetch);
  const isFetching = useCourseEnrollmentStore((x) => x.isFetching);

  useEffect(() => {
    setEnrollementInfo(
      enrolmentDetails?.enrolmentOptions?.filter((val) => val.type == 'self'),
    );
  }, [enrolmentDetails]);

  /**
   * Method to handle course enrollment
   */
  const onEnrollCourse = useCallback(async () => {
    await onCourseEnrollement({
      input: {
        courseid: courseId as string,
        instanceid: enrollementInfo?.[0].id,
        password: enrollementInfo?.[0].passwordRequired ? '' : undefined,
      },
    });
    onFetch(courseId as string);
    onCoreCourseFetch(courseId as string);
  }, [
    onCourseEnrollement,
    courseId,
    enrollementInfo,
    onFetch,
    onCoreCourseFetch,
  ]);

  return (
    <Button
      color="black"
      type="button"
      loading={isFetching}
      onClick={onEnrollCourse}
      dictionary={dictionary.enroll}
    />
  );
};

export default EnrollCourseAction;
