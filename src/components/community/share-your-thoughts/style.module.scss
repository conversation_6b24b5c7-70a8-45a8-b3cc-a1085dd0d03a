@use 'mixins' as *;

.head {
  display: flex;
  align-items: center;
  position: relative;
  @include borderRadius(var(--radius));
  @include forMarginPadding(padding, size(15), size(26), size(15), size(26));
  overflow: hidden;
  background: var(--grey-300);
  cursor: pointer;
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));

  p {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }

  &:hover {
    border: solid 1px var(--grey-900);
  }
}

.avatar {
  width: size(32);
  height: size(32);
  @include borderRadius(50%);
  @include forMarginPadding(margin, size(0), size(15), size(0), size(0));
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  @include borderRadius(50%);
  background: rgba(160, 146, 200, 0.15);

  p {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    font-weight: 500;
    font-size: size(12);
  }
}

.postInput {
  font-size: size(20);
  margin-bottom: size(20);
}

.icon {
  width: size(24);
  height: size(24);
  @include forMarginPadding(margin, size(0), size(0), size(0), auto);

  svg {
    fill: var(--grey-900);
  }
}

.footer {
  display: flex;
}

.btn {
  border: none;
  font-size: size(14);
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--bg-shade-10);
  @include borderRadius(8px);
  @include forMarginPadding(padding, size(12), size(15), size(12), size(15));
  @include forMarginPadding(margin, size(0), size(6), size(0), size(0));
  cursor: pointer;
  color: var(--grey-700);

  &.question {
    background: var(--bg-shade-11);
  }

  &.popper {
    background: var(--bg-shade-12);
  }

  &:hover {
    color: var(--grey-900);
    font-weight: bold;
  }

  &:last-child {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }

  i {
    width: size(18);
    height: size(18);
    @include forMarginPadding(margin, size(0), size(5), size(0), size(0));

    svg {
      fill: var(--grey-900);
    }
  }
}

@include for-all-phone() {
  .head {
    @include forMarginPadding(padding, size(16), size(16), size(16), size(16));

    p {
      font-size: size(16);
    }
  }

  .btn {
    @include forMarginPadding(padding, size(12), size(12), size(12), size(12));
  }
}
