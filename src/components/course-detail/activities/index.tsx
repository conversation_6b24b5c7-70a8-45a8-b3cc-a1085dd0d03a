import { FC, useCallback } from 'react';
import Typography from '@/ui/typography';
import Item from './item';
import ICoreSection from '@/types/domain/learning/course/coreSection';
import { Checkmark } from '@/ui/icons';
import ExternalIcon from '@/ui/icons/external';
import useStartLearningStore from '@/stores/learning/courses/start-learning';
import useTotaraAuth from '@/stores/totara-auth';
import useAuthStore from '@/stores/auth';
import { useLocale } from 'next-intl';

// Styles
import styles from './style.module.scss';
import dictionary from '@/dictionaries';

interface IActivities {
  list?: ICoreSection[];
  isEnrolled?: boolean;
}

const Activities: FC<IActivities> = (props) => {
  const { list = [], isEnrolled } = props;
  const onStartLearning = useStartLearningStore((x) => x.fetch);
  const onTotaraAuthFetch = useTotaraAuth((x) => x.fetch);
  const setIsLoading = useTotaraAuth((x) => x.setIsLoading);
  const user = useAuthStore((x) => x.user);
  const locale = useLocale();

  const onTotaraLogin = useCallback(
    async (url: string) => {
      setIsLoading(true);
      onTotaraAuthFetch(url, user?.idn as string, locale);
      onStartLearning();
    },
    [user?.idn, onTotaraAuthFetch, onStartLearning, setIsLoading, locale],
  );

  // Return JSX
  return (
    <>
      <Typography as="h4" dictionary={dictionary.activities} />
      <div className={styles.list}>
        {list?.map((section) => {
          let isCompleted = true;
          section?.data?.map((lesson) => {
            if (lesson?.completionstatus != 'complete') {
              isCompleted = false;
            }
          });
          if (section?.data && section?.data?.length > 0) {
            return (
              <div key={section.id} className={styles.card}>
                <div
                  className={
                    styles.number + ` ${isCompleted ? styles.isCompleted : ''}`
                  }
                >
                  {isCompleted && <Checkmark />}
                </div>
                <div className={styles.data}>
                  <Typography as="p" className={styles.title}>
                    {section.title}
                  </Typography>
                  <Typography as="span" className={styles.content}>
                    {section.summary}
                  </Typography>
                  {section.data.length == 1 && section?.data?.[0].viewurl ? (
                    <button
                      className={styles.singleExternalLink}
                      disabled={!section?.data?.[0].available || !isEnrolled}
                      onClick={() =>
                        onTotaraLogin(section?.data?.[0].viewurl as string)
                      }
                    >
                      <i>
                        <ExternalIcon />
                      </i>
                    </button>
                  ) : null}
                </div>
                {section.data.length == 1 && (
                  <div>
                    <div className={styles.singleTitle}>
                      {section.data[0].name}
                    </div>
                    <div className={styles.singleDescription}>
                      {section.data[0].availablereason}
                    </div>
                  </div>
                )}
                <div className={styles.itemsWrapper}>
                  {section.data.length > 1 &&
                    section.data.map((lesson) => {
                      return (
                        <Item
                          key={lesson.id}
                          isAvailable={lesson.available}
                          isEnrolled={isEnrolled}
                          title={lesson.name}
                          link={lesson.viewurl}
                          text={lesson.availablereason}
                          onActivityClick={onTotaraLogin}
                        />
                      );
                    })}
                </div>
              </div>
            );
          }
        })}
      </div>
    </>
  );
};

export default Activities;
