import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchNotificationsService } from '@/services/totara/notifications';

/**
 * Route to get the notifications
 */
export async function GET() {
  try {
    // Updating user profile
    const response = await TotaraFetchNotificationsService();
    // Return the response
    return NextResponse.json({
      total: response?.data?.notifications?.length || 0,
      list: response?.data?.notifications || [],
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error);
  }
}
