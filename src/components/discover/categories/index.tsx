'use client';

import { useEffect, useState } from 'react';
import useCoursesCategoriesStore from '@/stores/learning/courses/categories';
import Typography from '@/ui/typography';
import WithLoader from '@/ui/skeleton/with-loader';
import Item from './item';
import Loading from './loading';
import dictionary from '@/dictionaries';

// Styles
import styles from './style.module.scss';
import { useTranslations } from 'next-intl';
import dictionaries from '@/dictionaries';
import DialogDrawer from '@/ui/dialog-drawer';
import useBooleanFeatureFlag from '@/hooks/feature-flags/useBooleanFeatureFlag';
import useFeatureAccessStore from '@/stores/feature-access';
import { Features } from '@/types/domain/feature-access/featureAccess';

const CategoriesList = () => {
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const AIEnabled = useBooleanFeatureFlag('aiFeature');

  // Getting loading state
  const isFetching = useCoursesCategoriesStore((x) => x.isFetching);

  // Getting method to fetch categories
  const onFetch = useCoursesCategoriesStore((x) => x.fetch);

  const isFetchingFeatureAccess = useFeatureAccessStore((x) => x.isFetching);
  const checkFeatureAccess = useFeatureAccessStore((x) => x.checkAccess);

  // Getting categories
  const list = useCoursesCategoriesStore((x) => x.list);
  const t = useTranslations();
  // Effect to fetch categories
  useEffect(() => {
    // Fetching categories if not fetching
    if (isFetching === null) {
      // Fetching categories
      onFetch();
    }
  }, [onFetch, isFetching]);

  const onClose = () => {
    setIsDialogOpen(false);
  };

  const userAIAccess = checkFeatureAccess(Features.AI);

  // Return JSX
  return (
    <WithLoader
      loader={<Loading />}
      loading={isFetching || isFetchingFeatureAccess}
    >
      <div className={styles.category}>
        <div className={styles.head}>
          <Typography as="h5">{t(dictionaries.Categories)}</Typography>
        </div>
        <nav className={styles.list}>
          <ul>
            {list
              .map((x) => {
                if (!AIEnabled && !userAIAccess && x.categoryid === 99999)
                  return null;
                return (
                  <li key={x?.categoryid}>
                    <Item
                      id={x?.categoryid?.toString()}
                      text={x?.title}
                      icon={x?.image}
                    />
                  </li>
                );
              })
              .slice(0, 10)}
          </ul>
        </nav>
        {list.length > 10 ? (
          <div className={styles.viewAllWrapper}>
            <button
              className={styles.viewAllButton}
              onClick={() => setIsDialogOpen(true)}
            >
              <Typography dictionary={dictionary.viewAll} />
            </button>
          </div>
        ) : null}
      </div>
      <DialogDrawer
        isOpened={isDialogOpen}
        onClose={onClose}
        title={t(dictionaries.Categories)}
      >
        <div className={styles.categoriesWrapper}>
          <Typography as="p">
            {t(dictionaries.Total)} {list.length} {t(dictionaries.Categories)}
          </Typography>
          <nav className={styles.list}>
            <ul>
              {list.map((x) => (
                <li key={x?.categoryid}>
                  <Item
                    id={x?.categoryid?.toString()}
                    text={x?.title}
                    icon={x?.image}
                    onClick={onClose}
                  />
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </DialogDrawer>
    </WithLoader>
  );
};

export default CategoriesList;
