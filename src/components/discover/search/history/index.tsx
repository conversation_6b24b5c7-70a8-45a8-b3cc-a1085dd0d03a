import { FC, useCallback, useEffect } from 'react';
import Section from './section';
import useSearchCoursesStore from '@/stores/learning/courses/search';
import usePopularSearchesStore from '@/stores/learning/courses/filters/popular';
import useRecentSearchesStore from '@/stores/learning/courses/filters/recent';
import useSearchCourses from '@/hooks/helpers/search/course/useSearchCourses';

// Styles
import styles from './style.module.scss';
import { useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';

interface IHistory {
  closePopup?: () => void;
}

const History: FC<IHistory> = (props) => {
  const { closePopup } = props;

  // Getting update filter method
  const setFilter = useSearchCoursesStore((x) => x.setFilter);

  // Getting fetch action of the popular searches
  const fetchPopularSearches = usePopularSearchesStore((x) => x.fetch);

  // Getting fetch action of the recent searches
  const fetchRecentSearches = useRecentSearchesStore((x) => x.fetch);

  // Getting popular searches
  const popularSearches = usePopularSearchesStore((x) => x.list);

  // Getting recent searches
  const recentSearches = useRecentSearchesStore((x) => x.list);

  // Getting action for clear recent searches
  const clear = useRecentSearchesStore((x) => x.clear);

  // Getting methods from custom hook to handle search courses
  const { updateQueryParam } = useSearchCourses();
  const t = useTranslations();
  // Method to handle click on the sugessted keyword
  const onClick = useCallback(
    (keyword: string) => {
      // setting keyword filter
      setFilter({
        key: 'keyword',
        value: keyword,
      });

      // Updating query param in route
      updateQueryParam('search', keyword);

      // Closing popup
      if (closePopup) {
        closePopup();
      }
    },
    [setFilter, updateQueryParam, closePopup],
  );

  /**
   * React hook to handle the initial
   * fetch of the popular and recent searches
   */
  useEffect(() => {
    // Fetch popular searches
    fetchPopularSearches();

    // Fetch recent searches
    fetchRecentSearches();
  }, [fetchPopularSearches, fetchRecentSearches]);

  // Return JSX
  return (
    <div className={styles.searchHistory}>
      <Section
        title={t(dictionary.popularSearches)}
        list={popularSearches}
        onClick={onClick}
      />
      {recentSearches?.length > 0 && <div className={styles.sep}></div>}
      <Section
        title={t(dictionary.recentSearches)}
        list={recentSearches}
        onClick={onClick}
        onClear={clear}
      />
    </div>
  );
};

export default History;
