@use 'mixins' as *;

.coachExperience {
  display: flex;
  align-items: center;
  @include forMarginPadding(margin, size(15), size(0), size(16), size(0));

  .experienceLogos {
    display: flex;

    img {
      width: size(30);
      height: size(30);
      @include borderRadius(50%);
      border: 1px solid black;
    }

    .logoImgOverlap {
      @include forMarginPadding(margin, size(0), size(0), size(0), size(-15));
    }
  }

  .experienceText {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(10));
  }
}
