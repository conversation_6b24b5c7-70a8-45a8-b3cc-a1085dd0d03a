import { ICommunityDiscussion } from '@/types/domain/my-network/communityDetails';
import Item from './item';
import React from 'react';

interface IPostProps {
  discussions: ICommunityDiscussion[] | undefined;
  isMember?: boolean;
}

const Posts: React.FC<IPostProps> = ({ discussions, isMember }) => {
  return (
    <>
      {discussions?.map((details) => (
        <Item details={details} key={details.id} isMember={isMember} />
      ))}
    </>
  );
};

export default Posts;
