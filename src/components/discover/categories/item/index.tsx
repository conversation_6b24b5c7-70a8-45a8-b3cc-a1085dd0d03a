'use client';
import { FC, ReactNode, useCallback, useMemo } from 'react';
import Image from 'next/image';
import { usePathname, useRouter } from '@/i18n/routing';
import Typography from '@/ui/typography';
import useSearchCourses from '@/hooks/helpers/search/course/useSearchCourses';
import useSearchCoursesStore from '@/stores/learning/courses/search';
// Assets
import library from '@public/images/library.svg';

// Styles
import styles from './style.module.scss';

interface IItem {
  id?: string;
  icon?: ReactNode;
  text?: string;
  onClick?: () => void;
}

export interface IDiscoverSearchQueryParams {
  search?: string;
  category?: string;
}

const Item: FC<IItem> = (props) => {
  const { id, icon = library, text, onClick } = props;

  // Getting methods from custom hook to handle search courses
  const { updateQueryParam } = useSearchCourses();
  const setFilter = useSearchCoursesStore((x) => x.setFilter);

  // Getting pathname
  const pathname = usePathname();
  const router = useRouter();

  /**
   * Method to handle click on category item
   */
  const handleClick = useCallback(() => {
    if (id === '99999') {
      return router.push('/discover/ai-recommended');
    }
    // Updating query param in route
    setFilter({
      key: 'page',
      value: 1,
    });
    updateQueryParam('category', id as string);
    if (onClick) {
      onClick();
    }
  }, [id, setFilter, updateQueryParam, onClick, router]);

  // Getting active class
  const active = useMemo(() => {
    return pathname.includes(id as string) ? styles.active : '';
  }, [pathname, id]);

  // Return JSX
  return (
    <div className={active}>
      <button className={styles.linkItem} onClick={handleClick}>
        <span className={styles.icon}>
          {icon && (
            <i>
              <Image
                src={icon}
                width={80}
                height={80}
                alt={icon}
                className="img-fluid"
              />
            </i>
          )}
        </span>
        {text && (
          <Typography as="span" className={styles.text}>
            {text}
          </Typography>
        )}
      </button>
    </div>
  );
};

export default Item;
