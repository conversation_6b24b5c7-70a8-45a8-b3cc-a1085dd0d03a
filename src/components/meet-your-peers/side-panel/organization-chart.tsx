import dictionary from '@/dictionaries';
import Typography from '@/ui/typography';
import styles from './style.module.scss';
import Image from 'next/image';
import profileImage from '@public/images/job-profile-image.jpg';
import { Corporate } from '@/ui/icons';
import OrganizationChartItem from './organization-chart-item';

const listWorkWith = [
  { id: '1', name: '<PERSON><PERSON><PERSON>', work: 'Project Manager' },
  { id: '2', name: '<PERSON><PERSON><PERSON>', work: 'Project Manager' },
  { id: '3', name: '<PERSON><PERSON><PERSON>', work: 'Project Manager' },
  { id: '4', name: '<PERSON><PERSON><PERSON>', work: 'Project Manager' },
  { id: '5', name: '<PERSON><PERSON><PERSON>', work: 'Project Manager' },
  { id: '6', name: '<PERSON><PERSON><PERSON>', work: 'Project Manager' },
];

const OrganizationChart = () => {
  const handleClick = () => {
    throw new Error('Function not implemented.');
  };

  return (
    <div className={styles.chartWrapper}>
      <Typography as="h1" dictionary={dictionary.organizationChart} />
      <div className={styles.containerChart}>
        <OrganizationChartItem
          onClick={handleClick}
          fullname="Mahsa Jannaly"
          work="Project Manager"
        />
        <OrganizationChartItem
          onClick={handleClick}
          fullname="Mahsa Jannaly"
          work="Project Manager"
        />
        <OrganizationChartItem
          onClick={handleClick}
          fullname="Mahsa Jannaly"
          work="Project Manager"
        />
      </div>
      <div className={styles.containerWorkWith}>
        <Typography as="h4" dictionary={dictionary.youWorkWith} />
        <nav>
          <ul>
            {listWorkWith.map((item) => (
              <li key={item.id} className={styles.workWithItem}>
                <div className={styles.workWithItemImage}>
                  <Image
                    src={profileImage}
                    alt=""
                    className="img-fluid"
                    height={100}
                    width={100}
                  />
                </div>
                <div className={styles.workWithItemData}>
                  <Typography as="p">{item.name}</Typography>
                  <Typography as="p" className={styles.profileInfoIcon}>
                    <Corporate />
                    <span>{item.work}</span>
                  </Typography>
                </div>
              </li>
            ))}
          </ul>
        </nav>
      </div>
    </div>
  );
};
export default OrganizationChart;
