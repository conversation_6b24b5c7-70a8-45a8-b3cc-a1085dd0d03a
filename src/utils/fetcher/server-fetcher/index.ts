import CONFIG from '@/config';
import axios from 'axios';
import {
  getIlearnGraphQLAPIKey,
  getIlearnGraphQLURL,
  getIlearnSession,
  getSupportCenterToken,
  getUserLocale,
  getUserToken,
} from '../../auth';
import { onRequest, onRequestError } from './interceptors/request.interceptor';
import {
  onResponse,
  onResponseError,
} from './interceptors/response.interceptor';
import { onSupportCenterResponse } from './interceptors/support-center.interceptor';

interface IOptions {
  /**
   * Property to hold authorization
   * bearer state for the request
   */
  withBearer?: boolean;

  /**
   * Property to hold the state
   * if multipart formdata is
   * present
   */
  isMultipart?: boolean;

  /**
   * Property to hide the params
   * if they are not needed
   */
  hideParams?: boolean;

  /**
   * Property to remove language param
   * if it is not needed
   */
  withoutLanguage?: boolean;

  /**
   * Property to switch between workspace and openai.
   */
  isGenerative?: boolean;
}

/**
 * Class to handle application requests
 */
class ServerFetcher {
  /**
   * Method to get the default headers
   */
  private headers = (): {
    [key: string]: string;
  } => {
    return {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    };
  };

  // Method to create axios instance for graphql
  public query = async (data: {
    operationName: string;
    query: string;
    variables: {
      [key: string]: string | number | boolean;
    };
  }) => {
    //Getting headers
    const headers = this.headers();

    // Getting URL
    const URL = await getIlearnGraphQLURL();

    // Adding x-api-key header
    headers['x-api-key'] = await getIlearnGraphQLAPIKey();

    //Creating axios instance
    const instance = axios.create({
      baseURL: URL,
      headers: headers,
    });

    //Creating axios instance
    return instance.request({
      method: 'POST',
      data: data,
    });
  };

  /**
   * Method to create axios instance
   * with settings that are needed for
   * totara backend
   *
   * @returns AxiosInstance
   */
  public totara = async (
    options: IOptions = {
      withBearer: true,
      isMultipart: false,
      hideParams: false,
      withoutLanguage: false,
    },
  ) => {
    //Getting headers
    const headers = this.headers();

    // Getting user locale
    const locale = await getUserLocale();

    // Getting totara session
    const token = await getIlearnSession();

    //Adding headers multipart for form-data
    if (options.isMultipart) {
      headers['Content-Type'] = 'multipart/form-data';
    }

    //Creating axios instance
    const instance = axios.create({
      baseURL: process.env.ILEARN_API,
      headers: headers,
      ...(options.hideParams !== true
        ? {
            params: {
              [CONFIG.totara.key.token]: token,
              [CONFIG.totara.key.format]: 'json',
              [CONFIG.totara.key.language]: options.withoutLanguage
                ? undefined
                : locale,
            },
          }
        : {}),
    });

    //Creating axios instance
    return instance;
  };

  /**
   * Method to create axios instance
   * with settings that are needed for
   * liferay backend
   *
   * @returns AxiosInstance
   */
  public liferay = async (
    options: IOptions = {
      withBearer: true,
      isMultipart: false,
    },
  ) => {
    //Getting headers
    const headers = this.headers();

    //Adding headers specific to liferay
    if (options.withBearer) {
      headers['Authorization'] = await getUserToken();
    }

    //Adding headers specific to liferay
    if (options.isMultipart) {
      headers['Content-Type'] = 'multipart/form-data';
    }

    //Creating axios instance
    const instance = axios.create({
      baseURL: process.env.LIFERAY_API,
      headers: headers,
    });

    //Adding interceptor for request
    instance.interceptors.request.use(onRequest, onRequestError);

    //Adding interceptor for response
    instance.interceptors.response.use(onResponse, onResponseError);

    //Creating axios instance
    return instance;
  };

  /**
   * Method to create axios instance
   * with settings that are needed for
   * Support Center backend
   *
   * @returns AxiosInstance
   */
  public supportCenter = async (
    options: IOptions = {
      withBearer: true,
      isMultipart: false,
    },
  ) => {
    const headers = this.headers();

    const rawLocale = await getUserLocale();
    const normalizedLocale = rawLocale?.startsWith('ar') ? 'ar' : 'en';

    //Adding headers specific to support center
    if (options.withBearer) {
      headers['Authorization'] = await getSupportCenterToken();
      headers['Accept-Language'] = normalizedLocale;
    }

    //Adding headers specific to support center
    if (options.isMultipart) {
      headers['Content-Type'] = 'multipart/form-data';
    }

    //Creating axios instance
    const instance = axios.create({
      baseURL: process.env.SUPPORT_CENTER_API,
      headers: headers,
    });

    //Adding interceptor for request
    instance.interceptors.request.use(onRequest, onRequestError);

    //Adding interceptor for response
    instance.interceptors.response.use(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      onSupportCenterResponse as any,
      onResponseError,
    );

    //Creating axios instance
    return instance;
  };

  /**
   * AI Fetcher
   * @returns AxiosInstance
   */
  public ai = async (
    options: IOptions = {
      isGenerative: true,
    },
  ) => {
    //Getting headers
    const headers = this.headers();

    //Adding headers specific to workspace vs openai
    if (options.isGenerative) {
      headers['api-key'] = process.env.AI_API_KEY as string;
    } else {
      headers['X-API-KEY'] = process.env.AI_X_API_KEY as string;
    }

    //Creating axios instance
    const instance = axios.create({
      baseURL: options.isGenerative
        ? process.env.OPENAI_BASE_API
        : process.env.TOMOUH_BASE_API_URL,
      maxBodyLength: Infinity,
      headers: headers,
    });

    //Adding interceptor for request
    instance.interceptors.request.use(onRequest, onRequestError);

    //Adding interceptor for response
    instance.interceptors.response.use(onResponse, onResponseError);

    //Creating axios instance
    return instance;
  };

  /**
   * Feature Access Fetcher
   * @returns AxiosInstance
   */
  public featureAccess = async () => {
    //Getting headers
    const headers = this.headers();
    headers['X-API-KEY'] = process.env.FEATURE_ACCESS_X_API_KEY as string;

    //Creating axios instance
    const instance = axios.create({
      baseURL: process.env.TOMOUH_BASE_API_URL,
      maxBodyLength: Infinity,
      headers: headers,
    });

    //Adding interceptor for request
    instance.interceptors.request.use(onRequest, onRequestError);

    //Adding interceptor for response
    instance.interceptors.response.use(onResponse, onResponseError);

    //Creating axios instance
    return instance;
  };
}

// Creating instance of Server Fetcher
const serverFetcher = new ServerFetcher();

/**
 * Exporting ES6 default module
 */
export default serverFetcher;
