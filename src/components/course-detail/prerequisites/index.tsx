import { FC } from 'react';
import Typography from '@/ui/typography';
import { Tick } from '@/ui/icons';
import { IPrerequisites } from '@/types/domain/learning/course';

// Styles
import styles from './style.module.scss';
import EmptyMessage from '@/ui/empty-message';
import dictionary from '@/dictionaries';

interface IPrerequisitesProps {
  list?: IPrerequisites[];
}

const Prerequisites: FC<IPrerequisitesProps> = (props) => {
  const { list = [] } = props;

  // Return JSX
  return (
    <div className={styles.prerequisites}>
      <Typography as="h4" dictionary={dictionary.prerequisites} />
      {list && list.length > 0 ? (
        <nav>
          <ul className={styles.list}>
            {list.map((x) => (
              <li key={x.title}>
                <i>
                  <Tick />
                </i>
                <Typography as="p">{x.title}</Typography>
              </li>
            ))}
          </ul>
        </nav>
      ) : (
        <EmptyMessage icon="search" title={dictionary.noInformationAvailable} />
      )}
    </div>
  );
};

export default Prerequisites;
