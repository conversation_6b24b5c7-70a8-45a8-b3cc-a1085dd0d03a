import { FC } from 'react';
import Typography from '@/ui/typography';
import userImage from '@public/images/no-user.webp';

// Styles
import styles from '../style.module.scss';
import Image, { StaticImageData } from 'next/image';
import { Corporate, WorkOutline } from '@/ui/icons';

interface IItem {
  id: string;
  photo: StaticImageData | string;
  name: string;
  department: string;
  work: string;
  onClick: (id: string) => void;
}

/**
 * Component to handle item
 */
const Item: FC<IItem> = (props) => {
  // Deconstruct props
  const { id, photo = userImage, name, department, work, onClick } = props;

  /**
   * Method to handle click on item
   */
  const handleClick = () => {
    onClick(id);
  };

  // Return JSX
  return (
    <div className={styles.card} onClick={handleClick}>
      <div className={styles.photo}>
        <Image
          src={photo}
          alt=""
          className="img-fluid"
          height={100}
          width={100}
        />
      </div>
      <div className={styles.data}>
        <Typography as="span" className={styles.title}>
          {name}
        </Typography>
        {work && (
          <Typography as="span" className={styles.department}>
            <WorkOutline />
            <span>{work}</span>
          </Typography>
        )}
        {department && (
          <Typography as="span" className={styles.department}>
            <Corporate />
            <span>{department}</span>
          </Typography>
        )}
      </div>
    </div>
  );
};

export default Item;
