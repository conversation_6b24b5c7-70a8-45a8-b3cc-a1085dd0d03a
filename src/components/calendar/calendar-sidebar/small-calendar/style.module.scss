@use 'mixins' as *;

.smallCalendar {
  display: block;

  .topBar {
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: size(16);

    .dateDisplay {
      display: inline-flex;
      color: var(--grey-900);
      font-size: size(22);
      font-weight: 700;
      line-height: size(16);
    }

    .navButtons {
      display: flex;
      gap: size(8);

      .navButton {
        width: size(16.3);
        height: size(16.3);
        background-color: transparent;
        border: none;
        outline: none;
        cursor: pointer;

        svg {
          width: size(16.3);
          height: size(16.3);
          fill: var(--grey-900);
        }
      }

      .navButtonPrev svg {
        transform: rotate(90deg);
      }

      .navButtonNext svg {
        transform: rotate(-90deg);
      }
      .navButtonPrev.rtl svg {
        transform: rotate(-90deg);
      }

      .navButtonNext.rtl svg {
        transform: rotate(90deg);
      }
    }
  }

  .calendar {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    row-gap: size(2.5);

    div[class*='day-name-item'] {
      font-size: size(14);
      font-weight: 700;
      line-height: size(10);
      color: var(--grey-900);
      padding: size(15) size(0) size(18);
      text-align: center;
    }

    div[class*='box'] {
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      width: size(37.15);
      height: size(37.15);
      // padding: size(11) size(0);
      cursor: pointer;
      span[class*='day-number'] {
        font-size: size(14);
      }
    }

    div[class*='disable'] {
      background-color: var(--white);
      span[class*='day-number'] {
        color: var(--disabled-border);
      }
    }

    div[class*='monthly']:not([class*='disable']),
    div[class*='box'][class*='marked-weekly'][class*='weekly'] {
      background-color: var(--cardBg);
    }

    div[class*='marked-weekly'],
    div[class*='marked-daily'],
    div[class*='monthly'] {
      color: var(--grey-800);
    }

    div[class*='marked-daily'] {
      span {
        background-color: var(--cardBg);
        width: size(37.15);
        height: size(37.15);
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    div[class*='current']:not([class*='disable']) {
      padding: size(0);
      [class*='day-number'] {
        background-color: var(--text-link-blue);
        color: var(--white);
        width: size(37.15);
        height: size(37.15);
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    div[class*='first'][class*='monthly'],
    div[class*='box']:not([class*='disable']):not([class*='first']):not(
        [class*='last']
      ) {
      width: 100%;
    }

    div[class*='marked-weekly'][class*='first'] {
      width: 100%;
    }
    div[class*='box'][class*='rtl']:nth-of-type(7n + 1),
    div[class*='box'][class*='rtl']:first-of-type,
    div[class*='box'][class*='rtl'][class*='first'] {
      border-radius: 0 50% 50% 0 !important;
    }

    div[class*='box'][class*='rtl']:nth-of-type(7n),
    div[class*='box'][class*='rtl']:last-of-type,
    div[class*='box'][class*='rtl'][class*='last'] {
      border-radius: 50% 0 0 50% !important;
    }

    /* LTR styles with higher specificity */
    div[class*='box'][class*='ltr']:nth-of-type(7n + 1),
    div[class*='box'][class*='ltr']:first-of-type,
    div[class*='box'][class*='ltr'][class*='first'] {
      border-radius: 50% 0 0 50% !important;
    }

    div[class*='box'][class*='ltr']:nth-of-type(7n),
    div[class*='box'][class*='ltr']:last-of-type,
    div[class*='box'][class*='ltr'][class*='last'] {
      border-radius: 0 50% 50% 0 !important;
    }
  }
}
