import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchBadgesService } from '@/services/totara/badges';

/**
 * Route to get the badges
 */
export async function GET() {
  try {
    // Fetching the badges
    const response = await TotaraFetchBadgesService();
    // Return the response
    return NextResponse.json({
      available: response?.data?.availablebadges || [],
      user: response?.data?.userbadges || [],
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
