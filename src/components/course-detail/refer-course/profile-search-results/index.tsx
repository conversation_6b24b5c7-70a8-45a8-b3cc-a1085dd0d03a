import React, { useCallback } from 'react';
import styles from './style.module.scss';
import IProfileResult from '@/types/domain/talent/profile/recommendations/profile-result';
import { User } from '@/ui/icons';
import Avatar from '@/ui/image-fetcher/avatar';

interface IProfileSearchResults {
  items?: IProfileResult[] | null;
  isVisible?: boolean;
  onClick?: (
    id: number | undefined,
    name: string | undefined,
    picture: string | undefined,
  ) => void;
}

const ProfileSearchResults: React.FC<IProfileSearchResults> = ({
  items,
  onClick,
  isVisible,
}) => {
  const handleClick = useCallback(
    (
      id: number | undefined,
      name: string | undefined,
      picture: string | undefined,
    ) => {
      if (onClick) {
        onClick(id, name, picture);
      }
    },
    [onClick],
  );

  return (
    <div
      className={styles.resultsWrapper + ` ${isVisible ? '' : styles.hidden}`}
    >
      <ul>
        {items?.map((item: IProfileResult) => (
          <li
            key={item.id}
            onClick={() => handleClick(item.id, item?.name, item?.picture)}
          >
            <div className={styles.result}>
              {item?.picture?.length ? (
                <Avatar
                  src={item?.picture}
                  name={item?.name}
                  className="img-fluid"
                />
              ) : (
                <User />
              )}
              <div className={styles.resultName}>{item?.name}</div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default ProfileSearchResults;
