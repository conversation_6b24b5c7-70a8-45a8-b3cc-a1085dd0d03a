import { FC, useCallback, useMemo, useState } from 'react';
import Typography from '@/ui/typography';

// Styles
import styles from './style.module.scss';
import EmptyMessage from '@/ui/empty-message';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';

interface IDescription {
  content?: string;
}

const Description: FC<IDescription> = (props) => {
  const { content } = props;
  const t = useTranslations();
  // Property to hold expand state
  const [showFullContent, setShowFullContent] = useState(false);

  // Dynamically limiting content
  const descriptionCotent = useMemo(() => {
    if (content) {
      return showFullContent ? content : `${content?.slice(0, 500)}...`;
    }
    return '';
  }, [content, showFullContent]);

  /**
   * Method to toggle expand state
   */
  const toggle = useCallback(() => {
    setShowFullContent((prev) => !prev);
  }, []);

  // Return JSX
  return (
    <div className={styles.description}>
      <Typography as="h4" dictionary={dictionary.description} />
      {descriptionCotent ? (
        <div>
          <div
            className={styles.descriptionText}
            dangerouslySetInnerHTML={{ __html: descriptionCotent }}
          ></div>
          {content?.length && content?.length > 500 ? (
            <button type="button" className={styles.moreBtn} onClick={toggle}>
              <span className="resizable">
                {showFullContent
                  ? t(dictionary.viewLess)
                  : t(dictionary.viewMore)}
              </span>
            </button>
          ) : null}
        </div>
      ) : (
        <EmptyMessage icon="search" title={dictionary.noInformationAvailable} />
      )}
    </div>
  );
};

export default Description;
