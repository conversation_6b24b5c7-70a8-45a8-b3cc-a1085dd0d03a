import { IMultidayModule } from '@/types/domain/learning/course';
import Typography from '@/ui/typography';
import { FC, useCallback, useState } from 'react';
import Session from './session';
import styles from './style.module.scss';
import { ChevronDown } from '@/ui/icons';

interface IEventProps {
  event: IMultidayModule;
}

const Event: FC<IEventProps> = ({ event }) => {
  const [isSessionOpen, setIsSessionOpen] = useState(false);

  const handleToggleOpen = useCallback(() => {
    setIsSessionOpen((prevIsOpen) => !prevIsOpen);
  }, []);

  return (
    <div className={styles.event}>
      <div className={styles.header}>
        <Typography as="span" className={styles.eventName}>
          {event.name}
        </Typography>
        <div
          className={`${styles.collapseIcon} ${isSessionOpen ? styles.open : ''}`}
          onClick={handleToggleOpen}
          role="button"
          tabIndex={0}
          aria-expanded={isSessionOpen}
          aria-controls="session-details"
          onKeyDown={(e) =>
            (e.key === 'Enter' || e.key === ' ') && handleToggleOpen()
          }
        >
          <ChevronDown />
        </div>
      </div>
      {isSessionOpen && (
        <div id="session-details">
          {event.sessions.map((session) => (
            <Session key={session.id} session={session} />
          ))}
        </div>
      )}
    </div>
  );
};

export default Event;
