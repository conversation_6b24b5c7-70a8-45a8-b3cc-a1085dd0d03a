import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';

export const _MONTH = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec',
];

interface ILocalizeAmPm {
  [key: string]: string;
}

export const useLocalizeAmPm = (string: string) => {
  const t = useTranslations();
  const mapDictionary: ILocalizeAmPm = {
    AM: t(dictionary.AM),
    am: t(dictionary.am),
    PM: t(dictionary.PM),
    pm: t(dictionary.pm),
  };

  string = string.replace(/AM|am|PM|pm/gi, function (matched) {
    return mapDictionary[matched];
  });

  return string;
};

export const useLocalizedMonths = () => {
  const t = useTranslations();
  return [
    t(dictionary.january),
    t(dictionary.february),
    t(dictionary.march),
    t(dictionary.april),
    t(dictionary.may),
    t(dictionary.june),
    t(dictionary.july),
    t(dictionary.august),
    t(dictionary.september),
    t(dictionary.october),
    t(dictionary.november),
    t(dictionary.december),
  ];
};

export const useLocalizedShortDays = () => {
  const t = useTranslations();
  return [
    t(dictionary.mon),
    t(dictionary.tue),
    t(dictionary.wed),
    t(dictionary.thu),
    t(dictionary.fri),
    t(dictionary.sat),
    t(dictionary.sun),
  ];
};

export const useLocalizedShortDaysSmallCalendar = () => {
  const t = useTranslations();
  return [
    t(dictionary.mo),
    t(dictionary.tu),
    t(dictionary.we),
    t(dictionary.th),
    t(dictionary.fr),
    t(dictionary.sa),
    t(dictionary.su),
  ];
};

export const useLocalizedDays = () => {
  const t = useTranslations();
  return [
    t(dictionary.sunday),
    t(dictionary.monday),
    t(dictionary.tuesday),
    t(dictionary.wednesday),
    t(dictionary.thursday),
    t(dictionary.friday),
    t(dictionary.saturday),
  ];
};
