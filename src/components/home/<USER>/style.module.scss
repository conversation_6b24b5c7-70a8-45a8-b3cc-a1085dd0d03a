@use 'mixins' as *;

.greatingPannel {
  border: 1px solid var(--grey-400);
  background: var(--warning-400);
  min-height: initial;
  color: var(--grey-900);
}

.box {
  position: relative;
  @include forMarginPadding(padding, size(0), size(0), size(0), size(84));

  h1 {
    font-size: size(22);
    font-weight: 700;
    @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
  }

  p {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }
}

.icon {
  width: size(60);
  height: size(60);
  position: absolute;
  @include leftToRight(0);
  top: 0;
}

div.greetingLoading {
  min-height: size(113);
}

@include for-dark-theme() {
  .greatingPannel {
    border: 1px solid #302e2d;
    background: #302e2d;
  }
}
