import dictionary from '@/dictionaries';
import { CourseAgenda } from '@/types/domain/learning/course';
import Typography from '@/ui/typography';
import { FC } from 'react';
import styles from './style.module.scss';

interface IAgenda {
  agenda?: CourseAgenda[];
}

const Agenda: FC<IAgenda> = ({ agenda }) => {
  return (
    <div>
      <Typography as="h4" dictionary={dictionary.agenda} />
      {agenda?.map((a, i) => {
        return (
          <div key={i} className={styles.container}>
            <div className={styles.inner}>
              <div>
                <Typography as="h5">{a.title}</Typography>
                <Typography as="span">{a.desc}</Typography>
              </div>
              <Typography as="span" className={styles.duration}>
                {a.duration}
              </Typography>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default Agenda;
