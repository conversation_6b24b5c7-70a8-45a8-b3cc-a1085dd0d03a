import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraRequestToJoinNetworkService } from '@/services/totara/my-network/request-to-join-network';

/**
 * Route to request to join network
 */
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const id = request.nextUrl.searchParams.get('id') || '';
    // Fetching network enrollment
    const response = await TotaraRequestToJoinNetworkService(Number(id));
    // Return the response
    return NextResponse.json({
      data: response.data,
    });
  } catch (error) {
    // Rturn Error response
    return ErrorResponse(error);
  }
}
