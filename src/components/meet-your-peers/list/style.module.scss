@use 'mixins' as *;

.list {
  min-height: size(100);
  position: relative;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: size(16);
}

.isEmpty {
  display: flex;
}

.card {
  background: var(--white);
  @include borderRadius(var(--radius));
  @include forMarginPadding(padding, size(16), size(16), size(16), size(16));
  overflow: hidden;
  cursor: pointer;
  min-height: size(185);
  vertical-align: top;
  border: 1px solid var(--grey-400);
  position: relative;
}

.photo {
  width: size(64);
  height: size(64);
  @include borderRadius(50%);
  overflow: hidden;
  @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
  flex-shrink: 0;
}

.title {
  font-size: size(18);
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  display: inline;
  padding-bottom: 0;
  transition: all 0.5s linear;
  background: linear-gradient(
    to bottom,
    var(--grey-900) 0%,
    var(--grey-900) 98%
  );
  background-size: 0 1px;
  background-repeat: no-repeat;
  background-position: left 100%;
  font-weight: 600;

  display: inline-block;
  width: 100%;

  @include rtl {
    background-position: right 100%;
  }
}

.department {
  font-size: size(14);
  display: block;
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  svg ~ span {
    @include forMarginPadding(padding, size(0), size(7), size(0), size(7));
  }
  svg {
    fill: var(--grey-900);
    width: size(14);
  }
}

.right {
  width: 20%;
}

.rating {
  display: flex;
  align-items: center;
  line-height: 1;
  position: absolute;
  @include rightToLeft(size(16));
  top: size(16);

  i {
    width: 14px;
    height: 14px;
    position: relative;
    @include forMarginPadding(margin, size(0), size(5), size(0), size(0));
  }

  span {
    font-size: 12px;
    line-height: 12px;
  }
}

.wrapper {
  @include forMarginPadding(padding, size(0), size(32), size(0), size(0));
}

@include for-all-phone() {
  .list {
    grid-template-columns: repeat(1, 1fr);
  }

  .card {
    display: flex;
    align-items: center;
    min-height: initial;
  }

  .photo {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }

  .data {
    @include forMarginPadding(padding, size(0), size(0), size(0), size(16));
  }
}
