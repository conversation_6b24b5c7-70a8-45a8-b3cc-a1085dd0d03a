import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import useCourseDetailStore from '@/stores/learning/courses/detail';
import dictionary from '@/dictionaries';
import Button from '@/ui/form/button';
import DialogDrawer from '@/ui/dialog-drawer';
import { useTranslations } from 'next-intl';
import Booking from '../booking';
import Popup from '../popup';
import usePhysicalCourseStore from '@/stores/learning/courses/physical';

interface IBookSlotAction {
  isPendingApproval?: boolean;
}

const BookSlotAction: FC<IBookSlotAction> = ({ isPendingApproval }) => {
  const popupMessage = usePhysicalCourseStore((x) => x.popupMessage);
  const setPopupMessage = usePhysicalCourseStore((x) => x.setPopupMessage);
  const checkLineManager = usePhysicalCourseStore((x) => x.checkLineManager);
  const lineManagerCheck = usePhysicalCourseStore((x) => x.lineManagerCheck);
  const lineManagerCheckLoading = usePhysicalCourseStore((x) => x.isFetching);
  const selected = usePhysicalCourseStore((x) => x.selected);
  const setSelected = usePhysicalCourseStore((x) => x.setSelected);
  const bookASlot = usePhysicalCourseStore((x) => x.bookASlot);
  const isBooking = usePhysicalCourseStore((x) => x.isBooking);

  // Getting course detail
  const courseDetail = useCourseDetailStore((state) => state.detail);
  const t = useTranslations();
  const [isDialogOpened, setIsDialogOpened] = useState(false);

  const managerApprovalRequired = useMemo(() => {
    return courseDetail?.lessons?.some(
      (lesson) => lesson.manager_approval_required,
    );
  }, [courseDetail]);

  const displayText = () => {
    if (
      managerApprovalRequired &&
      courseDetail?.available_slots?.length === 1
    ) {
      return t(dictionary.requestToEnroll);
    } else if (managerApprovalRequired) {
      return t(dictionary.requestASlot);
    } else if (courseDetail?.available_slots?.length === 1) {
      return t(dictionary.enroll);
    } else {
      return t(dictionary.bookASlot);
    }
  };

  const handleBooking = useCallback(async () => {
    if (managerApprovalRequired && !lineManagerCheck?.success) {
      setIsDialogOpened(false);
      setPopupMessage({
        title: dictionary.lineManagerNotAssigned,
        message: dictionary.contactAdminTeam,
      });
      return;
    }

    const response = await bookASlot(courseDetail?.courseId as number);
    if (response.success) {
      setIsDialogOpened(false);
      if (managerApprovalRequired) {
        setPopupMessage({
          refreshData: true,
          title: dictionary.requestToEnrollReceived,
          message: dictionary.wellNotifyYouOutcome,
          slot: selected!,
        });
      } else {
        setPopupMessage({
          success: true,
          title: dictionary.slotConfirmedFor,
          slotName: selected?.slot,
          slot: selected!,
          refreshData: true,
        });
      }
      setSelected(null);
    }
  }, [
    managerApprovalRequired,
    lineManagerCheck?.success,
    bookASlot,
    courseDetail?.courseId,
    setPopupMessage,
    setSelected,
    selected,
  ]);

  const onBookSlot = useCallback(() => {
    if (courseDetail?.available_slots?.length === 1) {
      handleBooking();
    } else {
      setIsDialogOpened(true);
    }
  }, [courseDetail?.available_slots?.length, handleBooking]);

  useEffect(() => {
    checkLineManager();
  }, [checkLineManager]);

  useEffect(() => {
    if (
      Array.isArray(courseDetail?.assigned_slot) &&
      courseDetail.assigned_slot.length === 0 &&
      courseDetail?.available_slots?.length === 1
    ) {
      setSelected(courseDetail?.available_slots[0]);
    }
  }, [courseDetail?.assigned_slot, courseDetail?.available_slots, setSelected]);

  if (!courseDetail?.isSelfEnrollmentEnabled) return null;

  if (courseDetail?.available_slots?.length == 0) return null;

  if (!Array.isArray(courseDetail.assigned_slot) || isPendingApproval)
    return null;

  return (
    <>
      <Button
        color="black"
        type="button"
        onClick={onBookSlot}
        loading={isBooking || lineManagerCheckLoading}
      >
        {displayText()}
      </Button>
      <DialogDrawer
        isOpened={isDialogOpened}
        onClose={() => {
          setSelected(null);
          setIsDialogOpened(false);
        }}
        title={t(dictionary.bookYourSession)}
      >
        <Booking
          list={courseDetail.available_slots || []}
          handleBooking={handleBooking}
          managerApprovalRequired={managerApprovalRequired}
        />
      </DialogDrawer>
      {popupMessage && (
        <Popup
          isOpen={!!popupMessage}
          popupMessage={popupMessage}
          onClose={() => setPopupMessage(null)}
          courseId={courseDetail.courseId as unknown as string}
        />
      )}
    </>
  );
};

export default BookSlotAction;
