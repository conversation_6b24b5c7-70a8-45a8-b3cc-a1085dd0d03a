import { FC } from 'react';

// Styles
import styles from './style.module.scss';
import ICourse from '@/types/domain/learning/course';
import Type from '../../type';
import Favorite from '../../favorite';
import Provider from '../../provider';
import Title from '../../title';
import Image from '../../image';
import Tags from '../../tags';
import Progress from '../../progress';
import InfoList from '../../info-list';
import Options from '../../options';

interface IAlpha {
  course: ICourse;
}

const Alpha: FC<IAlpha> = (props) => {
  // Deconstructing props
  const { course } = props;

  // Return JSX
  return (
    <div className={styles.card}>
      <div className={styles.imageWrapper}>
        <Image
          course={course}
          hasLink
          className={styles.bg}
          alt={course?.title}
        />
      </div>

      <div className={styles.bottom}>
        <div className={styles.top}>
          <Type course={course} />
          <Favorite course={course} />
          <Options course={course} />
        </div>
        <Provider course={course} className={styles.provider} />
        <Title course={course} className={styles.heading} />
        <InfoList course={course} className={styles.listNav} />
        <Tags course={course} className={styles.cardTags} />
        <Progress course={course} className={styles.progress} />
      </div>
    </div>
  );
};

export default Alpha;
