import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchLearningPathwaysService } from '@/services/totara/learning-pathways';

export async function GET() {
  try {
    // Getting response
    const response = await TotaraFetchLearningPathwaysService({
      type: 'required',
      page: '1',
      perPage: '20',
    });

    // Return the response
    return NextResponse.json({
      progress: response?.data?.pathways?.['required']?.overallprogress,
      totalCourses: response?.data?.pathways?.['required']?.requiredcount,
      completedCourses: response?.data?.pathways?.['required']?.completedcount,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
