import { useEffect, useMemo } from 'react';
import CardsSection from '@/components/common/cards-section';
import CONFIG from '@/config';
import useShortCoursesStore from '@/stores/learning/courses/short';
import Course from '@/components/course';
import EmptyMessage from '@/ui/empty-message';
import { useTranslations } from 'next-intl';
import dictionaries from '@/dictionaries';
import dictionary from '@/dictionaries';

const ShortCourses = () => {
  // Getting loading state
  const isFetching = useShortCoursesStore((x) => x.isFetching);

  // Getting fetch action
  const onFetch = useShortCoursesStore((x) => x.fetch);

  // Getting short courses
  const list = useShortCoursesStore((x) => x.list);
  const t = useTranslations();
  // Creating dyanmic array of cards
  const items = useMemo(() => {
    if (list && list.length > 0) {
      return list.map((item) => (
        <Course key={item.id} course={item} variant="beta" />
      ));
    }
    return [];
  }, [list]);

  useEffect(() => {
    // fetching short courses
    onFetch();
  }, [onFetch]);

  // Return JSX
  return (
    <CardsSection
      title={t(dictionaries.LearningUnder30Mins)}
      listingPageUrl={CONFIG.routes.discover.short}
      items={items}
      limit={4}
      loading={isFetching}
      className="learningSlider3"
      slidesConfig={{
        desktop: {
          view: 4,
          gap: 16,
        },
        mobile: {
          view: 1.3,
          gap: 10,
        },
      }}
      noDataMessage={
        <EmptyMessage
          icon="search"
          title={t(dictionary.noSearchResultsFound)}
          description={t(dictionary.pleaseCheckThatAllWordsAreSpelledCorrectly)}
        />
      }
    />
  );
};

export default ShortCourses;
