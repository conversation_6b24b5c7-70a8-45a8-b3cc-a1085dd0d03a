import { useCallback, useState } from 'react';
import Typography from '@/ui/typography';
import { Profile } from '@/ui/icons';

// Styles
import styles from './style.module.scss';
import { useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';
interface IHeaderProps {
  title?: string;
  summary?: string;
  members?: number;
}

const Header: React.FC<IHeaderProps> = ({ title, summary, members }) => {
  const summaryTextLonger = summary?.length && summary?.length > 180;

  // Property to hold expand state
  const [showFullContent, setShowFullContent] = useState(!summaryTextLonger);
  /**
   * Method to toggle expand state
   */
  const toggle = useCallback(() => {
    setShowFullContent((prev) => !prev);
  }, []);
  const t = useTranslations();
  const summaryText =
    summaryTextLonger && !showFullContent ? summary.slice(0, 180) : summary;

  // Return JSX
  return (
    <>
      <Typography as="h1" className={styles.title}>
        {title}
      </Typography>
      <p>
        {summaryText}

        {!!summaryTextLonger && (
          <button type="button" className={styles.moreBtn} onClick={toggle}>
            <span className="resizable">
              {showFullContent
                ? t(dictionary.viewLess)
                : t(dictionary.viewMore)}
            </span>
          </button>
        )}
      </p>

      <div className={styles.members}>
        <i>
          <Profile />
        </i>
        <Typography as="span">
          {members} {t(dictionary.members)}
        </Typography>
      </div>
    </>
  );
};

export default Header;
