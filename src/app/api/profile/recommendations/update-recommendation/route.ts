import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';
import { ExternalUpdateRecommendationService } from '@/services/external/profile/recommendations/update-recommendation';
import { ExternalUpdateRecommendationNotifierService } from '@/services/external/profile/recommendations/update-recommendation-notifier';

/**
 * Route to get the learning types
 */
export async function POST(request: NextRequest) {
  try {
    // Getting request payload
    const data = await request.json();
    // Getting request params
    const id = request.nextUrl.searchParams.get('id') || '';
    // Fetching the recommendations ask
    const response = !data.isPublishUpdated
      ? await ExternalUpdateRecommendationService(data, id).then(() =>
          ExternalUpdateRecommendationNotifierService(data, data.talentId),
        )
      : await ExternalUpdateRecommendationService(data, id);

    return NextResponse.json({ user: response.data });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error as AxiosError);
  }
}
