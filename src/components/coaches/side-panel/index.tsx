import Typography from '@/ui/typography';
import BookedSessionItem from './item';

// Styles
import styles from './style.module.scss';
import NoRecord from './item/no-record';
import { FC, useEffect } from 'react';
import useCoachBookingStore from '@/stores/learning/coaches/booking';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '../list/loading';
import moment from 'moment';
import 'moment/locale/ar';
import dictionary from '@/dictionaries';
import { useTranslations, useLocale } from 'next-intl';
import useCoachesStore from '@/stores/learning/coaches/list';
import BookedCoachItem from './booked-coach/item';
import { coachExperience } from '../mock-data';

interface ISidePanel {
  handleOpenBookedCoachesDrawer: () => void;
}

const SidePanel: FC<ISidePanel> = ({ handleOpenBookedCoachesDrawer }) => {
  const {
    bookedSessions,
    isBooking,
    fetchBookedSession,
    isFetching,
    setViewAllBookedSession,
    error,
  } = useCoachBookingStore();

  const { list } = useCoachesStore();

  useEffect(() => {
    fetchBookedSession();
  }, [fetchBookedSession]);
  const t = useTranslations();
  const locale = useLocale();

  const bookedFutureSessionsItems = bookedSessions
    ?.filter(
      (session) => Number(session.startime) > Math.floor(Date.now() / 1000),
    )
    .sort((a, b) => {
      const aTime = Number(a.startime);
      const bTime = Number(b.startime);
      return aTime - bTime;
    })
    .slice(0, 2);

  const bookedPastSessionsItems = bookedSessions?.filter(
    (session) => Number(session.startime) < Math.floor(Date.now() / 1000),
  );

  const sessionCount =
    bookedSessions?.filter(
      (session) => Number(session.startime) > Math.floor(Date.now() / 1000),
    )?.length || 0;

  const renderSessions = () => {
    if (error) {
      return (
        <>
          <Typography as="h6">{t(dictionary.somethingWentWrong)}</Typography>
          <Typography as="p">{error}</Typography>
        </>
      );
    }
    if (bookedFutureSessionsItems && bookedFutureSessionsItems.length > 0) {
      return (
        <div className={styles.containerSessions}>
          {bookedFutureSessionsItems.map((session) => (
            <BookedSessionItem
              key={session.slotid}
              title={session.coach.fullname}
              userImg={session.coach.imgaeurl}
              designation={session.coach.jobtitle}
              day={session.startime || ''}
              time={`${moment
                .unix(Number(session.startime))
                .locale(locale === 'ar' ? 'ar' : 'en')
                .format('h:mma')} - ${moment
                .unix(Number(session.endtime))
                .locale(locale === 'ar' ? 'ar' : 'en')
                .format('h:mma')}`}
              location={session.location}
            />
          ))}
        </div>
      );
    } else if (bookedSessions && bookedSessions.length > 0) {
      return <NoRecord text={t(dictionary.noUpComingSessions)} />;
    } else {
      return (
        <NoRecord
          text={t(dictionary.dontHaveAnySessions)}
          subText={t(dictionary.exploreAvailableSessions)}
        />
      );
    }
  };

  const showViewAllButton =
    bookedSessions?.length &&
    (bookedSessions.length > 2 ||
      (bookedSessions.length > 0 &&
        bookedFutureSessionsItems &&
        bookedFutureSessionsItems.length < 2 &&
        bookedPastSessionsItems &&
        bookedPastSessionsItems.length >= 1));

  return (
    <div className={styles.sideBar}>
      <div className={styles.sideBarItem}>
        <WithLoader
          loader={<Loading />}
          loading={isFetching || (isBooking !== null && isBooking)}
        >
          <Typography
            as="h4"
            className={
              bookedFutureSessionsItems && bookedFutureSessionsItems?.length > 0
                ? styles.title
                : styles.titleNoSession
            }
            dictionary={dictionary.bookedSessions}
          />
          {bookedFutureSessionsItems && sessionCount > 0 && (
            <Typography as="p" className={styles.subtitle}>
              {t('bookedSessionsCount', { count: sessionCount })}
            </Typography>
          )}
          {renderSessions()}
          {showViewAllButton ? (
            <div className={styles.viewAllWrapper}>
              <button
                className={styles.viewAllButton}
                onClick={() => setViewAllBookedSession(true)}
              >
                <Typography dictionary={dictionary.viewAll} />
              </button>
            </div>
          ) : null}
        </WithLoader>
      </div>
      <div className={styles.sideBarItem}>
        <WithLoader
          loader={<Loading />}
          loading={isFetching || (isBooking !== null && isBooking)}
        >
          <Typography
            as="h4"
            className={
              bookedFutureSessionsItems && bookedFutureSessionsItems?.length > 0
                ? styles.title
                : styles.titleNoSession
            }
          >
            My Saved Coaches
          </Typography>
          {list.slice(0, 2)?.map((coach) => (
            <BookedCoachItem
              key={coach?.id}
              coachId={coach?.id}
              photo={coach?.picture}
              title={coach?.firstname?.concat(' ', coach?.lastname || '')}
              designation={coach?.designation}
              rating={parseFloat(coach?.rating || '')}
              experience={coachExperience}
              experienceDisplayCount={1}
            />
          ))}
          {showViewAllButton ? (
            <div className={styles.viewAllWrapper}>
              <button
                className={styles.viewAllButton}
                onClick={handleOpenBookedCoachesDrawer}
              >
                <Typography dictionary={dictionary.viewAll} />
              </button>
            </div>
          ) : null}
        </WithLoader>
      </div>
    </div>
  );
};

export default SidePanel;
