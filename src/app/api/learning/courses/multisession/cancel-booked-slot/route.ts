import { ErrorResponse } from '@/utils/fetcher';
import { NextRequest, NextResponse } from 'next/server';
import { AxiosError } from 'axios';
import { TotaraMultisessionCancelBookedSlotService } from '@/services/totara/courses/multisession';

export async function POST(request: NextRequest) {
  try {
    // Getting request body
    const { courseId, slotId } = await request.json();

    const response = await TotaraMultisessionCancelBookedSlotService(
      courseId,
      slotId,
    );
    // Return the response
    return NextResponse.json({
      cancelingResponse: response?.data,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error as AxiosError);
  }
}
