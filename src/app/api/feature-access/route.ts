import { externalFeatureAccessService } from '@/services/external/feature-access';
import { ErrorResponse } from '@/utils/fetcher';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();
    const response = await externalFeatureAccessService(email);
    return NextResponse.json({
      features: response.data.features,
    });
  } catch (error) {
    return ErrorResponse(error);
  }
}
