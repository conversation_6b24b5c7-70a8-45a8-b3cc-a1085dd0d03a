@use 'mixins' as *;

.quote {
  position: relative;
  @include forMarginPadding(padding, size(0), size(140), size(0), size(195));
}

.icon {
  position: absolute;
  @include leftToRight(size(90));
  top: size(30);
  width: size(57);
  height: size(57);

  svg {
    width: 100%;
    height: 100%;
    fill: var(--oat-milk-800);
  }

  @include rtl {
    transform: scaleX(-1);
  }
}

.quoteTitle {
  font-size: size(16);
  font-weight: 300;
  @include forMarginPadding(margin, size(0), size(0), size(10), size(0));
  display: block;
  text-transform: uppercase;
}

.title {
  font-size: size(40);
  font-weight: 300;
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));
}

.name {
  font-size: size(16);
  font-weight: 400;
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  @include forMarginPadding(padding, size(0), size(0), size(0), size(30));
  position: relative;
  display: block;

  &:before {
    position: absolute;
    @include leftToRight(0);
    top: 0;
    bottom: 0;
    @include forMarginPadding(margin, auto, auto, auto, auto);
    background: var(--grey-900);
    height: 1px;
    width: size(20);
    content: '';
  }
}

@include for-all-phone() {
  .quote {
    @include forMarginPadding(padding, size(30), size(0), size(20), size(50));
  }

  .icon {
    @include leftToRight(size(0));
    width: size(32);
    height: size(32);
  }

  .quoteTitle {
    font-size: size(12);
    @include forMarginPadding(margin, size(0), size(0), size(15), size(0));
  }

  .title {
    font-size: size(28);
  }

  .name {
    font-size: size(14);
  }
}
