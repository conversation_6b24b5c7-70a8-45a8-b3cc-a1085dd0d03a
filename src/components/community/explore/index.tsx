import Loading from '@/components/common/loading';
import useMyCommunitiesStore from '@/stores/my-network/my-communities';
import WithLoader from '@/ui/skeleton/with-loader';
import styles from './style.module.scss';
import Typography from '@/ui/typography';
import Paper from '@/ui/paper';
import dictionary from '@/dictionaries';
import { useEffect, useMemo } from 'react';
import CommunityItem from './item';
import { Link } from '@/i18n/routing';

const ExploreCommunitiesWidget = () => {
  const communities = useMyCommunitiesStore((state) => state.otherCommunities);
  const isFetching = useMyCommunitiesStore((state) => state.isFetching);
  const fetchCommunities = useMyCommunitiesStore(
    (state) => state.fetchCommunities,
  );

  const topCommunities = useMemo(() => {
    return communities
      .filter((c) => c.visible !== 0 && c.members)
      .sort((a, b) => b.members! - a.members!)
      .slice(0, 5);
  }, [communities]);

  useEffect(() => {
    if (!communities || communities.length === 0) {
      fetchCommunities();
    }
  }, [communities, fetchCommunities]);

  return (
    <Paper>
      <Typography
        as="h2"
        className={styles.title}
        dictionary={dictionary.exploreCommunities}
      />
      <WithLoader loading={isFetching} loader={<Loading />}>
        {topCommunities?.map((community) => (
          <CommunityItem key={community.networkid} community={community} />
        ))}
      </WithLoader>
      <div className={styles.viewAll}>
        <Link href="/social">
          <Typography as="span" dictionary={dictionary.viewAll} />
        </Link>
      </div>
    </Paper>
  );
};

export default ExploreCommunitiesWidget;
