import { FC } from 'react';
import Image from 'next/image';
import Typography from '@/ui/typography';
import { IReview } from '@/types/domain/learning/course';
import EmptyMessage from '@/ui/empty-message';

//Assets
import star from '@public/images/star.svg';

//Styles
import styles from './style.module.scss';
import dictionary from '@/dictionaries';

interface IReviews {
  reviews?: IReview[];
  overallRating?: string;
}

const Reviews: FC<IReviews> = (props) => {
  const { reviews = [], overallRating } = props;

  return (
    <div className={styles.reviewBox}>
      <div className={styles.header}>
        <Typography as="h4" dictionary={dictionary.reviews} />
        {reviews && reviews.length ? (
          <div className={styles.overallRating}>
            <i>
              <Image src={star} alt="" width={20} height={20} />
            </i>

            <Typography as="span" className={styles.ratingText}>
              {overallRating} ({reviews.length})
            </Typography>

            <Typography
              as="span"
              className={styles.overRating}
              dictionary={dictionary.overallRatings}
            />
          </div>
        ) : null}
      </div>
      {reviews && reviews.length ? (
        <nav>
          <ul className={styles.reviewList}>
            {reviews.map((review) => (
              <li key={review.id}>
                <div className={styles.imageBox}>
                  <Image
                    src={review.userimage}
                    alt=""
                    className="img-fluid"
                    width={100}
                    height={100}
                  />
                </div>
                <div className={styles.text}>
                  <Typography as="span" className={styles.name}>
                    {review?.username}
                  </Typography>
                  <div className={styles.review}>
                    <div className={styles.rating}>
                      <i>
                        <Image src={star} alt="" width={20} height={20} />
                      </i>
                      <Typography as="span">{review.review_rating}</Typography>
                    </div>
                    <Typography as="span" className={styles.ago}>
                      {review.review_date}
                    </Typography>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </nav>
      ) : (
        <EmptyMessage
          icon="members"
          title={dictionary.noInformationAvailable}
        />
      )}
    </div>
  );
};

export default Reviews;
