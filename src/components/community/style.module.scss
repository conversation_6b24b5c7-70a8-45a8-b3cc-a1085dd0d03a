@use 'mixins' as *;

.headerFixed {
  position: sticky;
  top: size(60);
  z-index: 2;
  overflow: hidden;
  @include forMarginPadding(margin, size(0), size(0), size(24), size(0));
}

.head {
  @include forMarginPadding(padding, size(25), size(25), size(0), size(25));

  nav {
    border-bottom: none;
  }
}

@include for-all-phone() {
  .headerFixed {
    position: initial;
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }
}
