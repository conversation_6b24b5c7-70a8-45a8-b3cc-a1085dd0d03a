import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import Typography from '@/ui/typography';
import Button from '@/ui/form/button';
import { LeftArrow } from '@/ui/icons';
import useCoachSlotsStore from '@/stores/learning/coaches/slots';
import styles from './style.module.scss';
import Slots from './slots';
import useCoachBookingStore from '@/stores/learning/coaches/booking';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';
import SelectedInfo from './selectedInfo';
import dictionary from '@/dictionaries';
import { useLocale, useTranslations } from 'next-intl';
import RightArrow from '@/ui/icons/right-arrow';

interface IBooking {
  coachId: string;
  onBack: () => void;
  onSucess: () => void;
}

// React component to handle coach booking
const Booking: FC<IBooking> = (props) => {
  // Deconstructing props
  const { coachId, onBack, onSucess } = props;

  // Property to hold error message
  const [error, setError] = useState<string | null>(null);

  // Getting method to book a slot
  const onBook = useCoachBookingStore((x) => x.book);

  // Getting selected slot
  const selected = useCoachBookingStore((x) => x.selected);

  // Getting selected slot
  const setSelected = useCoachBookingStore((x) => x.setSelected);

  // Getting the booking state
  const isBooking = useCoachBookingStore((x) => x.isBooking);

  // Getting list of available slots
  const list = useCoachSlotsStore((x) => x.list);

  // Method to fetch the available slots
  const onFetch = useCoachSlotsStore((x) => x.fetch);

  // Getting fetching state
  const isFetching = useCoachSlotsStore((x) => x.isFetching);

  const t = useTranslations();

  // Method to handle the days
  const days = useMemo(() => {
    // Property to hold the unique days
    const unique: string[] = [];
    // Filtring the unique days
    list.forEach((slot) => {
      if (slot?.slot_date && !unique.includes(slot.slot_date)) {
        unique.push(slot.slot_date);
      }
    });
    // Returning the unique days
    return unique;
  }, [list]);

  // Method to handle booking
  const handleBooking = useCallback(async () => {
    // Claering the error
    setError(null);
    // Booking the slot
    const response = await onBook();
    // If booking is successfull
    if (response?.status && response?.message) {
      onSucess();
    }
    // If booking is unsuccessfull
    if (!response?.status && response?.message) {
      setError(response?.message);
    }
  }, [onBook, onSucess, setError]);

  const locale = useLocale();
  // Effect to fetch the available slots
  useEffect(() => {
    // If coach id is available
    if (coachId) {
      // Claering the error
      setError(null);
      // Fetching the booking slots
      onFetch(coachId);
    }
  }, [coachId, onFetch, setError]);

  useEffect(() => {
    setSelected({ slot_id: '' });
  }, [setSelected]);

  // Return JSX
  return (
    <>
      <div className={styles.head}>
        <button type="button" className={styles.backDetail} onClick={onBack}>
          <i className="fa-icon">
            {locale == 'ar' ? <RightArrow /> : <LeftArrow />}
          </i>
          <Typography as="span" dictionary={dictionary.bookYourSession} />
        </button>
      </div>
      <div className={styles.body}>
        <WithLoader loader={<Loading />} loading={isFetching}>
          <nav className={styles.calendar}>
            <ul>
              <li>
                <div className={styles.left}>
                  <Typography as="h5" dictionary={dictionary.date} />
                </div>
                <div className={styles.data}>
                  <Typography
                    as="h5"
                    dictionary={dictionary.availableTimeSlot}
                  />
                </div>
              </li>
              {days.map((x) => (
                <Slots key={x} date={x} list={list} />
              ))}
            </ul>
          </nav>
          {selected && selected.slot_id && <SelectedInfo />}
          {error && (
            <div className="text-center mt-3">
              <Typography as="h6" className="m-0">
                {error}
              </Typography>
            </div>
          )}
        </WithLoader>
      </div>
      <div className={styles.footer}>
        <Button
          type="button"
          color="black"
          disabled={!selected?.slot_id}
          loading={isBooking}
          onClick={handleBooking}
          className={styles.innerBookCta}
        >
          {t(dictionary.bookYourSession)}
        </Button>
      </div>
    </>
  );
};

export default Booking;
