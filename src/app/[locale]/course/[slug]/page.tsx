import { Metadata } from 'next';
import { TotaraFetchCourseDetailService } from '@/services/totara/courses';
import CourseDetailScreen from '@/screens/course-detail';

type Props = {
  params: Promise<{ slug: string; locale: string }>;
};

export const generateMetadata = async ({
  params,
}: Props): Promise<Metadata> => {
  const { slug: courseId, locale } = await params;

  try {
    const response = await TotaraFetchCourseDetailService(courseId);

    if (response.data.exception) {
      throw new Error(response.data.message);
    }

    return {
      title: response.data.course_name,
      description: response.data.course_description.replace(/<[^>]*>/g, ''),
      openGraph: {
        title: response.data.course_name,
        description: response.data.course_description.replace(/<[^>]*>/g, ''),
        url: `${process.env.BASE_URL}/${locale}/course/${courseId}`,
        type: 'website',
        images: [
          {
            url: response.data.course_image,
            secureUrl: response.data.course_image,
          },
        ],
      },
      twitter: {
        title: response.data.title,
        description: response.data.course_description.replace(/<[^>]*>/g, ''),
        images: [
          {
            url: response.data.course_image,
            secureUrl: response.data.course_image,
          },
        ],
      },
    };
  } catch (err) {
    console.error(err);
    return {};
  }
};

export default function CourseDetailPage() {
  return <CourseDetailScreen />;
}
