import React, { useState, useRef, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import styles from './style.module.scss';
import { ArrowDownFrame } from '@/ui/icons';

type TimePeriod = 'daily' | 'weekly' | 'monthly';

const TIME_PERIOD_OPTIONS: TimePeriod[] = ['daily', 'weekly', 'monthly'];

interface IViewPeriod {
  selectedTimePeriod: TimePeriod;
  onTimePeriodChange: (newTimePeriod: TimePeriod) => void;
}

const ViewPeriod: React.FC<IViewPeriod> = ({
  selectedTimePeriod,
  onTimePeriodChange,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const dropdownRef = useRef<HTMLDivElement>(null);

  const t = useTranslations();

  const toggleDropdown = () => {
    setIsOpen((prev) => !prev);
  };

  const handleSelect = (timePeriod: TimePeriod) => {
    onTimePeriodChange(timePeriod);
    setIsOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const getDisplayLabel = (value: TimePeriod) => {
    return t(value);
  };

  return (
    <div className={styles.timePeriodContainer} ref={dropdownRef}>
      <button
        type="button"
        className={`${styles.timePeriodButton} ${isOpen ? styles.open : ''}`}
        onClick={toggleDropdown}
        aria-haspopup="true" // Indicates that this element controls a popup
        aria-expanded={isOpen ? 'true' : 'false'} // Indicates the current state of the popup
      >
        {getDisplayLabel(selectedTimePeriod)}
        <ArrowDownFrame />
      </button>

      {isOpen && (
        <div
          className={styles.optionsContainer}
          role="menu"
          aria-orientation="vertical"
          aria-labelledby="timePeriodSelectButton"
        >
          {TIME_PERIOD_OPTIONS.map((period) => (
            <button
              key={period}
              onClick={() => handleSelect(period as TimePeriod)}
              className={`
                  ${styles.menuItem}
                  ${selectedTimePeriod === period ? styles.selectedMenuItem : ''}
                `}
              role="menuitem"
            >
              {t(period)}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default ViewPeriod;
