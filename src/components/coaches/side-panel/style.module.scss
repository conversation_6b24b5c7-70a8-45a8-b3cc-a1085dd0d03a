@use 'mixins' as *;

.sideBar {
  display: flex;
  flex-direction: column;
  gap: size(20);
}

.sideBarItem {
  @include forMarginPadding(padding, size(24), size(16), size(16), size(16));
  @include borderRadius(var(--radius));
  background: var(--white);
  min-height: size(200);
  position: relative;
}

.title {
  @include forMarginPadding(margin, size(0), size(0), size(12), size(0));
}

.titleNoSession {
  @include forMarginPadding(margin, size(0), size(0), size(24), size(0));
}

.subtitle {
  @include forMarginPadding(margin, size(0), size(0), size(24), size(0));
}

.card {
  @include forMarginPadding(padding, size(16), size(16), size(16), size(16));
  @include borderRadius(16px);
  overflow: hidden;
  min-height: size(100);
  border: solid 1px var(--oat-milk-600);
  // background: var(--bg-shade-7);

  + .card {
    @include forMarginPadding(margin, size(16), size(0), size(0), size(0));
  }

  + .noRecord {
    @include forMarginPadding(margin, size(10), size(0), size(0), size(0));
  }
}

.header {
  display: flex;
  flex-direction: row;
  @include forMarginPadding(padding, size(0), size(0), size(16), size(0));
  border-bottom: solid 1px var(--grey-400);

  .photo {
    width: size(70);
    height: size(70);
    @include borderRadius(size(6));
  }

  .options {
    svg {
      width: size(15);
      height: size(19);
      cursor: pointer;
    }
  }
}

// .photo {
//   width: size(48);
//   height: size(48);
//   @include borderRadius(50%);
//   overflow: hidden;
//   display: flex;
//   align-self: top;
// }

.sessionDate {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: size(51);
  width: size(48);
  background-color: var(--blue-700);
  @include borderRadius(4px);
  @include forMarginPadding(padding, size(12), size(12), size(12), size(12));

  font-weight: 700;
  font-size: size(14);
  color: var(--always-dark);

  .month {
    font-weight: 400;
  }
}

.info {
  @include forMarginPadding(padding, size(0), size(0), size(0), size(16));
  flex-grow: 1;

  .sessionWith {
    font-size: size(10);
    color: var(--grey-800);
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: size(0);

    @include forMarginPadding(margin, size(0), size(0), size(10), size(0));
  }

  .title {
    font-size: size(16);
    color: var(--grey-900);
    line-height: 1.2;
    letter-spacing: 0;
  }
}

.helpTxt {
  font-size: size(12);
  display: block;
  @include forMarginPadding(margin, size(0), size(0), size(5), size(0));
}

.title {
  font-size: size(18);
  display: block;
  @include forMarginPadding(margin, size(0), size(0), size(5), size(0));
}

.designation {
  font-size: size(14);
  color: var(--grey-700);
  display: block;
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
}

.body {
  display: flex;
  gap: size(11);
  @include forMarginPadding(padding, size(16), size(0), size(0), size(0));
}

.detailList {
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
  flex-grow: 1;

  li {
    list-style: none;
    display: flex;
    align-items: flex-start;
    @include forMarginPadding(margin, size(6), size(0), size(0), size(0));

    .calendarIcon {
      svg {
        fill: var(--grey-900);
      }
    }

    i {
      width: size(14);
      height: size(14);
      @include forMarginPadding(margin, size(0), size(8), size(0), size(0));

      svg {
        vertical-align: top;
      }
    }

    span {
      font-size: size(12);
      font-weight: 400;
    }

    .meetingLinkTypography:hover {
      cursor: pointer;
    }
  }
}

.noRecord {
  @include forMarginPadding(padding, size(24), size(16), size(24), size(16));
  overflow: hidden;
  min-height: size(100);
  background: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
}

.noImage {
  width: size(98);
  height: size(75);
  @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
}

@include for-dark-theme() {
  .noImage {
    svg {
      path {
        fill: var(--grey-900);
      }
    }
  }

  .header {
    border-bottom: solid 1px var(--grey-500);
  }
}

.heading {
  font-size: size(14);
  font-weight: 700;
  display: block;
  color: var(--grey-900);
  @include forMarginPadding(margin, size(0), size(0), size(5), size(0));
}

.text {
  font-size: size(12);
  display: block;
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  color: var(--grey-800);
}

.viewAllButton {
  background-color: transparent;
  border: 0;
  color: var(--grey-800);
  cursor: pointer;

  p {
    margin: 0;
  }
}

.viewAllWrapper {
  text-align: center;
  @include forMarginPadding(padding, size(24), size(0), size(8), size(0));
  @include forMarginPadding(margin, size(24), size(0), size(0), size(0));
  border-top: solid 1px var(--grey-400);
}
