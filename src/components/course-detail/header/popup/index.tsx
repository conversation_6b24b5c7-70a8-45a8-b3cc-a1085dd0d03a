import { FC, useRef, useEffect } from 'react';
import styles from './style.module.scss';
import Typography from '@/ui/typography';
import Button from '@/ui/form/button';
import dictionary from '@/dictionaries';
import { CalendarRounded, Checkmark, Clock, Pin } from '@/ui/icons';
import { useLocale } from 'next-intl';
import { formatLocalTime } from '@/utils/time';
import { format } from '@/utils/date';
import moment from 'moment';
import { PopupMessage } from '@/stores/learning/courses/physical';
import useCourseDetailStore from '@/stores/learning/courses/detail';

interface IPopup {
  isOpen?: boolean;
  onClose: () => void;
  popupMessage: PopupMessage;
  courseId: string;
}

const Popup: FC<IPopup> = ({ isOpen, onClose, popupMessage, courseId }) => {
  const { title, message, success, slot, refreshData, slotName } = popupMessage;
  const dialogRef = useRef<HTMLDialogElement>(null);
  const onFetch = useCourseDetailStore((state) => state.fetch);
  const locale = useLocale();

  const localStartDate = formatLocalTime(slot?.startdatetime);
  const localEndDate = formatLocalTime(slot?.enddatetime);

  const formattedDate =
    slot?.date && moment(slot?.date, 'YYYY-MM-DD').isValid()
      ? format(slot.date as string, locale, 'YYYY-MM-DD', 'dddd, DD MMM YYYY')
      : 'DD MMM YYYY';

  useEffect(() => {
    if (isOpen) {
      dialogRef.current?.showModal();
    } else {
      dialogRef.current?.close();
    }
  }, [isOpen]);

  // Return JSX
  return (
    <dialog
      ref={dialogRef}
      className={
        styles.confirmationDialog +
        ` ${isOpen ? styles.confirmationDialogOpened : ''}`
      }
    >
      <div className={styles.confirmationDialogTop}>
        {success ? (
          <div className={styles.iconWrapper}>
            <Checkmark />
          </div>
        ) : null}
        {title ? (
          <Typography
            dictionary={title}
            as="h2"
            className={styles.confirmationDialogTitle}
          />
        ) : null}
        {message ? (
          <Typography
            dictionary={message}
            as="p"
            className={styles.confirmationDialogMessage}
          />
        ) : null}
        {slotName ? (
          <Typography as="p" className={styles.confirmationDialogMessage}>
            {slotName}
          </Typography>
        ) : null}
        {slot ? (
          <div className={styles.slot}>
            <div className={styles.slotItem}>
              <span className={styles.dateIconWrapper}>
                <CalendarRounded />
              </span>
              <span className={styles.dateText}>{formattedDate}</span>
            </div>
            <div className={styles.slotItem}>
              <span className={styles.dateIconWrapper}>
                <Clock />
              </span>
              <span className={styles.dateText}>
                {localStartDate
                  .locale(locale === 'ar' ? 'ar' : 'en')
                  .format('h:mma')}{' '}
                -{' '}
                {localEndDate
                  .locale(locale === 'ar' ? 'ar' : 'en')
                  .format('h:mma')}
              </span>
            </div>
            {slot.location &&
              !Array.isArray(slot.location) &&
              slot.location.room && (
                <div className={styles.slotItem}>
                  <span className={styles.dateIconWrapper}>
                    <Pin />
                  </span>
                  <span className={styles.dateText}>{slot.location.room}</span>
                </div>
              )}
          </div>
        ) : null}
      </div>
      <div className={styles.confirmationDialogActions}>
        <Button
          dictionary={dictionary.close}
          className={styles.confirmationDialogClose}
          onClick={() => {
            onClose();
            if (refreshData) {
              onFetch(courseId);
            }
          }}
        />
      </div>
    </dialog>
  );
};

export default Popup;
