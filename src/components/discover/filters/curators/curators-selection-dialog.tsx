import { useState, useMemo, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import DialogDrawer from '@/ui/dialog-drawer';
import Button from '@/ui/form/button';
import { Checkmark, Close, SearchIcon } from '@/ui/icons';
import dictionary from '@/dictionaries';
import styles from './style.module.scss';

interface CuratorsSelectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  allCreators: string[];
  initialSelection: string[];
  onApply: (newSelection: string[]) => void;
}

const CuratorsSelectionDialog = ({
  isOpen,
  onClose,
  allCreators,
  initialSelection,
  onApply,
}: CuratorsSelectionDialogProps) => {
  const t = useTranslations();
  const [searchTerm, setSearchTerm] = useState('');
  const [tempSelection, setTempSelection] =
    useState<string[]>(initialSelection);

  const dialogTitle = `${t(dictionary.Select)} ${t(dictionary.Curators)}`;

  useEffect(() => {
    if (isOpen) {
      setTempSelection(initialSelection);
    }
  }, [isOpen, initialSelection]);

  const filteredCreators = useMemo(() => {
    if (!searchTerm.trim()) return allCreators;
    return allCreators.filter((creator) =>
      creator.toLowerCase().includes(searchTerm.toLowerCase()),
    );
  }, [allCreators, searchTerm]);

  const selectionHasChanged = useMemo(() => {
    const initialSet = new Set(initialSelection);
    const tempSet = new Set(tempSelection);
    return (
      initialSet.size !== tempSet.size ||
      !Array.from(tempSet).every((item) => initialSet.has(item))
    );
  }, [tempSelection, initialSelection]);

  const handleSelectionToggle = (creator: string) => {
    setTempSelection((prev) =>
      prev.includes(creator)
        ? prev.filter((c) => c !== creator)
        : [...prev, creator],
    );
  };

  const handleApply = () => {
    onApply(tempSelection);
    onClose();
  };

  return (
    <DialogDrawer isOpened={isOpen} onClose={onClose} title={dialogTitle}>
      <div className={styles.curatorsSelectionDialog}>
        <div className={styles.curatorsSearch} role="search">
          <input
            type="search"
            className={styles.searchInput}
            placeholder={t(dictionary.Search)}
            aria-label="Search for creators"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          {searchTerm.length === 0 ? (
            <span className={styles.searchIcon} aria-hidden="true">
              <SearchIcon />
            </span>
          ) : (
            <button
              type="button"
              className={styles.deleteIcon}
              onClick={() => setSearchTerm('')}
              aria-label="Clear search"
            >
              <Close />
            </button>
          )}
        </div>

        <div className={styles.curatorDialogList} id="creator-list">
          {filteredCreators.map((creator) => (
            <div
              key={`modal-${creator}`}
              className={`${styles.curatorDialogItem} ${
                tempSelection.includes(creator)
                  ? styles.checked
                  : styles.notChecked
              }`}
              role="checkbox"
              aria-checked={tempSelection.includes(creator)}
              tabIndex={0}
              onClick={() => handleSelectionToggle(creator)}
              onKeyDown={(e) => {
                if (e.key === ' ' || e.key === 'Enter') {
                  e.preventDefault();
                  handleSelectionToggle(creator);
                }
              }}
              aria-labelledby={`label-text-${creator.replace(/\s+/g, '-')}`}
            >
              <span
                id={`label-text-${creator.replace(/\s+/g, '-')}`}
                className={styles.labelText}
              >
                {creator}
              </span>
              <input
                type="checkbox"
                value={creator}
                checked={tempSelection.includes(creator)}
                onChange={() => handleSelectionToggle(creator)}
                name={`label-text-${creator.replace(/\s+/g, '-')}`}
                aria-labelledby={`label-text-${creator.replace(/\s+/g, '-')}`}
                className={styles.checkboxInput}
                tabIndex={-1}
                style={{
                  position: 'absolute',
                  opacity: 0,
                  pointerEvents: 'none',
                }}
              />
              <span className={styles.checkmark}>
                {tempSelection.includes(creator) && <Checkmark />}
              </span>
            </div>
          ))}
        </div>

        <div className={styles.curatorDialogFooter}>
          <Button onClick={handleApply} disabled={!selectionHasChanged}>
            {t(dictionary.applyFilters)}
          </Button>
        </div>
      </div>
    </DialogDrawer>
  );
};

export default CuratorsSelectionDialog;
