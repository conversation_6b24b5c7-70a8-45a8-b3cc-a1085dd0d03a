import { NextRequest, NextResponse } from 'next/server';
import { ExternalProfileSearchService } from '@/services/external/profile/recommendations/profile-search';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';

// To handle a GET request to /api
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const keyword = request.nextUrl.searchParams.get('keyword') || '';

    // Fetching user profile
    const response = await ExternalProfileSearchService(keyword);
    // Return the response
    return NextResponse.json({
      profiles: response?.data.items,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error as AxiosError);
  }
}
