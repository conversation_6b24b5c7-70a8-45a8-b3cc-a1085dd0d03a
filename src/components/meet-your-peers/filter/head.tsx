import { FC } from 'react';
import Typography from '@/ui/typography';
import { Reset } from '@/ui/icons';

// Styles
import styles from './style.module.scss';
import dictionary from '@/dictionaries';

interface IHead {
  resetEnabled?: boolean;
}

const Head: FC<IHead> = (props) => {
  //Implement logic similar to Head component in discover filters

  // Deconstruct props
  const { resetEnabled = false } = props;

  /**
   * Method to reset all filters
   */
  const onReset = () => {
    throw new Error('Function not implemented.');
  };

  // Return JSX
  return (
    <div className={styles.head}>
      <div className={styles.bottom}>
        <Typography as="h2" className="mb-0" dictionary={dictionary.Filter} />
        {resetEnabled && (
          <button type="button" className={styles.resetBtn} onClick={onReset}>
            <i>
              <Reset />
            </i>
            <Typography as="span" dictionary={dictionary.resetAll}>
              Reset all
            </Typography>
          </button>
        )}
      </div>
    </div>
  );
};

export default Head;
