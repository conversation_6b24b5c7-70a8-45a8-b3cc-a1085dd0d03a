import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraLeaveCommunityService } from '@/services/totara/my-network/leave-community';

/**
 * Route to leave community
 */
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const id = request.nextUrl.searchParams.get('id') || '';
    // Fetching leave community
    const response = await TotaraLeaveCommunityService(Number(id));
    // Return the response
    return NextResponse.json({
      data: response.data,
    });
  } catch (error) {
    // Rturn Error response
    return ErrorResponse(error);
  }
}
