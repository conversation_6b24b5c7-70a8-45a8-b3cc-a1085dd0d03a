import { IMultidaySession } from '@/types/domain/learning/course';
import Typography from '@/ui/typography';
import { FC } from 'react';
import styles from './style.module.scss';
import { useLocale } from 'next-intl';
import { format } from '@/utils/date';
import { CalendarRounded, Clock, Pin } from '@/ui/icons';
import moment from 'moment';
import { formatLocalTime } from '@/utils/time';
import Button from '@/ui/form/button';
import dictionary from '@/dictionaries';

interface ISessionProps {
  session: IMultidaySession;
}

const Session: FC<ISessionProps> = ({ session }) => {
  const locale = useLocale();

  const formattedDate =
    session.startdatetime &&
    moment(session.startdatetime, 'YYYY-MM-DD').isValid()
      ? format(
          session.startdatetime as string,
          locale,
          'YYYY-MM-DD',
          'dddd DD MMM YYYY',
        )
      : 'DD MMM YYYY';

  const localStartDate = formatLocalTime(session.startdatetime);
  const localEndDate = formatLocalTime(session.enddatetime);

  return (
    <div className={styles.item}>
      <Typography as="h5" className={styles.slotName}>
        {session.session}
      </Typography>
      <div className={styles.row}>
        <CalendarRounded />
        <Typography as="span">{formattedDate}</Typography>
      </div>
      <div className={styles.row}>
        <Clock />
        <Typography as="span">
          {localStartDate
            .locale(locale === 'ar' ? 'ar' : 'en')
            .format('h:mm a')}{' '}
          -{' '}
          {localEndDate.locale(locale === 'ar' ? 'ar' : 'en').format('h:mm a')}
        </Typography>
      </div>
      {session.location &&
        !Array.isArray(session.location) &&
        session.location.room && (
          <div className={styles.row}>
            <Pin />
            <Typography as="span">{session.location.room}</Typography>
          </div>
        )}
      {session.location &&
        !Array.isArray(session.location) &&
        session.location.address && (
          <Button
            href={session.location.address}
            fullLink
            dictionary={dictionary.getDirections}
            target="_blank"
            size="md"
          />
        )}
    </div>
  );
};

export default Session;
