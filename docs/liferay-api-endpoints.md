# Liferay API Endpoints Reference
## Complete Endpoint Documentation for UAE Government Platform

**Base URL:** `https://admin.stg.workspace.dge.gov.ae/o`  
**Authentication:** Bear<PERSON> Required (except for login endpoints)  
**Last Updated:** January 30, 2025

---

## 🔐 **Authentication Endpoints**

### **User Authentication**
| Endpoint | Method | Description | Authentication |
|----------|--------|-------------|----------------|
| `/adsg-authentication/v1.0/authenticate` | POST | UAE Pass & Email authentication | None |
| `/adsg-authentication/v1.0/logout` | DELETE | User logout | Bearer <PERSON>ken |
| `/oauth2/token` | POST | OAuth2 token endpoint | Basic Auth |

**Request Example - UAE Pass Authentication:**
```json
POST /adsg-authentication/v1.0/authenticate
{
  "accessCode": "authorization_code_from_uae_pass",
  "redirectURL": "https://your-app.com/auth/return"
}
```

**Request Example - Email Authentication:**
```json
POST /adsg-authentication/v1.0/authenticate
Headers: {
  "Authorization": "Basic <EMAIL>:Liferay@DGE"
}
{
  "email": "<EMAIL>",
  "password": "user_password"
}
```

---

## 👤 **User Management Endpoints**

### **User Profile Operations**
| Endpoint | Method | Description | Parameters |
|----------|--------|-------------|------------|
| `/adsg-talent-team/v1.0/profile-progress/{id}` | GET | Get user progress tracking | `id` - User ID |
| `/adsg-profiles-search-results/v1.0/search?userId={id}` | GET | Fetch user profile | `userId` - User ID |
| `/adsg-profiles-search-results/v1.0/updateUser?userId={id}` | PATCH | Update user profile | `userId` - User ID |
| `/adsg-profiles-search-results/v1.0/updateUser?userId={id}` | PUT | Update user profile | `userId` - User ID |

### **Profile Search & Discovery**
| Endpoint | Method | Description | Parameters |
|----------|--------|-------------|------------|
| `/adsg-profiles-search-results/v1.0/profile-search?keyword={keyword}` | GET | Search user profiles | `keyword` - Search term |

**Request Example - Update User Profile:**
```json
PATCH /adsg-profiles-search-results/v1.0/updateUser?userId=12345
{
  "firstName": "Ahmed",
  "lastName": "Al-Mansouri",
  "jobTitle": "Software Engineer",
  "department": "IT Department"
}
```

---

## 🔢 **One-Time Password (OTP) Endpoints**

### **OTP Operations**
| Endpoint | Method | Description | Parameters |
|----------|--------|-------------|------------|
| `/adsg-smpp-verification/v1.0/generate-code` | POST | Generate OTP code | Mobile/Email details |
| `/adsg-smpp-verification/v1.0/verify-code` | POST | Verify OTP code | Code and verification data |

**Request Example - Generate OTP:**
```json
POST /adsg-smpp-verification/v1.0/generate-code
{
  "userId": "12345",
  "email": "<EMAIL>",
  "mobileNumber": "+971501234567",
  "type": 1
}
```

**Request Example - Verify OTP:**
```json
POST /adsg-smpp-verification/v1.0/verify-code
{
  "userId": "12345",
  "code": "123456",
  "type": 1
}
```

---

## 🏆 **Talent & Recommendations Endpoints**

### **Recommendation System**
| Endpoint | Method | Description | Parameters |
|----------|--------|-------------|------------|
| `/adsg-talent-recommendations/v1.0/recommendations/list` | GET | Get user recommendations | User context |
| `/adsg-talent-recommendations/v1.0/add/recommendation` | POST | Request recommendation | Recommendation details |
| `/adsg-talent-recommendations/v1.0/update/recommendation/{id}` | PUT | Update recommendation | `id` - Recommendation ID |

### **Talent Profile Notifications**
| Endpoint | Method | Description | Parameters |
|----------|--------|-------------|------------|
| `/adsg-talent-profile/v1.0/talent/recommendation-request` | POST | Notify recommendation request | Request details |
| `/adsg-talent-profile/v1.0/talent/recommendation/{id}` | POST | Notify recommendation update | `id` - Recommendation ID |

### **Talent Recommendation Creation**
| Endpoint | Method | Description | Parameters |
|----------|--------|-------------|------------|
| `/c/recommendatalents` | POST | Create talent recommendation | Recommendation data |

**Request Example - Add Recommendation:**
```json
POST /adsg-talent-recommendations/v1.0/add/recommendation
{
  "recommendeeId": "12345",
  "recommenderId": "67890",
  "skillArea": "Software Development",
  "message": "Excellent problem-solving skills"
}
```

---

## 💼 **Work Experience & Skills Endpoints**

### **Work Experience Management**
| Endpoint | Method | Description | Parameters |
|----------|--------|-------------|------------|
| `/c/workexperiences` | POST | Add work experience | Experience details |
| `/c/workexperiences/{id}` | PUT | Edit work experience | `id` - Experience ID |
| `/c/workexperiences/{id}` | DELETE | Delete work experience | `id` - Experience ID |

### **Skills & Endorsements**
| Endpoint | Method | Description | Parameters |
|----------|--------|-------------|------------|
| `/c/skillsendorsementses?filter=skillName eq '{skillName}' and talentProfileId eq '{talentProfileId}'` | GET | Get skill endorsements | `skillName`, `talentProfileId` |
| `/c/skillsendorsementses/{id}` | PUT | Update skill endorsement | `id` - Endorsement ID |

### **Profile Data**
| Endpoint | Method | Description | Parameters |
|----------|--------|-------------|------------|
| `/c/userpersonaldetailses/{id}` | GET | Get detailed user profile | `id` - User ID |
| `/c/countries?pageSize=-1` | GET | Get countries list | None |

**Request Example - Add Work Experience:**
```json
POST /c/workexperiences
{
  "userId": "12345",
  "companyName": "Emirates NBD",
  "position": "Senior Developer",
  "startDate": "2020-01-01",
  "endDate": "2023-12-31",
  "description": "Led development team for mobile banking applications"
}
```

---

## 📄 **Document Management Endpoints**

### **OBS Document Operations**
| Endpoint | Method | Description | Parameters |
|----------|--------|-------------|------------|
| `/adsg-document-upload/v1.0/obs-document-url/{id}` | GET | Get document URL | `id` - Document ID |
| `/adsg-document-upload/v1.0/obs-document/{folder}` | POST | Upload document | `folder` - Target folder |

**Request Example - Upload Document:**
```json
POST /adsg-document-upload/v1.0/obs-document/profiles
Content-Type: multipart/form-data

{
  "file": [binary_file_data],
  "fileName": "resume.pdf",
  "userId": "12345"
}
```

---

## 📚 **Course Management Endpoints**

### **Course Referral System**
| Endpoint | Method | Description | Parameters |
|----------|--------|-------------|------------|
| `/adsg-courses/v1.0/courses/referred/received?userId={id}&page=1&pageSize=100` | GET | Get received course referrals | `userId`, `page`, `pageSize` |
| `/adsg-courses/v1.0/courses/referred/sent?userId={id}&page=1&pageSize=100` | GET | Get sent course referrals | `userId`, `page`, `pageSize` |
| `/adsg-courses/v1.0/courses/referred/course` | POST | Refer course to user | Course and user details |

**Request Example - Refer Course:**
```json
POST /adsg-courses/v1.0/courses/referred/course
{
  "courseId": "course_123",
  "referredToUserId": "12345",
  "referredByUserId": "67890",
  "message": "This course will help you with React development"
}
```

---

## 📋 **System Data Endpoints**

### **Picklist & Configuration**
| Endpoint | Method | Description | Parameters |
|----------|--------|-------------|------------|
| `/headless-admin-list-type/v1.0/list-type-definitions?filter=name eq '{id}'` | GET | Get picklist data | `id` - Picklist name |

### **Geographic Data**
| Endpoint | Method | Description | Parameters |
|----------|--------|-------------|------------|
| `/c/countries?pageSize=-1` | GET | Get all countries | None |

**Request Example - Get Picklist:**
```
GET /headless-admin-list-type/v1.0/list-type-definitions?filter=name eq 'JobTitles'
```

---

## ❓ **Support Center Endpoints**

### **FAQ System**
| Endpoint | Method | Description | Parameters |
|----------|--------|-------------|------------|
| `/adsg-faq-headless/v1.0/faq` | GET | Get FAQ content | Query parameters |

---

## 🔧 **Request Headers**

### **Required Headers**
```http
Authorization: Bearer {your_access_token}
Content-Type: application/json
Accept: application/json
```

### **Optional Headers**
```http
Accept-Language: en-US,en;q=0.9,ar;q=0.8
User-Agent: YourApp/1.0
```

---

## 📊 **Response Formats**

### **Success Response**
```json
{
  "status": "success",
  "data": {
    // Response data
  },
  "message": "Operation completed successfully"
}
```

### **Error Response**
```json
{
  "status": "error",
  "error": {
    "code": "INVALID_REQUEST",
    "message": "The request parameters are invalid",
    "details": "User ID is required"
  }
}
```

---

## 🚀 **Rate Limiting**

- **Rate Limit:** 1000 requests per hour per user
- **Burst Limit:** 100 requests per minute
- **Headers:** `X-RateLimit-Remaining`, `X-RateLimit-Reset`

---

## 🔍 **Query Parameters**

### **Common Parameters**
- `userId` - User identifier
- `page` - Page number for pagination (default: 1)
- `pageSize` - Items per page (default: 20, max: 100)
- `filter` - OData-style filtering
- `sort` - Sorting criteria

### **Filter Examples**
```
filter=name eq 'John'
filter=createdDate gt '2023-01-01'
filter=status eq 'active' and department eq 'IT'
```

---

## 📝 **Notes**

1. **All endpoints require HTTPS**
2. **Bearer token must be included** in Authorization header (except login)
3. **Rate limiting applies** to all endpoints
4. **Pagination is supported** where applicable
5. **OData filtering** is available on list endpoints
6. **Multipart form data** required for file uploads
7. **UTC timestamps** used throughout the API

This reference provides complete coverage of all Liferay API endpoints available in the UAE government platform.
