import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchPostCommentsService } from '@/services/totara/my-network';

/**
 * Route to get the Post comments
 */
export async function GET(request: NextRequest) {
  try {
    const postId = request.nextUrl.searchParams.get('postId');

    // Fetching comments for post
    const response = await TotaraFetchPostCommentsService(postId || '');

    // Return the response
    return NextResponse.json({
      success: response.data.success,
      comments: response.data.comments,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
