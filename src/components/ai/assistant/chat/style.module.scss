@use 'mixins' as *;

.chatBackground {
  display: block;
  background-color: rgba(0, 0, 0, 0.2);
  bottom: 0;
  left: 0;
  height: 100%;
  position: fixed;
  width: 100%;
  z-index: 99;

  &:not(.isActiveClass) {
    animation-fill-mode: forwards;
    animation-name: chat-out;
    animation-duration: 0.4s;
    animation-timing-function: cubic-bezier(0.77, 0.23, 0.23, 0.81);
  }
}

.chatWrapper {
  background-color: var(--white);
  border-radius: size(20);
  bottom: size(80);
  display: flex;
  height: calc(100% - size(160));
  overflow: hidden;
  position: fixed;
  right: size(80);
  top: size(80);
  transition: 0.5s all cubic-bezier(0.77, 0.23, 0.23, 0.81);
  width: size(430);
  z-index: 99;

  @include for-all-phone() {
    bottom: 0;
    border-radius: 0;
    height: 100vh;
    right: 0;
    top: 0;
    width: 100vw;
  }

  @include rtl {
    left: size(80);
    right: auto;
  }

  &:not(.isActiveClass) {
    animation-fill-mode: forwards;
    animation-name: chat-out;
    animation-duration: 0.4s;
    animation-timing-function: cubic-bezier(0.77, 0.23, 0.23, 0.81);
  }

  div[class*='searchWrapper'],
  button[class*='newCourseCta'],
  div[class*='sessionWrapper'] {
    opacity: 0;
    animation: sessions-fade-in;
    animation-fill-mode: forwards;
    animation-duration: 0.4s;
    animation-delay: 0.2s;

    @include for-all-phone() {
      opacity: 1 !important;
    }
  }

  button[class*='newCourseCta'] {
    animation-delay: 0.4s;
  }

  div[class*='sessionWrapper'] {
    animation-delay: 0.5s;
  }

  div[class*='suggestionsWrapper'] {
    div[class*='swiper-slide'] {
      &:first-child {
        margin-left: size(0);
        transition: 0.4s margin-left ease-in-out;

        @include rtl {
          margin-left: size(0);
          margin-right: size(0);
          transition: 0.4s margin-right ease-in-out;
        }
      }
    }
  }

  &:not(.isExpanded) {
    div[class*='searchWrapper'],
    button[class*='newCourseCta'],
    div[class*='sessionWrapper'] {
      opacity: 0;
      animation: sessions-fade-out;
      animation-fill-mode: forwards;
      animation-duration: 0.1s;
      animation-timing-function: cubic-bezier(0.77, 0.23, 0.23, 0.81);
    }

    div[class*='suggestionsWrapper'] {
      div[class*='swiper-slide'] {
        &:first-child {
          margin-left: size(24);
          transition: 0.4s margin-left ease-in-out;

          @include rtl {
            margin-left: size(0);
            margin-right: size(24);
            transition: 0.4s margin-right ease-in-out;
          }
        }
      }
    }
  }
}

.actionsWrapper {
  display: flex;
  gap: size(26);

  button {
    background-color: transparent;
    border: 0;
    cursor: pointer;

    svg {
      fill: var(--grey-900);
      height: size(20);
      width: size(20);
    }
  }
}

.chatBodyWrapper {
  display: flex;
  flex-direction: column;
  width: 100%;

  &::before,
  &::after {
    content: '';
    height: 100%;
    position: absolute;
    pointer-events: none;
    width: 100%;
    z-index: -1;
  }

  &::before {
    background: linear-gradient(
      48deg,
      rgba(255, 255, 255, 0) 77%,
      rgba(235, 240, 246, 1) 88%,
      rgba(198, 213, 229, 1) 100%
    );

    @include rtl {
      transform: scale(1, -1);
    }
  }

  &:after {
    background: linear-gradient(
      210deg,
      rgba(255, 255, 255, 0) 72%,
      rgba(235, 240, 246, 1) 85%,
      rgba(198, 213, 229, 1) 100%
    );

    @include rtl {
      transform: scale(1, -1);
    }
  }
}

@include for-dark-theme {
  .chatBodyWrapper {
    &::before {
      background: linear-gradient(
        48deg,
        rgba(0, 0, 0, 0) 70%,
        #455464bd 83%,
        #495969 100%
      );
    }

    &:after {
      background: linear-gradient(
        210deg,
        rgba(0, 0, 0, 0) 75%,
        #455464a9 93%,
        #495969 100%
      );
    }
  }
}

.isExpanded {
  bottom: 0;
  border-radius: 0;
  height: 100%;
  right: 0;
  top: 0;
  width: 100%;
  transition: 0.5s all cubic-bezier(0.77, 0.23, 0.23, 0.81);

  @include rtl {
    left: 0;
  }

  div[class*='sessionWrapper'] {
    opacity: 0;
    animation: sessions-fade-in;
    animation-fill-mode: forwards;
    animation-duration: 0.4s;
    animation-delay: 0.5s;
    animation-timing-function: cubic-bezier(0.77, 0.23, 0.23, 0.81);
  }
}

.chatActions {
  display: flex;
  justify-content: space-between;
  padding: size(32) size(24);
  position: relative;
  top: 0;
}

.titleWrapper {
  font-size: size(26);
  opacity: 1;
  transition: 0.4s all ease-in-out;
  transition-delay: 0.2s;

  &.titleHidden {
    opacity: 0;
    transition: 0.4s all ease-in-out;
  }
}

.chatBody {
  height: calc(100vh - size(525));
  flex: 1;
  transition: 0.5s height cubic-bezier(0.77, 0.23, 0.23, 0.81);
}

.activeArea {
  height: calc(100vh - size(430));
  transition: 0.4s height cubic-bezier(0.77, 0.23, 0.23, 0.81);
}

.bottomInput {
  bottom: 0;
}

.historyWrapper {
  background-color: var(--white);
  border-right: 1px solid var(--grey-400);
  overflow: hidden;

  @include rtl {
    border-left: 1px solid var(--grey-400);
    border-right: 0;
  }
}

.isActiveClass {
  animation-fill-mode: forwards;
  animation-name: chat-in;
  animation-duration: 0.4s;
  animation-timing-function: cubic-bezier(0.77, 0.23, 0.23, 0.81);
}

@keyframes chat-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes chat-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes sessions-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes sessions-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
