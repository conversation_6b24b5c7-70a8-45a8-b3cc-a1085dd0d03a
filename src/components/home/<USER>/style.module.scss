@use 'mixins' as *;

.list {
  @include forMarginPadding(padding, size(0), size(0), size(24), size(0));

  ul {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    @include forMarginPadding(padding, size(0), size(0), size(0), size(0));

    li {
      @include forMarginPadding(padding, size(0), size(24), size(0), size(24));
      position: relative;
      list-style: none;

      &:before {
        position: absolute;
        @include rightToLeft(0);
        top: 0;
        width: 1px;
        height: 100%;
        background: var(--grey-400);
        content: '';
      }

      &:first-child {
        @include forMarginPadding(padding, size(0), size(24), size(0), size(0));
      }

      &:last-child {
        @include forMarginPadding(padding, size(0), size(0), size(0), size(24));

        &:before {
          display: none;
        }
      }
    }
  }
}

@include for-all-phone() {
  .list {
    ul {
      grid-template-columns: repeat(3, 1fr);
      grid-gap: 0;
      @include forMarginPadding(padding, size(0), size(0), size(0), size(0));

      li {
        @include forMarginPadding(padding, size(0), size(5), size(0), size(5));

        &:nth-child(3) {
          &:before {
            display: none;
          }
        }

        &:first-child {
          @include forMarginPadding(
            padding,
            size(0),
            size(5),
            size(0),
            size(0)
          );
        }

        &:last-child {
          @include forMarginPadding(
            padding,
            size(0),
            size(0),
            size(0),
            size(5)
          );

          &:before {
            display: none;
          }
        }

        &:nth-child(4),
        &:nth-child(5) {
          display: none;
        }
      }
    }
  }
}

.coachingWrapper {
  min-height: size(230);

  @include for-all-phone() {
    min-height: size(190);
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }
}
