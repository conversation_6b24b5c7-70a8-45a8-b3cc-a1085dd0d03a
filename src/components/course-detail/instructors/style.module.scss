@use 'mixins' as *;

.prerequisites {
  @include forMarginPadding(padding, size(30), size(0), size(30), size(0));
  border-bottom: solid 1px var(--grey-400);
}

.list {
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
  display: grid;
  align-items: center;
  flex-wrap: nowrap;
  grid-template-columns: repeat(3, 1fr);

  li {
    list-style: none;
    display: flex;
    align-items: center;
  }
}

.card {
  display: flex;
  flex-wrap: wrap;
}

.photo {
  width: size(56);
  height: size(56);
  @include borderRadius(50%);
  overflow: hidden;
  @include forMarginPadding(margin, size(0), size(15), size(0), size(0));

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.data {
  @include forMarginPadding(padding, size(10), size(0), size(0), size(0));
}

.title {
  font-size: size(18);
  font-weight: 700;
  display: block;
  @include forMarginPadding(margin, size(0), size(0), size(2), size(0));
}

.designation {
  font-size: size(14);
  color: var(--grey-700);
  display: block;
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
}

@include for-all-phone() {
  .list {
    display: block;
    li {
      + li {
        @include forMarginPadding(margin, size(16), size(0), size(0), size(0));
      }
    }
  }
}
