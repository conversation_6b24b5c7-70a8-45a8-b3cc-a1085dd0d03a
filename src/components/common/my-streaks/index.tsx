'use client';

import Typography from '@/ui/typography';
import Progress from '@/ui/progress/circular/';
import useLearningStreaksStore from '@/stores/learning/streaks';
import { useCallback, useEffect, useMemo } from 'react';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from './loading';
import Media from '@/ui/media';

// Styles
import styles from './style.module.scss';

const MyStreaks = () => {
  // Getting fetching state
  const isFetching = useLearningStreaksStore((x) => x.isFetching);

  // Getting fetch action
  const onFetch = useLearningStreaksStore((x) => x.fetch);

  // Geting the streaks data
  const goal = useLearningStreaksStore((x) => x.streaks.goal);
  const day = useLearningStreaksStore((x) => x.streaks.day);
  const week = useLearningStreaksStore((x) => x.streaks.week);

  // Getting sorted streaks
  const streaks = useMemo(() => {
    // Creating a copy of the streaks
    const _streaks = [...(week || [])];
    // Changing the order
    _streaks.pop();
    //_streaks.push(week[week.length - 1])
    // return the sorted streaks
    return [week[week.length - 1], ..._streaks];
  }, [week]);

  /**
   * Method to get the active classname
   */
  const getActiveClass = useCallback((i: number) => {
    // Get current day of the week
    const current = new Date().getDay();
    // Iteration day is current day
    if (current === i) {
      // Return active class name
      return styles.active;
    }
    // Return empty string
    return '';
  }, []);

  // Effect to fetch the announcements
  useEffect(() => {
    // Fetch the announcements
    onFetch();
  }, [onFetch]);

  // Return JSX
  return (
    <WithLoader loader={<Loading />} loading={isFetching}>
      <div className={styles.myStreaksBox}>
        <div className={styles.body}>
          <Typography as="h4">My Streaks</Typography>
          <Media mobile={false} tablet={false}>
            <div className={styles.streakProgress}>
              <Progress
                percentage={day?.progress}
                circleWidth={175}
                strokWidth="20"
                radius={75}
                stepColorStart="#67D9B6"
                stepColorEnd="#01BF86"
                flameImage
                id="myStreak"
                centerText={`Daily goal ${goal} mins`}
              />
            </div>
          </Media>
        </div>
        <div className={styles.footer}>
          <nav>
            <ul>
              {streaks?.map((x, i) => (
                <li className={getActiveClass(i)} key={'s-w-' + i}>
                  <Progress
                    id={'s-w-' + i}
                    percentage={x?.progress >= 100 ? 100 : x?.progress}
                    circleWidth={70}
                    strokWidth="6"
                    radius={30}
                    stepColorStart="#67D9B6"
                    stepColorEnd="#01BF86"
                    centerText={x?.day?.[0] || '-'}
                    className={styles.weekDay}
                  />
                  <div className={styles.dot}></div>
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </div>
    </WithLoader>
  );
};

export default MyStreaks;
