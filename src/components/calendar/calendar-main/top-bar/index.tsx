import React from 'react';
import { useTranslations, useLocale } from 'next-intl';
import ViewPeriod from '../view-period-selector';
import dictionary from '@/dictionaries';
import styles from './style.module.scss';
import { ArrowDownFrame, ListView, CalendarView } from '@/ui/icons';

type TimePeriod = 'daily' | 'weekly' | 'monthly';

interface TopBarProps {
  currentDate: Date;
  selectedTimePeriod: TimePeriod;
  isCalendarView: boolean;
  onPrevClick: () => void;
  onNextClick: () => void;
  onTodayClick: () => void;
  onTimePeriodChange: (newTimePeriod: TimePeriod) => void;
  onViewTypeChange: (isCalendaView: boolean) => void;
}

const TopBar: React.FC<TopBarProps> = ({
  currentDate,
  selectedTimePeriod,
  isCalendarView,
  onPrevClick,
  onNextClick,
  onTodayClick,
  onTimePeriodChange,
  onViewTypeChange,
}) => {
  const locale = useLocale();
  const t = useTranslations();

  const formatTimePeriodDate = (date: Date, period: TimePeriod): string => {
    const options: Intl.DateTimeFormatOptions = {};
    let formattedDate = '';

    switch (period) {
      case 'daily': {
        // Example: Monday, May 5, 2025
        options.weekday = 'long';
        options.year = 'numeric';
        options.month = 'long';
        options.day = 'numeric';
        formattedDate = new Intl.DateTimeFormat(locale, options).format(date);
        break;
      }
      case 'weekly': {
        // Example: Monday, May 5 - 9, 2025
        const startOfWeek = new Date(date);
        startOfWeek.setDate(
          date.getDate() - date.getDay() + (date.getDay() === 0 ? -6 : 1),
        );
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);

        const startMonthOptions: Intl.DateTimeFormatOptions = {
          month: 'long',
          day: 'numeric',
        };
        const endMonthOptions: Intl.DateTimeFormatOptions = { day: 'numeric' }; // Only day for end if same month
        const yearOptions: Intl.DateTimeFormatOptions = { year: 'numeric' };

        const startDay = new Intl.DateTimeFormat(
          locale,
          startMonthOptions,
        ).format(startOfWeek);
        const endDay = new Intl.DateTimeFormat(locale, endMonthOptions).format(
          endOfWeek,
        );
        const year = new Intl.DateTimeFormat(locale, yearOptions).format(
          startOfWeek,
        );

        if (startOfWeek.getMonth() !== endOfWeek.getMonth()) {
          // If week spans two months, show full month/day for both
          const endDayFull = new Intl.DateTimeFormat(locale, {
            month: 'long',
            day: 'numeric',
          }).format(endOfWeek);
          formattedDate = `${startDay} - ${endDayFull}, ${year}`;
        } else {
          formattedDate = `${startDay} - ${endDay}, ${year}`;
        }
        break;
      }
      case 'monthly': {
        // Example: May, 2025
        options.year = 'numeric';
        options.month = 'long';
        formattedDate = new Intl.DateTimeFormat(locale, options).format(date);
        break;
      }
      default:
        formattedDate = '';
    }
    return formattedDate;
  };

  const formattedDateString = formatTimePeriodDate(
    currentDate,
    selectedTimePeriod,
  );

  return (
    <div className={styles.topBarContainer}>
      <div className={styles.dateNavigation}>
        <button className={styles.todayButton} onClick={onTodayClick}>
          {t(dictionary.today)}
        </button>
        <span className={styles.dateDisplay}>{formattedDateString}</span>
        <div className={styles.navButtons}>
          <button
            className={`${styles.navButton} ${styles.navButtonPrev} ${locale == 'ar' ? styles.rtl : ''}`}
            onClick={onPrevClick}
          >
            <ArrowDownFrame />
          </button>

          <button
            className={`${styles.navButton} ${styles.navButtonNext} ${locale == 'ar' ? styles.rtl : ''}`}
            onClick={onNextClick}
          >
            <ArrowDownFrame />
          </button>
        </div>
      </div>

      <div className={styles.controlsSection}>
        <div className={styles.viewSwitcher}>
          <button
            className={`${styles.viewButton} ${styles.viewButtonList} ${!isCalendarView ? styles.viewBtnActive : ''}`}
            onClick={() => onViewTypeChange(isCalendarView)}
          >
            <ListView />
          </button>
          <button
            className={`${styles.viewButton} ${styles.viewButtonCalendar} ${isCalendarView ? styles.viewBtnActive : ''}`}
            onClick={() => onViewTypeChange(isCalendarView)}
          >
            <CalendarView />
          </button>
        </div>
        <ViewPeriod
          selectedTimePeriod={selectedTimePeriod}
          onTimePeriodChange={onTimePeriodChange}
        />
      </div>
    </div>
  );
};

export default TopBar;
