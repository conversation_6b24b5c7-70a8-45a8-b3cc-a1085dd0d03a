<svg width="821" height="1169" viewBox="0 0 821 1169" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.5" clip-path="url(#clip0_354_596)">
<g filter="url(#filter0_f_354_596)">
<path d="M516.259 889.026C591.709 812.709 524.697 658.957 366.583 545.611C208.469 432.266 335.942 629.36 260.492 705.676C185.042 781.993 -64.7604 708.633 93.3535 821.979C251.467 935.325 440.808 965.343 516.259 889.026Z" fill="url(#paint0_linear_354_596)"/>
</g>
<g filter="url(#filter1_f_354_596)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M-69.7207 608.499C-48.4405 540.062 3.14251 478.656 74.8112 478.378C146.598 478.1 197.573 539.44 220.776 607.374C245.059 678.472 247.588 763.327 187.57 808.519C125.587 855.192 39.0712 837.944 -22.186 790.322C-76.8648 747.814 -90.2848 674.634 -69.7207 608.499Z" fill="url(#paint1_linear_354_596)"/>
</g>
<g filter="url(#filter2_f_354_596)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M65.2981 392.073C81.7477 354.356 77.0127 304.935 108.787 278.793C140.538 252.671 187.404 262.861 228.231 258.024C265.22 253.642 300.705 249.426 337.886 251.651C385.219 254.484 449.644 233.501 476.099 272.857C504.267 314.763 446.417 369.294 450.778 419.602C454.336 460.641 499.015 490.111 498.505 531.301C497.951 576.009 476.302 618.528 447.887 653.048C417.178 690.356 379.368 736.8 331.101 734.677C280.174 732.437 260.212 659.844 212.213 642.682C166.701 626.41 110.884 666.489 69.6876 641.21C28.9592 616.217 6.2662 563.576 5.42429 515.791C4.60324 469.19 46.6654 434.796 65.2981 392.073Z" fill="url(#paint2_linear_354_596)"/>
</g>
</g>
<defs>
<filter id="filter0_f_354_596" x="-51.5863" y="417.779" width="692.333" height="606.945" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="46.7443" result="effect1_foregroundBlur_354_596"/>
</filter>
<filter id="filter1_f_354_596" x="-182.738" y="374.501" width="523.479" height="565.379" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="51.9381" result="effect1_foregroundBlur_354_596"/>
</filter>
<filter id="filter2_f_354_596" x="-107.813" y="135.333" width="719.547" height="712.64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="56.6125" result="effect1_foregroundBlur_354_596"/>
</filter>
<linearGradient id="paint0_linear_354_596" x1="204.925" y1="901.96" x2="428.539" y2="590.025" gradientUnits="userSpaceOnUse">
<stop stop-color="#7DA1C4"/>
<stop offset="1" stop-color="#8C958F"/>
</linearGradient>
<linearGradient id="paint1_linear_354_596" x1="189.491" y1="872.809" x2="147.516" y2="460.524" gradientUnits="userSpaceOnUse">
<stop offset="1" stop-color="#7DA1C4"/>
</linearGradient>
<linearGradient id="paint2_linear_354_596" x1="132.848" y1="906.331" x2="724.411" y2="611.178" gradientUnits="userSpaceOnUse">
<stop stop-color="#B4AFA5"/>
<stop offset="1" stop-color="#D5C8BE"/>
</linearGradient>
<clipPath id="clip0_354_596">
<rect width="932.289" height="919.305" fill="white" transform="translate(-340 887.576) rotate(-72.1832)"/>
</clipPath>
</defs>
</svg>
