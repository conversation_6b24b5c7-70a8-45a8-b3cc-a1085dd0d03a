import dictionary from '@/dictionaries';
import Button from '@/ui/form/button';
import Typography from '@/ui/typography';
import { ChangeEvent, FC, useState } from 'react';
import styles from './style.module.scss';
import InputField from '@/ui/form/input-field';
import { Pencil, Phone, User } from '@/ui/icons';
import { useTranslations } from 'next-intl';
import useRecommendTalentStore from '@/stores/recommend-talent';
import useAuthStore from '@/stores/auth';
import { isValidPhoneRuleSimple } from '@/utils/validations/phone';
import { checkEmptyValidation } from '@/utils/validations/helpers';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';

interface IRecommendATalentInformation {
  onBack: () => void;
  onCloseDrawer: () => void;
}

const RecommendATalentInformation: FC<IRecommendATalentInformation> = (
  props,
) => {
  const { onBack, onCloseDrawer } = props;
  const [error, setError] = useState({
    phone: '',
  });
  const t = useTranslations();
  const userID = useAuthStore((x) => x.user?.id) || '';
  const {
    recommendTalentData,
    setRecommendTalentData,
    createRecommendTalent,
    setIsPopupActive,
    setEmptyRecommendTalentData,
    isFetching,
  } = useRecommendTalentStore();

  const handleClickNext = async () => {
    const newErrors = {
      phone: '',
    };

    if (!isValidPhoneRuleSimple(recommendTalentData.phone)) {
      newErrors.phone = t(dictionary.invalidPhoneNumber);
    }

    const hasErrors = Object.values(newErrors).some((error) => error);

    if (hasErrors) {
      setError(newErrors);
    } else {
      setError({ ...error, phone: '' });
      const recommendTalentDataWithUser = {
        ...recommendTalentData,
        recommendedByUserId: userID,
      };

      await createRecommendTalent(recommendTalentDataWithUser);

      setIsPopupActive(true);
      setEmptyRecommendTalentData();
      onCloseDrawer();
    }
  };

  const handleClickBack = () => {
    onBack();
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (name === 'phone') {
      let cleanedValue = '';
      if (value.startsWith('+')) {
        cleanedValue = '+' + value.substring(1).replace(/[^0-9]/g, '');
      } else {
        cleanedValue = value.replace(/[^0-9]/g, '');
      }
      const finalValue = cleanedValue;
      setRecommendTalentData({ ...recommendTalentData, [name]: finalValue });
      e.target.value = finalValue;
    } else {
      setRecommendTalentData({ ...recommendTalentData, [name]: value });
    }
  };

  const isEmpty = checkEmptyValidation({
    phone: recommendTalentData.phone,
    talentName: recommendTalentData.talentName,
  });

  return (
    <>
      <WithLoader
        loader={<Loading />}
        loading={isFetching !== null && isFetching}
      >
        <Typography
          as="h4"
          className="dialog-heading"
          dictionary={dictionary.pleaseProvideTalentInformation}
        />
        <InputField
          label={dictionary.name}
          name="talentName"
          value={recommendTalentData?.talentName || ''}
          placeholder={dictionary.name}
          startIcon={<User />}
          onChange={handleChange}
          required
        />
        <InputField
          label={dictionary.occupation}
          name="occupation"
          value={recommendTalentData?.occupation || ''}
          placeholder={dictionary.occupation}
          startIcon={<Pencil />}
          onChange={handleChange}
        />
        <InputField
          label={dictionary.phoneNumber}
          name="phone"
          className={styles.phoneField}
          value={recommendTalentData?.phone || ''}
          placeholder={dictionary.phoneNumber}
          startIcon={<Phone />}
          onChange={handleChange}
          required
          type="text"
          error={error.phone}
        />
        <InputField
          label="LinkedIn"
          name="linkedin"
          value={recommendTalentData?.linkedin || ''}
          placeholder={'ex: https://www.linkedin.com/'}
          startIcon={<User />}
          onChange={handleChange}
        />
        <div className={styles.buttons}>
          <Button
            type="button"
            color="black"
            outline
            onClick={handleClickBack}
            dictionary={dictionary.previous}
          />
          <Button
            type="button"
            color="black"
            onClick={handleClickNext}
            disabled={isEmpty}
            loading={false}
            dictionary={dictionary.submit}
          />
        </div>
      </WithLoader>
    </>
  );
};
export default RecommendATalentInformation;
