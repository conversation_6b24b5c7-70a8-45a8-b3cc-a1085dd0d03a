import { FC, useState, useCallback } from 'react';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';
import Typography from '@/ui/typography';
import KababMenu from '@/ui/menu/kabab';
import useReferCourseStore from '@/stores/learning/courses/refer-course';

import { Share, ShareProfile } from '@/ui/icons';
import bannerImage from '@public/images/course-banner.png';
import Toast from '@/ui/toast';

// Styles
import styles from './style.module.scss';
import Favorite from '@/components/course/favorite';
import ICourse from '@/types/domain/learning/course';
import dictionary from '@/dictionaries';
import useCourseDetailStore from '@/stores/learning/courses/detail';
import { useLocale } from 'next-intl';
import { generateShareLink } from '@/utils/share-course';

interface IBanner {
  course?: ICourse;
  image?: string;
  isLoading?: boolean | null;
}

const Banner: FC<IBanner> = (props) => {
  const { image, isLoading, course } = props;
  const [displayToast, setDisplayToast] = useState<boolean>(false);
  const setVisible = useReferCourseStore((state) => state.setVisible);
  const setCourseInfo = useReferCourseStore((state) => state.setCourseInfo);
  const courseDetail = useCourseDetailStore((state) => state.detail);
  const locale = useLocale();

  const onReferCourse = useCallback(
    (id?: number, name?: string, image?: string) => {
      setCourseInfo(id, name, image);
      setVisible(true);
    },
    [setVisible, setCourseInfo],
  );

  return (
    <>
      <WithLoader
        loading={isLoading}
        loader={<Loading className={styles.bannerWrapper} />}
      >
        <div className={styles.banner}>
          <div
            className={styles.bg}
            style={{ backgroundImage: `url(${image || bannerImage.src})` }}
          ></div>
          <div className={styles.menuWrap}>
            {course && (
              <Favorite course={course} className={styles.favoritebb} />
            )}
            <KababMenu>
              <nav>
                <ul className={styles.menuList}>
                  <li>
                    <button
                      type="button"
                      onClick={() =>
                        onReferCourse(
                          course?.courseId,
                          course?.course_name,
                          course?.course_image,
                        )
                      }
                    >
                      <i>
                        <ShareProfile />
                      </i>
                      <Typography
                        as="span"
                        dictionary={dictionary.referACourse}
                      />
                    </button>
                  </li>
                  <li>
                    <button
                      type="button"
                      onClick={() => {
                        const textArea = document.createElement('textarea');
                        textArea.value = generateShareLink(
                          courseDetail?.courseId as number,
                          courseDetail?.course_name || '',
                          courseDetail?.course_description || '',
                          courseDetail?.course_image || '',
                          locale,
                        );
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        setDisplayToast(true);
                      }}
                    >
                      <i>
                        <Share />
                      </i>
                      <Typography as="span" dictionary={dictionary.Share} />
                    </button>
                  </li>
                </ul>
              </nav>
            </KababMenu>
          </div>
        </div>
      </WithLoader>
      {displayToast && (
        <Toast
          text="Link copied to clipboard"
          onClose={() => {
            setDisplayToast(false);
          }}
        />
      )}
    </>
  );
};

export default Banner;
