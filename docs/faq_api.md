# FAQ API – Frontend Integration Guide (DGE CRM)

This document describes the actual FAQ endpoints implemented in the backend (govacademy_backend), including URL paths, methods, headers, parameters, request/response schemas, and usage examples. It follows the project’s bilingual (en/ar) and envelope response conventions.

Notes:
- API lives under the legacy v2 base: `/app/v2/crm/`
- All endpoints require JWT authentication (Authorization: Bearer …)
- Bilingual responses depend on the `Accept-Language` header
- Responses use the global “operationResponse” envelope (see below)

---

## Base URL and Auth

- Base path: `/app/v2/crm/`
- Auth: JWT via `Authorization: Bearer <access_token>`
- Required headers:
  - `Authorization: Bearer <JWT>`
  - `Accept-Language: en` or `ar` (default `en` if omitted)

Envelope response format (always):
- Top-level keys:
  - `data`: any
  - `pagination`: null or object (not used by these endpoints)
  - `operationResponse`: `{ success, message, errorCode, errorDescription, validationErrors }`

Example operationResponse on success:
- `operationResponse.success = true`
- `errorCode = null`, `errorDescription = null`, `validationErrors = null`

---

## Data Models (actual backend schema)

- FAQ Category
  - `id`: integer
  - `name_en`: string (required, non-empty)
  - `name_ar`: string (required, non-empty)
  - `description_en`: string (optional)
  - `description_ar`: string (optional)
  - `slug`: string (unique; matches configured slug pattern)
  - `is_published`: boolean
  - `sort_order`: integer (via SortOrderMixin)
  - Additional display fields: `icon` (string), `color` (hex color string)

- FAQ Item
  - `id`: integer
  - `category`: FK to FAQCategory
  - `question_en`: string (required, non-empty)
  - `question_ar`: string (required, non-empty)
  - `answer_en`: string (required, non-empty; HTML allowed, scripts disallowed)
  - `answer_ar`: string (required, non-empty; HTML allowed, scripts disallowed)
  - `is_published`: boolean
  - `is_featured`: boolean
  - `view_count`: integer
  - `sort_order`: integer
  - `tags`: string (comma-separated)

Bilingual behavior in responses:
- Category serializer returns:
  - `id`
  - `name`: localized value based on `Accept-Language`
  - `description`: localized value based on `Accept-Language`
- Item serializer returns:
  - `id`
  - `question`: localized
  - `answer`: localized

Important: serializers do not return bilingual objects; they return a single localized string per field depending on `Accept-Language`.

---

## Endpoints Overview

### Categories

1) List categories
- Method: `GET`
- URL: `/app/v2/crm/faq/categories/`
- Auth: required (authenticated user)
- Query params:
  - `search` (optional, string) – filters by bilingual name match
- Response (200):
  - `data`: array of `{ id, name, description }` localized
  - `operationResponse`: see envelope
- Errors:
  - `401` Unauthorized (missing/invalid token)
  - `500` Internal Server Error

2) Category details
- Method: `GET`
- URL: `/app/v2/crm/faq/categories/{id}/`
- Auth: required
- Response (200):
  - `data`: `{ id, name, description }` localized
- Errors:
  - `404` Not Found if no category
  - `401/500` as above

3) List categories with counts
- Method: `GET`
- URL: `/app/v2/crm/faq/categories/with-counts/`
- Auth: required
- Query params:
  - `search` (optional, string)
- Response (200):
  - `data`: array of `{ id, name, description }` localized
  - Note: The backend currently annotates counts internally but the response serializer only exposes id/name/description. No count field is included in the payload at this time.

4) Admin-only (CMS) write operations (not needed by frontend UI)
- Create category: `POST` `/app/v2/crm/faq/categories/`
- Update category: `PUT/PATCH` `/app/v2/crm/faq/categories/{id}/`
- Delete category: `DELETE` `/app/v2/crm/faq/categories/{id}/`
- Permissions:
  - Create/Update: staff/content managers
  - Delete: superuser
- Frontend user interface should not call these endpoints.

### Items

1) List items
- Method: `GET`
- URL: `/app/v2/crm/faq/items/`
- Auth: required
- Query params:
  - `category_id` (optional, integer) – filter items by category
  - `search` (optional, string) – bilingual search across question/answer
- Response (200):
  - `data`: array of `{ id, question, answer }` localized
- Errors:
  - `401/500` as above

2) Item details
- Method: `GET`
- URL: `/app/v2/crm/faq/items/{id}/`
- Auth: required
- Response (200):
  - `data`: `{ id, question, answer }` localized
- Errors:
  - `404` Not Found for missing item
  - `401/500` as above

3) Admin-only (CMS) write operations (not needed by frontend UI)
- Create item: `POST` `/app/v2/crm/faq/items/`
- Update item: `PUT/PATCH` `/app/v2/crm/faq/items/{id}/`
- Delete item: `DELETE` `/app/v2/crm/faq/items/{id}/`
- Bulk create: `POST` `/app/v2/crm/faq/items/bulk-create`
- Bulk update: `PATCH` `/app/v2/crm/faq/items/bulk-update`
- Bulk delete: `DELETE` `/app/v2/crm/faq/items/bulk-delete`
- Permissions:
  - Create/Update/Bulk create/update: staff/content managers
  - Delete/bulk delete: superuser
- Frontend user interface should not call these endpoints.

---

## Request Specifications

Common headers for all endpoints:
- `Authorization: Bearer <JWT>`
- `Accept-Language: en` or `ar`

Body:
- Read endpoints (`GET`) do not need a request body.
- Write endpoints exist but are CMS-only and their payloads are not required by the frontend UI; skip.

---

## Response Specifications

Common HTTP codes:
- `200 OK` – successful read
- `201 Created` – write (admin-only)
- `204 No Content` – delete (admin-only)
- `400 Bad Request` – validation errors (write/admin-only)
- `401 Unauthorized` – missing or invalid token
- `404 Not Found` – missing resource
- `500 Internal Server Error` – unhandled error

Envelope (renderer output) fields:
- `data`: endpoint-specific payload
- `pagination`: null (no pagination used here)
- `operationResponse`:
  - `success`: boolean
  - `message`: null (by current renderer)
  - `errorCode`: string or null
  - `errorDescription`: string or null
  - `validationErrors`: array or null (objects with `{ field, message }`)

Schema for `data` per endpoint:
- Categories (list/detail/with-counts):
  - `id`: integer
  - `name`: string (localized)
  - `description`: string (localized)
- Items (list/detail):
  - `id`: integer
  - `question`: string (localized)
  - `answer`: string (localized; may contain safe HTML)

---

## Sample Responses

1) GET `/app/v2/crm/faq/categories/` (Accept-Language: en)
```
{
  "data": [
    { "id": 1, "name": "General", "description": "Common questions" },
    { "id": 2, "name": "Registration", "description": "Account setup and access" }
  ],
  "pagination": null,
  "operationResponse": {
    "success": true,
    "message": null,
    "errorCode": null,
    "errorDescription": null,
    "validationErrors": null
  }
}
```

2) GET `/app/v2/crm/faq/items/?category_id=2` (Accept-Language: ar)
```
{
  "data": [
    {
      "id": 101,
      "question": "كيف أسجل حسابًا جديدًا؟",
      "answer": "<p>انتقل إلى صفحة التسجيل واتبع الخطوات...</p>"
    },
    {
      "id": 102,
      "question": "كيف أستعيد كلمة المرور؟",
      "answer": "<p>استخدم رابط نسيت كلمة المرور على شاشة تسجيل الدخول...</p>"
    }
  ],
  "pagination": null,
  "operationResponse": {
    "success": true,
    "message": null,
    "errorCode": null,
    "errorDescription": null,
    "validationErrors": null
  }
}
```

3) GET `/app/v2/crm/faq/items/12345/` (missing token)
```
{
  "data": null,
  "pagination": null,
  "operationResponse": {
    "success": false,
    "message": null,
    "errorCode": "401",
    "errorDescription": "Authentication credentials were not provided.",
    "validationErrors": null
  }
}
```

---

## Usage Examples (Frontend Flows)

Recommended UI flow:
1) Fetch all categories (localized)
   - `GET /app/v2/crm/faq/categories/` (Accept-Language set from user preference)
2) For a selected category, fetch its questions (localized) and answers
   - `GET /app/v2/crm/faq/items/?category_id={categoryId}`
   - The list already includes both question and answer. You can render the question and expand to display answer without another roundtrip.
3) For deep-linking or a full item view, fetch an item by id:
   - `GET /app/v2/crm/faq/items/{id}/`

Error handling:
- If `401`, redirect to login/refresh token flow.
- For `500`, show generic error toast and retry option.

---

## Filtering and Query Parameters

- Categories: `GET /faq/categories/?search={text}`
  - `search` filters by bilingual name (en/ar contains)
- Items: `GET /faq/items/?category_id={id}&search={text}`
  - `category_id` filters by category
  - `search` filters by bilingual question/answer text

---

## Permissions Summary

- Read (list/retrieve for categories/items): Authenticated users
- Write (create/update): Staff/content managers only
- Delete: Superuser only
- Frontend only needs the read endpoints listed above.

---

## Notes and Caveats

- Bilingual responses depend on `Accept-Language` header. Make sure to always send it (`en` or `ar`).
- Items list returns the answer already; no extra call is required when expanding a question.
- The `categories-with-counts` endpoint currently returns the same field set as the regular categories list (`id/name/description`). No count field is exposed in the response.
- No pagination is configured for these FAQ endpoints at present.

---

## Quick Reference

- List categories
  - `GET /app/v2/crm/faq/categories/?search=`
- Category details
  - `GET /app/v2/crm/faq/categories/{id}/`
- List categories with counts (no count field in payload)
  - `GET /app/v2/crm/faq/categories/with-counts/`
- List items for a category
  - `GET /app/v2/crm/faq/items/?category_id={id}&search=`
- Item details
  - `GET /app/v2/crm/faq/items/{id}/`

Headers for all:
- `Authorization: Bearer <JWT>`
- `Accept-Language: en | ar`

---

If required, we can add drf-spectacular annotations and generate OpenAPI examples so these endpoints appear in Swagger UI at `/app/v2/docs/`.
