import InputField from '@/ui/form/input-field';
import Typography from '@/ui/typography';
import { useTranslations } from 'next-intl';
import { ChangeEvent, FC } from 'react';
import dictionary from '@/dictionaries';
import { User } from '@/ui/icons';
import styles from './style.module.scss';
import InputTextarea from '@/ui/form/input-textarea';
import Message from '@/ui/icons/message';
import Accordion from '@/ui/accordion';
import Microphone from '@/ui/icons/microphone';
import VideoCamera from '@/ui/icons/video';
import Button from '@/ui/form/button';
import { isValidEmailComplexRule } from '@/utils/validations/email';
import AudioUpload from '@/ui/audio-upload';
import VideoUpload from '@/ui/video-upload';
import useRecommendTalentStore from '@/stores/recommend-talent';
import CONFIG from '@/config';
import ToggleSwitch from '@/ui/form/toggle-switch';
import { checkEmptyValidation } from '@/utils/validations/helpers';

interface IRecommendATalentWhoAndWhy {
  onNext: () => void;
}

const RecommendATalentWhoAndWhy: FC<IRecommendATalentWhoAndWhy> = (props) => {
  const { onNext } = props;
  const t = useTranslations();

  const {
    recommendTalentData,
    setRecommendTalentData,
    uploadFile,
    isUploading,
    setAudioUploadError,
    setVideoUploadError,
    emailError,
    setEmailError,
  } = useRecommendTalentStore();

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setRecommendTalentData({ ...recommendTalentData, [name]: value });
  };

  const handleClickNext = () => {
    if (isValidEmailComplexRule(recommendTalentData?.email)) {
      onNext();
    } else {
      setEmailError(t(dictionary.pleaseEnterAValidEmailAddress));
    }
  };

  const updateAudio = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('folder', CONFIG.document.profilePicture);
    const responseId = await uploadFile(formData);

    if (responseId) {
      setRecommendTalentData({
        ...recommendTalentData,
        audioFileId: responseId.id,
      });
      setAudioUploadError(undefined);
    } else {
      setAudioUploadError(t(dictionary.fileFormatNotSupportedMP3));
    }
  };

  const updateVideo = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('folder', CONFIG.document.profilePicture);
    const responseId = await uploadFile(formData);

    if (responseId) {
      setRecommendTalentData({
        ...recommendTalentData,
        videoFileId: responseId.id,
      });
      setVideoUploadError(undefined);
    } else {
      setVideoUploadError(t(dictionary.fileFormatNotSupportedMP4));
    }
  };

  const handleToggleChange = () => {
    setRecommendTalentData({
      ...recommendTalentData,
      isAnonyomous: !recommendTalentData.isAnonyomous,
    });
  };

  const isEmpty = checkEmptyValidation({
    email: recommendTalentData.email,
  });

  const missingReason =
    !recommendTalentData.videoFileId &&
    !recommendTalentData.audioFileId &&
    !recommendTalentData.textMessage;

  return (
    <>
      <InputField
        label={dictionary.whoAreYouRecommending}
        name="email"
        value={recommendTalentData.email || ''}
        placeholder={t(dictionary.pleaseEnterEmail)}
        startIcon={<User />}
        onChange={handleChange}
        error={emailError}
      />
      <Typography
        as="h4"
        className="dialog-heading"
        dictionary={dictionary.whyAreYouRecommendingThisPerson}
      />
      <Accordion
        startIcon={<Message />}
        title={t(dictionary.writeAMessage)}
        subtitle={t(dictionary.explainTheBriefAboutThePerson)}
      >
        <div className={styles.accordionSection}>
          <InputTextarea
            label={dictionary.message}
            name="textMessage"
            placeholder={dictionary.tellUsWhyYouRecommendingThisPerson}
            value={recommendTalentData.textMessage || ''}
            onChange={handleChange}
          />
        </div>
      </Accordion>

      <Accordion
        startIcon={<Microphone />}
        title={t(dictionary.uploadVoice)}
        subtitle={t(dictionary.explainTheBriefAboutThePersonByVoice)}
      >
        <div className={styles.accordionSection}>
          <AudioUpload
            updateAudio={updateAudio}
            title={t(dictionary.clickHereToSelectFiles)}
            subtitle={t(dictionary.mp3)}
            isLoading={isUploading}
            id="recommend-a-talent-audio"
          />
        </div>
      </Accordion>

      <Accordion
        startIcon={<VideoCamera />}
        title={t(dictionary.uploadVideo)}
        subtitle={t(dictionary.explainTheBriefAboutThePersonByVideo)}
      >
        <div className={styles.accordionSection}>
          <VideoUpload
            updateVideo={updateVideo}
            title={t(dictionary.clickHereToSelectFiles)}
            subtitle={t(dictionary.mp4)}
            isLoading={isUploading}
            id="recommend-a-talent-video"
          />
        </div>
      </Accordion>
      <div className={styles.containerToggleSwitch}>
        <Typography
          as="span"
          className="dialog-heading"
          dictionary={dictionary.keepMeAnonymous}
        />
        <ToggleSwitch
          isChecked={recommendTalentData.isAnonyomous}
          onChange={handleToggleChange}
        />
      </div>
      <Button
        className="dialog-form-action"
        type="button"
        color="black"
        onClick={handleClickNext}
        disabled={isEmpty || missingReason}
        dictionary={dictionary.next}
      />
    </>
  );
};
export default RecommendATalentWhoAndWhy;
