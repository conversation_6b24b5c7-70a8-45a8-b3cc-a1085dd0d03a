import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraNetworkEnrollmentService } from '@/services/totara/my-network/network-enrollment';

/**
 * Route to enroll the network
 */
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const id = request.nextUrl.searchParams.get('id') || '';
    // Fetching network enrollment
    const response = await TotaraNetworkEnrollmentService(Number(id));
    // Return the response
    return NextResponse.json({
      data: response.data,
    });
  } catch (error) {
    // Rturn Error response
    return ErrorResponse(error);
  }
}
