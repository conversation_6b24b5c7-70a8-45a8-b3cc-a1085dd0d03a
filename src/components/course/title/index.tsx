import { FC, ReactNode, useMemo } from 'react';
import ICourse from '@/types/domain/learning/course';
import Link from '../link';
import cn from 'classnames';
import styles from './style.module.scss';

interface ITitle {
  course: ICourse;
  className?: string;
  children?: ReactNode;
}

// React component to handle course link
const Title: FC<ITitle> = (props) => {
  // Deconstructing props
  const { className, course } = props;

  // Getting course title
  const title = useMemo(() => {
    return course?.title || '';
  }, [course?.title]);

  // Dynamic classNames
  const classNames = cn({
    [styles.title]: true,
    [className || '']: className,
  });

  // Return JSX
  return (
    <p className={classNames}>
      <Link course={course}>
        <span>{title}</span>
      </Link>
    </p>
  );
};

// Exporting course component
export default Title;
