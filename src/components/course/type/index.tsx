import { FC, ReactNode, useMemo } from 'react';
import ICourse from '@/types/domain/learning/course';
import Typography from '@/ui/typography';
import { useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';

interface IType {
  course: ICourse;
  className?: string;
  children?: ReactNode;
}

// React component to handle course link
const Type: FC<IType> = (props) => {
  // Deconstructing props
  const { className, course } = props;
  const t = useTranslations();

  // Getting course type
  const type = useMemo(() => {
    return course?.component_type || course?.type || '';
  }, [course?.component_type, course?.type]);

  // Return JSX)
  return (
    <Typography as="p" className={className}>
      {type.length ? t(type.toLowerCase()) : t(dictionary.course)}
    </Typography>
  );
};

// Exporting course component
export default Type;
