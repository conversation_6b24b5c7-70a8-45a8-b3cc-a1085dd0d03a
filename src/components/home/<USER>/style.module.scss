@use 'mixins' as *;

.aiBanner {
  @include borderRadius(var(--radius));
  @include forMarginPadding(padding, size(35), size(56), size(35), size(56));
  position: relative;
  overflow: hidden;
  background: var(--blue-900);

  min-height: size(208);
  width: 100%;
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p {
    color: var(--white);
  }

  a[class*='textLink'] {
    text-decoration: underline;

    span {
      background: none;
    }

    &:hover {
      text-decoration: none;
    }
  }
}

.aiImg {
  width: 100%;
  height: 100%;
  position: absolute;
  @include rightToLeft(0);
  top: 0;
}

.text {
  width: 70%;
  position: relative;
  z-index: 2;

  p {
    font-size: size(18);
  }
}

.heading {
  @include forMarginPadding(margin, size(0), size(0), size(10), size(0));
}

.icon {
  width: size(34);
  height: size(34);
  @include forMarginPadding(margin, size(0), size(0), size(0), size(20));
  display: inline-block;
}

@include for-dark-theme {
  .aiBanner {
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    a {
      color: #ffffff !important;
    }
    a {
      span {
        background: linear-gradient(
          to bottom,
          #ffffff 0%,
          #ffffff 98%
        ) !important;
        background-size: 100% 1px !important;
        background-repeat: no-repeat !important;
        background-position: left 100% !important;

        @include rtl {
          background-position: right 100% !important;
        }
      }
    }
  }
}

@include for-all-phone() {
  .aiBanner {
    @include forMarginPadding(padding, size(20), size(20), size(25), size(20));
    min-height: size(148);
  }

  .text {
    width: 85%;

    p {
      font-size: size(14);
    }
  }

  .heading {
    font-size: size(20);
    @include forMarginPadding(margin, size(0), size(0), size(10), size(0));
  }

  .icon {
    width: size(22);
    height: size(22);
    @include forMarginPadding(margin, size(0), size(0), size(0), size(10));
    position: relative;
    top: size(5);
  }

  .btnWrap {
    a {
      font-size: size(14);
    }
  }
}
