import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraRateReviewService } from '@/services/totara/courses/rate-review';

export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const courseId = request.nextUrl.searchParams.get('courseId') || '';
    const rating = request.nextUrl.searchParams.get('rating') || '';

    // Fetching records counts
    const response = await TotaraRateReviewService(courseId, rating);

    // Return the response
    return NextResponse.json(response?.data);
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
