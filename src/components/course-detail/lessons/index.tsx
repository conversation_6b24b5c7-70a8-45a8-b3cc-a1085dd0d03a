import { FC, useCallback } from 'react';
import Typography from '@/ui/typography';
import ICoreSection from '@/types/domain/learning/course/coreSection';
import ExternalIcon from '@/ui/icons/external';
import Button from '@/ui/form/button';
import useStartLearningStore from '@/stores/learning/courses/start-learning';
import useTotaraAuth from '@/stores/totara-auth';
import useAuthStore from '@/stores/auth';
import { useLocale } from 'next-intl';

// Styles
import styles from './style.module.scss';
import EmptyMessage from '@/ui/empty-message';

interface ILessons {
  list?: ICoreSection[];
  courseType?: string;
  isEnrolled?: boolean;
}

const Lessons: FC<ILessons> = (props) => {
  const { list = [], courseType, isEnrolled } = props;
  const onStartLearning = useStartLearningStore((x) => x.fetch);
  const onTotaraAuthFetch = useTotaraAuth((x) => x.fetch);
  const setIsLoading = useTotaraAuth((x) => x.setIsLoading);
  const user = useAuthStore((x) => x.user);
  const locale = useLocale();

  const onTotaraLogin = useCallback(
    async (url: string) => {
      setIsLoading(true);
      onTotaraAuthFetch(url, user?.idn as string, locale);
      onStartLearning();
    },
    [user?.idn, onTotaraAuthFetch, onStartLearning, setIsLoading, locale],
  );

  //Return JSX
  return (
    <div className={styles.description}>
      <Typography as="h4">Lessons</Typography>
      {list && list.length > 0 ? (
        <div className={styles.list}>
          {list?.map((section) => {
            if (section?.data && section?.data?.length > 0) {
              return (
                <div key={section.id} className={styles.card}>
                  <div className={styles.data}>
                    <Typography as="p" className={styles.title}>
                      {section.data[0].name}
                    </Typography>
                    <Typography as="span" className={styles.content}>
                      LinkedIn Learning is a recognized Approved Provider.
                    </Typography>
                    <button
                      className={styles.externalLink}
                      disabled={!section?.data?.[0].available || !isEnrolled}
                      onClick={() =>
                        onTotaraLogin(section?.data?.[0].viewurl as string)
                      }
                    >
                      <i>
                        <ExternalIcon />
                      </i>
                    </button>
                  </div>
                  {courseType == 'Instructor-led' ||
                    ((courseType == 'Blended' || courseType == 'مزيج') && (
                      <Button
                        className={styles.lessonCta}
                        disabled={!section?.data?.[0].available || !isEnrolled}
                        onClick={() =>
                          onTotaraLogin(section?.data?.[0].viewurl as string)
                        }
                        color="black"
                      >
                        Start Course
                      </Button>
                    ))}
                </div>
              );
            }
          })}
        </div>
      ) : (
        <EmptyMessage
          icon="search"
          title="No Lessons"
          description="No Lessons"
        />
      )}
    </div>
  );
};

export default Lessons;
