import imagePlaceholder from '@public/images/imagePlaceholder.png';

export const coachExperience = [
  { company: 'IBM', logo: imagePlaceholder },
  { company: 'Apple', logo: imagePlaceholder },
  { company: 'Google', logo: imagePlaceholder },
  { company: 'Microsoft', logo: imagePlaceholder },
  { company: 'Adobe', logo: imagePlaceholder },
];

export const coachReviews = [
  {
    reviewId: 1,
    userPhoto: '',
    firstName: '<PERSON>',
    rating: 5,
    date: 'May 4, 2025',
    comment:
      'Thanks <PERSON>! <PERSON> is a very experienced mentor. She has helped me many times find clarity on different projects and how to approach them from a HCD perspective, but also how to present your findings and work in an engaging way that will keep the interest of the participants.',
  },
  {
    reviewId: 2,
    userPhoto: '',
    firstName: 'Sara',
    rating: 5,
    date: 'May 4, 2025',
    comment:
      'Thanks <PERSON>! <PERSON> is a very experienced mentor. She has helped me many times find clarity on different projects and how to approach them from a HCD perspective, but also how to present your findings and work in an engaging way that will keep the interest of the participants.',
  },
];

export const coachEducation = [
  {
    id: 1,
    school: 'Abu Dhabi University',
    degree_name: 'Masters in Business Administration, Business',
    logo: '',
    date_from: 'Oct 2018', // if no day defined, put - yyyy-mm-01
    date_to: 'Jun 2020', // if no day defined, put - yyyy-mm-01 --- if not end date this is currently working position
    location: 'Abu Dhabi, UAE',
  },
  {
    id: 2,
    school: 'Harvard University',
    degree_name: 'Bachelor of Science in Architectural Engineering',
    logo: '',
    date_from: 'May 2014', // if no day defined, put - yyyy-mm-01
    date_to: 'Jun 2018', // if no day defined, put - yyyy-mm-01 --- if not end date this is currently working position
    location: 'Cambridge, USA',
  },
];

export const coachWorkHistory = [
  {
    id: 1,
    employer: 'Google Inc',
    employer_id: 1,
    logo: '',
    date_from: 'Jun 2020',
    date_to: '', // if no day defined, put - yyyy-mm-01 --- if not end date this is currently working position
    time_period: '',
    location: 'Abu Dhabi, UAE',
    designation: 'Head of Content Strategy', // TITLE IS DESIGNATION ACTUALLY
    department: 'Department of Content and Strategy',
  },
  {
    id: 2,
    employer: 'Ernst & Young',
    employer_id: 2,
    logo: '',
    date_from: 'Jun 2017',
    date_to: 'May 2020', // if no day defined, put - yyyy-mm-01 --- if not end date this is currently working position
    time_period: '2 yrs 5 mos',
    location: 'Abu Dhabi, UAE',
    designation: 'Senior Content Strategy Manager', // TITLE IS DESIGNATION ACTUALLY
    department: 'Department of Content and Strategy',
  },
  // {
  //   id: 3,
  //   employer: "Ernst & Young",
  //   employer_id: 2,
  //   logo: "",
  //   date_from: "Jun 2015",
  //   date_to: "May 2017", // if no day defined, put - yyyy-mm-01 --- if not end date this is currently working position
  //   time_period: "2 yrs 5 mos",
  //   location: "Abu Dhabi, UAE",
  //   designation: "Content Strategy Manager", // TITLE IS DESIGNATION ACTUALLY
  //   department: "Department of Content and Strategy"
  // },
];
