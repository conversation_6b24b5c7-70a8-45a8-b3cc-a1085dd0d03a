import { FC, Fragment, useMemo } from 'react';
import styles from './style.module.scss';
import ISlot from '@/types/domain/learning/coach/slot';
import Typography from '@/ui/typography';
import Item from './item';
import useCoachBookingStore from '@/stores/learning/coaches/booking';
import { useLocale, useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';
import moment from 'moment';

interface ISlots {
  date: string;
  list: ISlot[];
}

/**
 * Component to handle slots
 */
const Slots: FC<ISlots> = (props) => {
  // Deconstructing props
  const { date: _date, list } = props;
  // Getting selected slot
  const selected = useCoachBookingStore((x) => x.selected);

  const t = useTranslations();
  const locale = useLocale();

  /**
   * Method to typecast the slot
   * date into moment object
   */
  const date = useMemo(() => {
    return moment(_date, 'ddd MMM DD YYYY', 'en').locale(
      locale === 'ar' ? 'ar' : 'en',
    );
  }, [_date, locale]);

  /**
   * Method to filter the slots
   */
  const _slots = useMemo(() => {
    const filtered = list.filter((slot) => slot.slot_date === _date);
    const sorted = filtered.sort((a, b) => {
      const aTime = new Date(a?.time_from || '').getTime();
      const bTime = new Date(b?.time_from || '').getTime();
      return aTime - bTime;
    });
    return sorted;
  }, [list, _date]);

  // Return JSX
  return (
    <li>
      <div className={styles.left}>
        <div className={styles.center}>
          <Typography as="span" className={styles.day}>
            {date.format('ddd')}
          </Typography>
          <Typography as="span" className={styles.date}>
            {date.format('DD')}
          </Typography>
          <Typography as="span" className={styles.month}>
            {date.format('MMM')}
          </Typography>
        </div>
      </div>
      <div className={styles.data}>
        {_slots.map((x, i) => (
          <Fragment key={x?.slot_id}>
            {selected.slot_id === x.slot_id ? (
              <div className={styles.radioSelected}></div>
            ) : (
              <div className={styles.radionNotSelected}></div>
            )}
            <Item slot={x} title={t(dictionary.Session) + ` ${i + 1}`} />
          </Fragment>
        ))}
      </div>
    </li>
  );
};

// Exporting component
export default Slots;
