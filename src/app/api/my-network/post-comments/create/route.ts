import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraCreatePostCommentService } from '@/services/totara/my-network';

/**
 * Route to create new comment on post
 */
export async function POST(request: NextRequest) {
  try {
    // Getting request params

    const { postId, content, draftid } = await request.json();

    // call API to create new comment on post
    const response = await TotaraCreatePostCommentService(
      postId,
      content,
      draftid,
    );
    // Return the response
    return NextResponse.json({
      success: response.data.success,
      message: response.data.message,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error);
  }
}
