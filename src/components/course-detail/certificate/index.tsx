'use client';
import { FC } from 'react';
import { ICertificate } from '@/types/domain/learning/course';
import Typography from '@/ui/typography';
import certificate from '@public/images/certificate.jpg';
import Item from './item';

// Styles
import styles from './style.module.scss';
import EmptyMessage from '@/ui/empty-message';
import dictionary from '@/dictionaries';

interface ICertificates {
  list?: ICertificate[];
}

const Certificates: FC<ICertificates> = (props) => {
  const { list = [] } = props;

  return (
    <div className={styles.certificates}>
      <Typography as="h4" dictionary={dictionary.getACertificate} />
      {list && list.length > 0 ? (
        <nav>
          <ul className={styles.list}>
            {list.map((x) => (
              <li key={x.name}>
                <Item image={certificate} link={x.link} description={x.name} />
              </li>
            ))}
          </ul>
        </nav>
      ) : (
        <EmptyMessage
          icon="certificates"
          title={dictionary.noInformationAvailable}
        />
      )}
    </div>
  );
};

export default Certificates;
