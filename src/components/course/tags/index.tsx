import Tag from '@/ui/tag';
import styles from './style.module.scss';
import { FC, useMemo } from 'react';
import ICourse from '@/types/domain/learning/course';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';

interface ITags {
  course: ICourse;
  className?: string;
}

const Tags: FC<ITags> = (props) => {
  // Deconstructing props
  const { course, className } = props;
  const t = useTranslations();

  // Getting progress status
  const isInprogress = useMemo(() => {
    // Getting status
    const status = course?.status?.toUpperCase();
    // Checking if course is in progress
    return status === 'INPROGRESS';
  }, [course?.status]);

  // Getting progress status
  const isOverdue = useMemo(() => {
    // Getting overdue status
    const overdue = course?.course_due_date?.toUpperCase();
    // Checking if course is overdue
    return overdue?.includes('OVERDUE') ? true : false;
  }, [course?.course_due_date]);

  // Getting progress status
  const isDue = useMemo(() => {
    return isInprogress && !isOverdue;
  }, [isInprogress, isOverdue]);

  const isCompleted = useMemo(() => {
    return course.progress === 100;
  }, [course.progress]);

  // Returns JSX
  return (
    (isInprogress || isDue || isOverdue || isCompleted) && (
      <div className={styles.tags + ` ${className}`}>
        {isInprogress && course?.status && (
          <Tag color="yellow">{t(dictionary.inProgress)}</Tag>
        )}
        {isDue && course?.course_due_date && (
          <Tag color="blue">{course?.course_due_date}</Tag>
        )}
        {isOverdue && course?.course_due_date && (
          <Tag color="red">{course?.course_due_date}</Tag>
        )}
        {isCompleted && <Tag color="green">{t(dictionary.completed)}</Tag>}
      </div>
    )
  );
};

export default Tags;
