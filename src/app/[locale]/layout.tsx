import type { Metadata } from 'next';
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { routing } from '@/i18n/routing';

import Header from '@/layout/header';
import SiderBar from '@/layout/sidebar';
import SettingsBar from '@/components/common/settings-bar';
import MobileNavigation from '@/layout/mobile-navigation';

import '../../styles/global.scss';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import AuthProvider from '@/providers/auth-provider';
import ThemeProvider from '@/providers/theme-provider';
import { GoogleAnalytics } from '@next/third-parties/google';
import type { Viewport } from 'next';

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 12,
  minimumScale: 0.5,
  userScalable: true,
  viewportFit: 'cover',
};

export const metadata: Metadata = {
  title: 'Welcome to Tomouh',
  description:
    'A hyper personalized learning platform for Abu Dhabi government employees.',
  keywords:
    'DGE, ADDA, DGS, HRA, abu dhabi digital authority, department of government support, Human Resources Authority, department of government enablement, Abu Dhabi School of Government,  دائرة الإسناد الحكومي, هيئة الموارد البشرية, أكاديمية أبوظبي الحكومية, هيئة أبوظبي الرقمية, دائرة التمكين الحكومي',
  openGraph: {
    title: 'Welcome to Tomouh',
    description:
      'A hyper personalized learning platform for Abu Dhabi government employees.',
    url: `${process.env.BASE_URL}/`,
    images: [
      {
        url: `${process.env.BASE_URL}/images/icon-512.png`,
        secureUrl: `${process.env.BASE_URL}/images/icon-512.png`,
      },
    ],
  },
  twitter: {
    title: 'Welcome to Tomouh',
    description:
      'A hyper personalized learning platform for Abu Dhabi government employees.',
    images: [
      {
        url: `${process.env.BASE_URL}/images/icon-512.png`,
        secureUrl: `${process.env.BASE_URL}/images/icon-512.png`,
      },
    ],
  },
};

interface ILayout {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

export default async function LocaleLayout(props: ILayout) {
  // Deconstruct the incoming props
  const { children, params } = props;

  // Getting locale from the incoming `params` promise
  const locale = (await params).locale;

  // Ensure that the incoming `locale` is valid
  if (!routing.locales.includes(locale as 'ar' | 'en')) {
    notFound();
  }

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages();

  // Render the layout
  return (
    <html lang={locale} dir={locale === 'ar' ? 'rtl' : 'ltr'}>
      <head>
        <link
          rel="apple-touch-icon"
          sizes="180x180"
          href="/images/apple-touch-icon.png"
        />
      </head>
      <body>
        <NextIntlClientProvider messages={messages}>
          <AuthProvider>
            <ThemeProvider>
              <Header />
              <SiderBar />
              <main>{children}</main>
              <SettingsBar />
              <MobileNavigation />
            </ThemeProvider>
          </AuthProvider>
        </NextIntlClientProvider>
      </body>
      {process.env.GA_MEASUREMENT_ID && (
        <GoogleAnalytics gaId={process.env.GA_MEASUREMENT_ID} />
      )}
    </html>
  );
}
