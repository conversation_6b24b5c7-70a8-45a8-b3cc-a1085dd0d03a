@use 'mixins' as *;

.moreBtn {
  color: var(--text-link-blue);
  font-size: size(16);
  background: none;
  border: none;
  cursor: pointer;
  font-weight: 500;

  span {
    display: inline;
    padding-bottom: 0;
    transition: all 0.5s linear;
    background: linear-gradient(
      to bottom,
      var(--text-link-blue) 0%,
      var(--text-link-blue) 98%
    );
    background-size: 0 1px;
    background-repeat: no-repeat;
    background-position: left 100%;

    @include rtl {
      background-position: right 100%;
    }
  }
}

.members {
  display: flex;
  font-size: size(16);
  color: var(--grey-700);
  fill: var(--grey-700);
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));

  i {
    @include forMarginPadding(margin, size(0), size(5), size(0), size(0));
    width: size(20);
    height: size(20);
  }
}

.title {
  font-size: size(32);
  font-weight: 700;
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));
}

@include hover() {
  .moreBtn {
    &:hover {
      span {
        background-size: 100% 1px;
      }
    }
  }
}
