import { FC } from 'react';
import styles from './style.module.scss';
import ICourse from '@/types/domain/learning/course';
import cn from 'classnames';
import Link from '../../link';
import Title from '../../title';
import Provider from '../../provider';
import Type from '../../type';
import Favorite from '../../favorite';
import Image from '../../image';
import InfoList from '../../info-list';
import Tags from '../../tags';
import Progress from '../../progress';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';
import Options from '../../options';

interface IBeta {
  course: ICourse;
  inProgress?: boolean;
}

const Beta: FC<IBeta> = (props) => {
  // Deconstructing props
  const { course, inProgress } = props;

  // Creating classname for card
  const classNames = cn({
    [styles.card]: true,
    [styles.medium]: true,
  });

  const t = useTranslations();

  const continueCourseLabel =
    t(dictionary.continue) + ' ' + t(dictionary.course);
  const continueProgramLabel =
    t(dictionary.continue) + ' ' + t(dictionary.program);
  const continueLabel =
    course.type === 'course' ? continueCourseLabel : continueProgramLabel;

  // Return JSX
  return (
    <div className={classNames}>
      <Image course={course} className={styles.bg} alt={course?.title} />
      <div className={styles.content}>
        <Link course={course} className={styles.link} />
        <div className={styles.top}>
          <Type course={course} />
          <Favorite course={course} className={styles.favorite} />
          <Options course={course} />
        </div>
        <div className={styles.bottom}>
          <Provider course={course} className={styles.provider} />
          <Title course={course} className={styles.heading} />
          {!inProgress ? (
            <InfoList
              course={course}
              className={styles.infoList}
              hasRating={false}
            />
          ) : (
            <p className={styles.continueLabel}>{continueLabel}</p>
          )}
          <Tags course={course} />
          <Progress course={course} />
        </div>
      </div>
    </div>
  );
};

export default Beta;
