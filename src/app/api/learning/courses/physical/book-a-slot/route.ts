import { ErrorResponse } from '@/utils/fetcher';
import { NextRequest, NextResponse } from 'next/server';
import { AxiosError } from 'axios';
import { TotaraBookASlotService } from '@/services/totara/courses/physical';

export async function POST(request: NextRequest) {
  try {
    // Getting request body
    const { courseId, slotId, previousSlotId } = await request.json();

    const response = await TotaraBookASlotService(
      courseId,
      slotId,
      previousSlotId,
    );
    // Return the response
    return NextResponse.json({
      bookingResponse: response?.data,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error as AxiosError);
  }
}
