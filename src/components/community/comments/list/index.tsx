import useCommunityDetailsCommentsStore from '@/stores/my-network/community-details/comments';
import styles from './style.module.scss';
import { useEffect } from 'react';
import CommentItem from '../comment';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';
import ListInput from './list-input';

interface ICommentsList {
  postId: number;
}

const CommentsList: React.FC<ICommentsList> = ({ postId }) => {
  const { comments, getComments, isFetching, createComment } =
    useCommunityDetailsCommentsStore();
  useEffect(() => {
    getComments(postId);
  }, [getComments, postId]);

  const postComments = comments[postId] || [];

  const handleSendComment = (
    commentText: string,
    formData: FormData | undefined,
  ) => {
    createComment(postId, commentText, formData);
  };

  return (
    <>
      <div className={styles.listWrapper}>
        <WithLoader loader={<Loading />} loading={isFetching}>
          {postComments &&
            postComments.map((comment) => {
              return (
                <CommentItem
                  comment={comment}
                  key={comment.id}
                  postId={postId}
                />
              );
            })}
        </WithLoader>
      </div>
      <ListInput onClickSend={handleSendComment} />
    </>
  );
};
export default CommentsList;
