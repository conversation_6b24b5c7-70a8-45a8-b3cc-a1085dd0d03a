import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchProgramRequiredAndRecommendedService } from '@/services/totara/courses/programs';

// Route to get the program details
export async function GET() {
  try {
    // Fetch the data
    const response = await TotaraFetchProgramRequiredAndRecommendedService();

    // Return the response
    return NextResponse.json({
      requiredPrograms: response?.data.requiredprograms || null,
      recommendedPrograms: response?.data.recommendedprograms || null,
      programCategories: response?.data.programcategories || null,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
