import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchProfileService } from '@/services/totara/profile';

/**
 * Route to get the announcements
 */
export async function GET() {
  try {
    // Updating user profile
    const response = await TotaraFetchProfileService();
    // Return the response
    return NextResponse.json({
      user: response?.data,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
