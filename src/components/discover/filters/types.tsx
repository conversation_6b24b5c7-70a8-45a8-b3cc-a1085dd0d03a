import { useCallback, useEffect, useState } from 'react';
import ChipButton from '@/ui/form/chip-button';
import Typography from '@/ui/typography';
import useCoursesFiltersStore from '@/stores/learning/courses/filters/advance';
import useSearchCoursesStore from '@/stores/learning/courses/search';

// Styles
import styles from './style.module.scss';
import dictionaries from '@/dictionaries';
import { useTranslations } from 'next-intl';
import { RightArrow } from '@/ui/icons';
import Loading from '../categories/loading';

const Types = () => {
  const [isAccordionOpen, setIsAccordionOpen] = useState(true);
  const isFetching = useCoursesFiltersStore((x) => x.isFetching);
  // Getting the types
  const list = useCoursesFiltersStore((x) => x?.list?.types);

  // Getting selected filter
  const selected = useSearchCoursesStore((x) => x.filters?.learningType);

  // Getting method to set filter
  const setFilter = useSearchCoursesStore((x) => x.setFilter);

  useEffect(() => {
    if (selected) {
      setIsAccordionOpen(true);
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const handleAccordionToggle = () => {
    setIsAccordionOpen((prev) => !prev);
  };

  // Method to handle click
  const onClick = useCallback(
    (x?: string) => {
      if (x) {
        setFilter({
          key: 'learningType',
          value: selected === x ? undefined : x,
        });
      }
    },
    [setFilter, selected],
  );
  const t = useTranslations();
  // Return JSX
  return (
    <nav className={styles.filter}>
      <div className={styles.filterHeader} onClick={handleAccordionToggle}>
        <Typography as="h4" dictionary={dictionaries.learningType} />
        <div
          className={`${styles.accordionAction} ${isAccordionOpen ? styles.openState : styles.closeState}`}
        >
          <RightArrow />
        </div>
      </div>
      {isAccordionOpen && (
        <>
          {isFetching ? (
            <div className={styles.loadingContainer}>
              <Loading />
            </div>
          ) : (
            <ul className={styles.listContainer}>
              {list?.map((x, i) => (
                <li key={'type-' + i}>
                  <ChipButton
                    variant="rounded"
                    active={selected === x}
                    onClick={() => onClick(x)}
                  >
                    {t(x) || x}
                  </ChipButton>
                </li>
              ))}
            </ul>
          )}
        </>
      )}
    </nav>
  );
};

// Export the component
export default Types;
