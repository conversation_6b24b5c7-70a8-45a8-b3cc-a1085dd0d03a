import { FC, useCallback } from 'react';
import Typography from '@/ui/typography';
import { Close, Reset } from '@/ui/icons';
import useSearchCoursesStore from '@/stores/learning/courses/search';
import useSearchCourses from '@/hooks/helpers/search/course/useSearchCourses';

// Styles
import styles from './style.module.scss';
import dictionary from '@/dictionaries';

interface IHead {
  onClose: (isReset?: boolean) => void;
  resetEnabled?: boolean;
}

const Head: FC<IHead> = (props) => {
  // Getting methods from custom hook to handle search courses
  const { removeQueryParams } = useSearchCourses();

  // Deconstruct props
  const { onClose, resetEnabled = false } = props;

  // Getting method to set filter
  const onClear = useSearchCoursesStore((x) => x.clearFilters);

  /**
   * Method to reset all filters
   */
  const onReset = useCallback(() => {
    // Clear filters
    onClear();

    // Remove query params
    removeQueryParams(['learningType', 'creator', 'duration']);

    // Close the popup
    onClose(true);
  }, [onClear, onClose, removeQueryParams]);

  // Return JSX
  return (
    <div className={styles.head}>
      <div className={styles.top}>
        <button onClick={() => onClose()} className={styles.closeFilter}>
          <i>
            <Close />
          </i>
        </button>
      </div>
      <div className={styles.bottom}>
        <Typography as="h2" className="mb-0" dictionary={dictionary.Filter} />
        {resetEnabled && (
          <button type="button" className={styles.resetBtn} onClick={onReset}>
            <i>
              <Reset />
            </i>
            <Typography as="span" dictionary={dictionary.resetAll}>
              Reset all
            </Typography>
          </button>
        )}
      </div>
    </div>
  );
};

export default Head;
