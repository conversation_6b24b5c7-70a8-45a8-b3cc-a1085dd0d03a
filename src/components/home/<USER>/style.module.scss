@use 'mixins' as *;

.list {
  @include forMarginPadding(padding, size(0), size(0), size(24), size(0));

  ul {
    display: grid;
    grid-template-columns: repeat(3, 1fr);

    li {
      @include forMarginPadding(padding, size(0), size(8), size(0), size(8));

      position: relative;
      list-style: none;
      &:first-child {
        @include forMarginPadding(padding, size(0), size(8), size(0), size(0));
      }
      &:last-child {
        @include forMarginPadding(padding, size(0), size(0), size(0), size(8));
      }
    }
  }
}

@include for-all-phone() {
  .list {
    ul {
      grid-template-columns: repeat(1, 1fr);

      li {
        @include forMarginPadding(padding, size(0), size(5), size(0), size(5));
        &:first-child {
          @include forMarginPadding(
            padding,
            size(0),
            size(5),
            size(0),
            size(0)
          );
        }

        &:nth-child(3) {
          @include forMarginPadding(
            padding,
            size(0),
            size(0),
            size(0),
            size(5)
          );
        }
        &:nth-child(2),
        &:nth-child(3) {
          display: none;
        }
      }
    }
  }
}
