@use 'mixins' as *;

.sideNavs {
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));

  li {
    list-style: none;
    border-bottom: solid 1px var(--grey-400);

    &:last-child {
      border: none;

      a {
        @include forMarginPadding(
          padding,
          size(12),
          size(0),
          size(12),
          size(0)
        );
      }
    }

    &:first-child {
      a {
        @include forMarginPadding(
          padding,
          size(12),
          size(0),
          size(12),
          size(0)
        );
      }
    }

    a {
      border: none;
      background: none;
      @include forMarginPadding(padding, size(12), size(0), size(12), size(0));
      line-height: 1;
      display: flex;
      align-items: center;
      width: 100%;
      cursor: pointer;
      fill: var(--grey-900);

      .leftIcon {
        width: size(20);
        height: size(20);
        @include forMarginPadding(margin, size(0), size(12), size(0), size(0));

        svg {
          width: 100%;
          height: 100%;
        }
      }

      span {
        font-size: size(16);
      }

      .rightArrow {
        @include forMarginPadding(margin, size(0), size(0), size(0), auto);
        width: size(9.5);
        height: size(17.5);
        display: flex;
        align-items: center;

        svg {
          width: size(6);
          height: size(11);
        }
      }
    }
  }
}
