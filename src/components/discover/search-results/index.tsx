import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import WithLoader from '@/ui/skeleton/with-loader';
import Paper from '@/ui/paper';
import Grid from '@/ui/grid';
import Typography from '@/ui/typography';
import EmptyMessage from '@/ui/empty-message';
import Loading from '@/components/common/loading';
import Tags from './tags';
import Filters from '../filters';
import useSearchCourses from '@/hooks/helpers/search/course/useSearchCourses';
import useSearchCoursesStore from '@/stores/learning/courses/search';
import useOnScrolledBottom from '@/hooks/helpers/useOnScrolledBottom';
import useNewCoursesStore from '@/stores/learning/courses/new';
import useCoursesCategoriesStore from '@/stores/learning/courses/categories';
import CardsSection from '@/components/common/cards-section';
import dictionary from '@/dictionaries';
import { useTranslations, useLocale } from 'next-intl';
import CONFIG from '@/config';

// Styles
import styles from './style.module.scss';
import Course from '@/components/course';
import { LeftArrow, RightArrow } from '@/ui/icons';
import IFilter from '@/types/domain/learning/course/filter';

const SearchResults = () => {
  // Getting query params from custom hook to handle search courses
  const { queryParams, updateQueryParam } = useSearchCourses();
  const [catName, setCatName] = useState<string | undefined>('');
  // Getting loading state, list of courses, subcategory, pagination, fetch action, loadmore action
  const {
    isFetching,
    list: searchedList,
    subcategory,
    pagination,
    fetch: onFetch,
    loadmore: onLoadmore,
    isLoadingMore,
  } = useSearchCoursesStore();

  const {
    fetch: onNewlyFetch,
    list: newlyList,
    isFetching: isFetchingNew,
  } = useNewCoursesStore();

  const locale = useLocale();
  const isRTL = locale === 'ar';
  const t = useTranslations();
  const totalResultsTxt = t(dictionary.totalResults);
  const resultTxt = t(dictionary.result);

  const categories = useCoursesCategoriesStore((x) => x.list);

  const itemsNumber = searchedList?.length;

  const subcategoryRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(false);

  // Effect to loadmore data
  useOnScrolledBottom(onLoadmore, isLoadingMore);

  /**
   * Method to handle click on category item
   */
  const handleCategoryClick = useCallback(
    (id: string | undefined, name: string | undefined) => {
      setCatName(name);
      // Updating query param in route
      updateQueryParam('category', id as string);
    },
    [updateQueryParam],
  );

  /**
   * Method to fetch the searched courses
   */
  const fetchCourses = useCallback(() => {
    if (Object.keys(queryParams).length > 0) {
      const apiPayload: {
        keyword?: string | null;
        categoryID?: string | null;
        learningType?: string | null;
        creator?: string[] | string | null;
        duration?: string | null;
      } = {
        keyword: queryParams.search,
        categoryID: queryParams.category,
        learningType: queryParams.learningType,
        creator: queryParams.creator,
        duration: queryParams.duration,
      };

      if (apiPayload.creator && Array.isArray(apiPayload.creator)) {
        apiPayload.creator = JSON.stringify(apiPayload.creator);
      }

      onFetch(apiPayload as IFilter);
    }
  }, [queryParams, onFetch]);

  useEffect(() => {
    fetchCourses();
  }, [fetchCourses]);

  const records = useMemo(() => {
    if (pagination?.total && pagination?.total > 0) {
      return pagination?.total > 1 ? (
        <Typography as="h4">{`${pagination.total} ${totalResultsTxt}`}</Typography>
      ) : (
        <Typography as="h4">{`${pagination.total} ${resultTxt}`}</Typography>
      );
    }
    return null;
  }, [pagination, totalResultsTxt, resultTxt]);

  useEffect(() => {
    onNewlyFetch({ categoryID: queryParams.category });
  }, [onNewlyFetch, queryParams.category]);

  const newlyItems = useMemo(() => {
    // Mapping list to create cards
    return (
      newlyList?.map((item) => (
        <Course key={item.courseid} course={item} variant="beta" />
      )) || []
    );
  }, [newlyList]);

  const isCategoryPage = queryParams.category ? true : false;
  const hasFilters =
    queryParams.search ||
    queryParams.learningType ||
    queryParams.creator?.length ||
    queryParams.duration
      ? true
      : false;

  const isParentCategory = useMemo(() => {
    categories.forEach((category) => {
      if (category.categoryid === Number(queryParams.category)) {
        setCatName(category.title);
        return true;
      }
    });
    return false;
  }, [categories, queryParams.category]);

  const categoryItems = useMemo(() => {
    if (itemsNumber > 0 && hasFilters) {
      return itemsNumber > 1
        ? '(' + itemsNumber + ' ' + totalResultsTxt + ')'
        : '(' + itemsNumber + ' ' + resultTxt + ')';
    }
    return null;
  }, [itemsNumber, hasFilters, totalResultsTxt, resultTxt]);

  const handleScroll = useCallback(() => {
    if (subcategoryRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = subcategoryRef.current;
      if (isRTL) {
        setShowRightArrow(scrollLeft < 0);
        setShowLeftArrow(-scrollLeft + clientWidth < scrollWidth - 10);
      } else {
        setShowLeftArrow(scrollLeft > 0);
        setShowRightArrow(scrollLeft + clientWidth < scrollWidth);
      }
    }
  }, [isRTL]);

  useEffect(() => {
    handleScroll(); // Initial check on mount and when subcategories change
  }, [subcategory, handleScroll]);

  const scrollLeftHandler = useCallback(() => {
    subcategoryRef.current?.scrollBy({
      left: -300,
      behavior: 'smooth',
    });
  }, []);

  const scrollRightHandler = useCallback(() => {
    subcategoryRef.current?.scrollBy({
      left: 300,
      behavior: 'smooth',
    });
  }, []);

  return (
    <Paper className={styles.searchResultsWrapper}>
      <WithLoader loading={isFetching} loader={<Loading />}>
        <div className={styles.resultHeading}>
          {!isCategoryPage ? (
            records
          ) : (
            <div className={styles.categoryNameWrapper}>
              <Typography as="h4">{catName}</Typography>
              <p className={styles.totalCategories}>
                {!isFetching ? categoryItems : null}
              </p>
            </div>
          )}
          {searchedList?.length > 0 && <Filters />}
        </div>
        {subcategory?.length > 0 && !isFetching && (
          <div className={styles.subcategoryScrollWrapper}>
            {showLeftArrow && (
              <button
                className={styles.scrollArrow}
                onClick={scrollLeftHandler}
              >
                <LeftArrow />
              </button>
            )}
            <div
              className={styles.subcategoryWrapper}
              ref={subcategoryRef}
              onScroll={handleScroll}
            >
              {subcategory.map((cat) => (
                <button
                  key={cat.id}
                  onClick={() =>
                    handleCategoryClick(cat.id?.toString(), cat.name)
                  }
                  className={styles.subcategoryButton} // Add a class for styling
                >
                  {cat.name}
                </button>
              ))}
            </div>
            {showRightArrow && (
              <button
                className={styles.scrollArrow}
                onClick={scrollRightHandler}
              >
                <RightArrow />
              </button>
            )}
          </div>
        )}
        <Tags />

        {isParentCategory && !hasFilters ? (
          <>
            <WithLoader loading={isFetchingNew} loader={<Loading />}>
              <CardsSection
                title={t(dictionary.newlyAdded)}
                listingPageUrl={
                  CONFIG.routes.discover.newCategory +
                  `?id=${queryParams.category}`
                }
                items={newlyItems}
                limit={8}
                loading={isFetching}
                className="learningSlider"
                slidesConfig={{
                  desktop: {
                    view: 3.4,
                    gap: 18,
                  },
                  mobile: {
                    view: 1.3,
                    gap: 10,
                  },
                }}
                noDataMessage={
                  <EmptyMessage
                    icon="search"
                    title={dictionary.noSearchResultsFound}
                    description={
                      dictionary.pleaseCheckThatAllWordsAreSpelledCorrectly
                    }
                  />
                }
              />
              <span className={styles.divider}></span>
            </WithLoader>
          </>
        ) : null}
        {isParentCategory && !hasFilters ? (
          <Typography as="h5" dictionary={dictionary.allCourses} />
        ) : null}
        <Grid xs={1} sm={3} md={4} gap="lg">
          {searchedList?.length > 0 &&
            searchedList.map((item) => (
              <Course
                key={`${item.courseid || item.id}${item.type ? item.type : ''}`}
                course={item}
              />
            ))}
        </Grid>
      </WithLoader>
      {!isFetching && searchedList?.length <= 0 && (
        <EmptyMessage
          icon="search"
          title={dictionary.noSearchResultsFound}
          description={dictionary.pleaseCheckThatAllWordsAreSpelledCorrectly}
        />
      )}
    </Paper>
  );
};

export default SearchResults;
