@use 'mixins' as *;

.settingsSectionTypography {
  @include forMarginPadding(margin, size(32), size(0), size(32), size(0));
}

.optionListContainer {
  padding-right: 30px;
}

.optionListContainerAr {
  padding-left: 30px;
}

.iconContainer {
  width: 50%;
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin: auto;
  margin-top: 24px;
  margin-bottom: 24px;
  padding-right: 1.875rem;
  .leftIcon {
    width: size(26);
    height: size(26);
    svg {
      fill: var(--grey-900);
      fill-rule: evenodd;
      width: size(26);
      height: size(26);
      vertical-align: top;
    }
  }
}
