import { NextRequest, NextResponse } from 'next/server';
import { ExternalFetchProfileService } from '@/services/external/profile';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';

// To handle a GET request to /api
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const id = request.nextUrl.searchParams.get('userId') || '';
    // Fetching user profile
    const response = await ExternalFetchProfileService(id);
    // Return the response
    return NextResponse.json({
      user: response?.data,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error as AxiosError);
  }
}
