import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraStartLearningService } from '@/services/totara/courses/start-learning';

/**
 * Route to get the announcements
 */
export async function GET() {
  try {
    // Fetching records counts
    const response = await TotaraStartLearningService();

    // Return the response
    return NextResponse.json({
      startLearning: response?.data,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
