import ProgramDetailCourses from '@/screens/program-detail';
import { TotaraFetchProgramDetailService } from '@/services/totara/programs';
import { Metadata } from 'next';

type Props = {
  params: Promise<{ slug: string; locale: string }>;
};

export const generateMetadata = async ({
  params,
}: Props): Promise<Metadata> => {
  const { slug: programId, locale } = await params;

  try {
    const response = await TotaraFetchProgramDetailService(programId);

    if (response.data.exception) {
      throw new Error(response.data.message);
    }

    return {
      title: response.data.program_name,
      description: response.data.program_description,
      openGraph: {
        title: response.data.program_name,
        description: response.data.program_description,
        images: [
          {
            url: response.data.program_image,
            alt: response.data.program_name,
          },
        ],
        url: `${process.env.BASE_URL}/${locale}/program/${programId}`,
        type: 'website',
      },
      twitter: {
        title: response.data.program_name,
        description: response.data.program_description,
        images: [{ url: response.data.program_image }],
      },
    };
  } catch (err) {
    console.log(err);
    return {};
  }
};

export default function ProgramDetailPage() {
  return <ProgramDetailCourses />;
}
