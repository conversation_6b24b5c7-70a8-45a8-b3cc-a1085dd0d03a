@use 'mixins' as *;

.badgeList {
  position: relative;
  min-height: size(243);
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: size(16);

  div[class*='wrapper'] {
    > div[class*='image'] {
      img {
        object-fit: contain;
      }
    }

    > p[class*='description'] {
      max-width: size(290);
    }
  }
}

.isEmpty {
  display: block;
}

@include for-all-phone() {
  .wrapper {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }

  .badgeList {
    grid-template-columns: repeat(2, 1fr);
    grid-gap: size(16);
  }
}
