import { ErrorResponse } from '@/utils/fetcher';
import { NextRequest, NextResponse } from 'next/server';
import { AxiosError } from 'axios';
import { TotaraMultisessionBookASlotService } from '@/services/totara/courses/multisession';

export async function POST(request: NextRequest) {
  try {
    // Getting request body
    const { seminarId, courseId } = await request.json();

    const response = await TotaraMultisessionBookASlotService(
      seminarId,
      courseId,
    );
    // Return the response

    return NextResponse.json({
      bookingResponse: response?.data,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error as AxiosError);
  }
}
