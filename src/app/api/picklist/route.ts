import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';
import { ExternalFetchPicklistService } from '@/services/external/picklist';
import IPicklistItem from '@/types/domain/external/picklist/item';

/**
 * Route to get the learning types
 */
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const id = request.nextUrl.searchParams.get('id') || '';
    // Fetching the picklist
    const response = await ExternalFetchPicklistService(id);
    // Getting entries
    const entries = response?.data?.items?.[0]?.listTypeEntries || [];
    // Mapping the entries into required data
    const list =
      entries?.map((entry: IPicklistItem) => ({
        id: entry?.id,
        key: entry?.key,
        name: entry?.name,
      })) || [];
    // Return the response
    return NextResponse.json({ list });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error as AxiosError);
  }
}
