@use 'mixins' as *;

.prerequisites {
  @include forMarginPadding(padding, size(30), size(0), size(30), size(0));
  border-bottom: solid 1px var(--grey-400);
}

.list {
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  align-items: center;
  flex-wrap: nowrap;

  li {
    list-style: none;
    display: flex;
    align-items: center;

    i {
      width: size(40);
      height: size(40);
      background: var(--oat-milk-600);
      display: flex;
      justify-content: center;
      align-items: center;
      @include borderRadius(50%);
      @include forMarginPadding(margin, size(0), size(20), size(0), size(0));

      svg {
        width: size(14);
        height: size(14);
        fill: var(--grey-900);
      }
    }

    p {
      @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    }
  }
}

@include for-all-phone() {
  .list {
    display: block;
    li {
      + li {
        @include forMarginPadding(margin, size(16), size(0), size(0), size(0));
      }
    }
  }
}

@include for-dark-theme {
  .list {
    li {
      i {
        background: var(--oat-milk-700);

        svg {
          width: size(14);
          height: size(14);
          fill: var(--grey-300);
        }
      }
    }
  }
}
