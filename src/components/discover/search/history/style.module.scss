@use 'mixins' as *;

.searchHistory {
  @include forMarginPadding(margin, size(40), size(0), size(0), size(0));
}

.sep {
  border-bottom: solid 1px var(--grey-500);
  height: 1px;
  width: 100%;
  display: block;
  @include forMarginPadding(margin, size(30), size(0), size(30), size(0));
}

.popularSearch {
  ul {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
    display: flex;
    flex-wrap: wrap;

    li {
      list-style: none;
      display: flex;
      justify-content: center;
      @include forMarginPadding(margin, size(0), size(10), size(10), size(0));

      button {
        @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
      }
    }
  }
}

.clearBtn {
  background: none;
  border: none;
  color: var(--grey-900);
  font-size: size(16);
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: size(16);

  i {
    width: size(22);
    height: size(22);
    display: block;
    @include forMarginPadding(margin, size(0), size(5), size(0), size(0));

    svg {
      fill: var(--grey-700);
      width: 100%;
      height: 100%;
      fill-rule: evenodd;
    }
  }

  span {
    display: inline;
    padding-bottom: 0;
    transition: all 0.5s linear;
    background: linear-gradient(
      to bottom,
      var(--grey-900) 0%,
      var(--grey-900) 98%
    );
    background-size: 0 1px;
    background-repeat: no-repeat;
    background-position: left 100%;

    @include rtl {
      background-position: right 100%;
    }
  }
}

@include hover {
  .clearBtn {
    &:hover {
      span {
        background-size: 100% 1px;
      }
    }
  }
}

@include for-all-phone() {
  .searchHistory {
    position: initial;
    max-height: initial;
    overflow-y: initial;
    background: none;
    @include forMarginPadding(padding, size(20), size(0), size(20), size(0));
  }
}
