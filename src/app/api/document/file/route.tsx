import { getUserToken } from '@/utils/auth';
import { NextRequest, NextResponse } from 'next/server';

// To handle a GET request to /api
export async function GET(request: NextRequest) {
  //Getting response properties
  const url = request.nextUrl.searchParams.get('url') || '';
  // Getting user token
  const token = await getUserToken();

  try {
    // Getting file form liferay
    const file = await fetch(url, {
      headers: {
        Authorization: token,
      },
    });
    // Getting file content
    const blob = await file.blob();
    //Returning response
    return new NextResponse(blob, {
      status: 200,
      statusText: 'ok',
      headers: {
        'content-type': file.headers.get('content-type') || '',
        'content-description': file.headers.get('content-description') || '',
      },
    });
  } catch (e) {
    console.log(e);
  }

  // Do whatever you want
  return NextResponse.json({ src: '' });
}
