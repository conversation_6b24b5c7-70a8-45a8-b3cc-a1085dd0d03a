import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraAddCoursesToPersonalListService } from '@/services/totara/personal-lists';

export async function POST(request: NextRequest) {
  const { listId, courseIds } = await request.json();

  try {
    const response = await TotaraAddCoursesToPersonalListService(
      listId,
      courseIds,
    );

    return NextResponse.json({
      addCourses: response?.data || null,
    });
  } catch (error) {
    return ErrorResponse(error);
  }
}
