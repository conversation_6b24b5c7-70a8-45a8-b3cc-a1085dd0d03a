<svg width="1675" height="1265" viewBox="0 0 1675 1265" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_354_1076)">
<ellipse cx="390.449" cy="598.119" rx="390.449" ry="598.119" transform="matrix(-0.0263977 0.999651 -0.998184 -0.0602449 1445.08 277.891)" fill="url(#paint0_linear_354_1076)"/>
</g>
<defs>
<filter id="filter0_f_354_1076" x="0.616211" y="0.171509" width="1674.25" height="1264" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="120" result="effect1_foregroundBlur_354_1076"/>
</filter>
<linearGradient id="paint0_linear_354_1076" x1="389.16" y1="649.438" x2="-32.3896" y2="1169.72" gradientUnits="userSpaceOnUse">
<stop stop-color="#7DA1C4"/>
<stop offset="1" stop-color="#D5C8BE"/>
</linearGradient>
</defs>
</svg>
