import { FC } from 'react';
import { motion } from 'motion/react';
import Media from '@/ui/media';
import Typography from '@/ui/typography';
import BackOverly from '@/ui/back-overly';
import BackButton from '@/ui/back-button';
import <PERSON>Field from './field';
import History from './history';

// Styles
import styles from './style.module.scss';
import dictionaries from '@/dictionaries';

interface IPopup {
  onClose?: () => void;
}

const Popup: FC<IPopup> = (props) => {
  const { onClose } = props;

  // Return JSX
  return (
    <motion.div
      initial={{ opacity: 0, zIndex: 9999 }}
      animate={{ opacity: 1 }}
      className={styles.popup}
    >
      <Media mobile={false}>
        <BackOverly onClick={onClose} />
      </Media>
      <div className={styles.discover}>
        <div className={styles.head}>
          <Media mobile={true} tablet={false} desktop={false}>
            <BackButton onClick={onClose} />
          </Media>
          <Typography as="h2" dictionary={dictionaries.Discover} />
        </div>
        <SearchField closePopup={onClose} />
        <History closePopup={onClose} />
      </div>
    </motion.div>
  );
};

export default Popup;
