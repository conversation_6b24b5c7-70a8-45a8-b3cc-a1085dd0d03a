import { FC, useState } from 'react';
import Trigger from './trigger';
import Chat from './chat';
import Toast from '@/ui/toast';
import useChatStore from '@/stores/ai/chat';
import useCompletionStore from '@/stores/ai/completion';
import useGenerativeStore from '@/stores/ai/generative';
import dictionary from '@/dictionaries';

interface IAssistant {
  id?: string | undefined;
}

const Assistant: FC<IAssistant> = () => {
  const [isChatActive, setIsChatActive] = useState<boolean>(false);
  const hasChatError = useChatStore((x) => x.hasChatError);
  const hasCompletionError = useCompletionStore((x) => x.hasCompletionError);
  const hasGenerativeError = useGenerativeStore((x) => x.hasGenerativeError);

  const handleActivateChat = (isActive: boolean) => {
    setIsChatActive(isActive);
  };

  return (
    <>
      <Trigger onActivateChat={handleActivateChat} />
      {isChatActive ? <Chat onCloseChat={handleActivateChat} /> : null}
      {isChatActive &&
      (hasChatError || hasCompletionError || hasGenerativeError) ? (
        <Toast
          text={dictionary.thereIsAnErrorWithContentGeneration}
          type="error"
        />
      ) : null}
    </>
  );
};

export default Assistant;
