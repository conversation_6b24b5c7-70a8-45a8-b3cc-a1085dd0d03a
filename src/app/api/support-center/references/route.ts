import { getReferences } from '@/services/external/support-center';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const response = await getReferences();

    const data = response.data;

    const formattedResponse = {
      references: data?.data || data?.references || data,
    };

    return NextResponse.json(formattedResponse);
  } catch (error) {
    console.log(error);
    return NextResponse.json(
      { error: 'Failed to fetch references' },
      { status: 500 },
    );
  }
}
