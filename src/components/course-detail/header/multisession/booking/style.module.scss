@use 'mixins' as *;

.footer {
  width: 100%;
  z-index: 9;
  background: var(--white);
  @include forMarginPadding(padding, size(24), size(24), size(24), size(0));

  button.innerBookCta {
    width: 100%;
  }
}

.head {
  @include forMarginPadding(padding, size(12), size(24), size(12), size(24));
  background: var(--white);
  width: 100%;
  height: 7vh;
  display: flex;
}

.backDetail {
  display: flex;
  align-items: center;
  font-size: size(40);
  line-height: 1;
  background: none;
  border: none;
  cursor: pointer;

  i {
    width: size(26);
    height: size(26);
    @include forMarginPadding(margin, size(0), size(10), size(0), size(0));

    svg {
      vertical-align: top;
    }
  }
}

@include for-dark-theme() {
  .innerBookCta:disabled {
    background-color: var(--grey-700) !important;
    color: var(--disabled) !important;
  }
}

.sessionslist {
  ul {
    @include forMarginPadding(margin, size(0), size(0), size(10), size(0));
    @include forMarginPadding(padding, size(0), size(0), size(110), size(0));
    position: relative;
  }
}

.selectedDetailsContainer {
  @include forMarginPadding(padding, size(20), size(16), size(20), size(36));
  background: var(--warning-500);
  @include borderRadius(var(--radius));

  display: flex;
}

@include for-all-phone() {
  .left,
  .data {
    h5 {
      font-size: size(20);
    }
  }

  .backDetail {
    font-size: size(24);

    i {
      width: size(16);
      height: size(16);
    }
  }

  .footer {
    button {
      font-size: size(18);
      height: size(56);
      text-align: center;
    }
  }

  .selectedDetailsContainer {
    width: 90%;
    height: size(100);
  }
}

.sessionlist {
  height: 100%;
  max-height: calc(100% - size(222));
  ul {
    width: 100%;
    @include forMarginPadding(padding, size(0), size(40), size(0), size(0));
    overflow-y: auto;
    height: calc(100vh - size(288));
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    gap: size(8);

    @include for-all-phone() {
      height: calc(100vh - size(179));
    }

    @media (min-width: 576px) {
      &::-webkit-scrollbar-track {
        background-color: var(--white);
      }

      &::-webkit-scrollbar {
        width: size(8);
        background-color: transparent;
      }

      &::-webkit-scrollbar-thumb {
        border-radius: size(8);
        background-color: var(--grey-900);
      }
    }
  }
}

.sessionHeader {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  width: 100%;

  .radioBtn {
    width: size(20);
    min-width: size(20);
    min-height: size(20);
    height: size(20);
    align-self: anchor-center;
    @include leftToRight(calc(22% - size(7)));
    top: size(30);
    @include borderRadius(50%);
    background: var(--white);
    border: size(2) solid var(--grey-800);

    content: '';
  }

  .selected {
    background-color: var(--grey-900);
    border: size(2) solid var(--white);
    box-shadow: 0 0 0 size(2) var(--grey-900);
  }

  .sessionInfo {
    display: flex;
    flex-direction: column;
    gap: size(10);
    @include forMarginPadding(padding, size(0), size(0), size(0), size(24));
    width: 100%;

    .sessionTitle span {
      display: block;
      font-size: size(18);
      font-weight: 700;
      color: var(--grey-900);
      line-height: 1;
    }
    .sessionDate span {
      display: block;
      font-size: size(16);
      font-weight: 400;
      color: var(--grey-800);
      line-height: 1;
    }
  }

  .collapseIcon {
    align-items: center;
    background-color: transparent;
    border-radius: 50%;
    border: 1px solid var(--disabled-border);
    cursor: pointer;
    display: flex;
    justify-content: center;
    height: size(34);
    width: size(34);
    min-height: size(34);
    min-width: size(34);

    svg {
      fill: var(--grey-900);
      height: size(14);
      width: size(14);
    }

    &.open {
      transform: rotate(180deg);
    }
  }
}

.sessionDetails {
  @include forMarginPadding(margin, size(40), size(0), size(0), size(0));

  .sessionDetailsHeader {
    @include forMarginPadding(padding, size(0), size(0), size(24), size(0));
    span {
      font-size: size(14);
      font-weight: 400;
      color: var(--grey-900);
      text-transform: uppercase;
    }
  }

  .moduleSetion {
    @include forMarginPadding(padding, size(24), size(0), size(24), size(0));
    border-top: 2px solid var(--grey-400);
    .moduleTitle span {
      font-size: size(20);
      font-weight: 700;
      color: var(--grey-900);
      display: block;
      &::first-letter {
        text-transform: uppercase;
      }
    }
  }

  .modulesWrapper {
    width: 100%;
    overflow-y: auto;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    gap: 0.5rem;

    .module {
      list-style-type: none !important;
      border-radius: size(8);
      background-color: var(--bg-shade-5);
      display: flex;
      flex-direction: column;
      gap: size(12);
      @include forMarginPadding(
        padding,
        size(16),
        size(21),
        size(19),
        size(21)
      );

      .moduleTitle {
        font-size: size(16);
        color: var(--grey-900);
        line-height: 1;
      }

      .eventInfo {
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        gap: size(8);

        .eventTimeDate {
          display: flex;
          flex-direction: row;
          flex-wrap: nowrap;
          align-items: center;
          gap: size(16);
        }
      }

      .time,
      .date,
      .location {
        font-size: size(16);
        font-weight: 400;
        color: var(--grey-800);
        line-height: 1;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        align-items: flex-end;
        gap: size(4);
        svg {
          width: size(16);
          height: size(16);
          fill: var(--grey-800);
        }
      }

      .time path,
      .date path {
        stroke: var(--grey-800);
      }
    }
  }
}

.session {
  border: 2px solid var(--disabled-border);
  border-radius: size(12);
  @include forMarginPadding(padding, size(24), size(16), size(24), size(24));
  list-style: none !important;

  &.selectedSession {
    border: 2px solid var(--grey-900);
  }

  &.rejected {
    .radioBtn {
      border-color: var(--grey-700);
    }
    .sessionTitle span,
    .sessionDetailsHeader span,
    .moduleTitle span {
      color: var(--grey-700);
    }
    .sessionDate span,
    .module .eventInfo span,
    .module .eventTimeDate span {
      color: var(--grey-600);
    }
    .module .eventInfo svg,
    .module .eventTimeDate svg {
      fill: var(--grey-600);
    }

    .module .eventTimeDate path {
      stroke: var(--grey-600);
    }
  }

  &.disabled {
    opacity: 0.5;
    pointer-events: none;
    cursor: not-allowed;
  }
}

@include for-dark-theme() {
  .radionNotSelected {
    border: solid 2px var(--grey-800);
  }

  .radioSelected {
    background-color: var(--green-900);
    box-shadow: 0 0 0 2px var(--green-900);
  }

  .selectedDetailsContainer {
    background-color: var(--cardBg);
  }
}
