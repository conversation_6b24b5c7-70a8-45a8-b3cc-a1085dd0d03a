import { useEffect } from 'react';
import Grid from '@/ui/grid';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '../loading';

// Styles
import styles from '../style.module.scss';
import Course from '@/components/course';
import useCompletedCoursesStore from '@/stores/learning/courses/completed';
import EmptyMessage from '@/ui/empty-message';

const Completed = () => {
  // Getting Method to fetch courses
  const isFetching = useCompletedCoursesStore((x) => x.isFetching);
  const onFetch = useCompletedCoursesStore((x) => x.fetch);
  const list = useCompletedCoursesStore((x) => x.list);

  // Effect to fetch
  useEffect(() => {
    if (list?.length === 0) {
      onFetch();
    }
  }, [list, onFetch]);

  // Return JSX
  return (
    <div className={styles.tabBody}>
      <WithLoader loader={<Loading />} loading={isFetching}>
        <Grid xs={1} sm={3} md={4} gap="lg">
          {list.map((item, index) => (
            <Course key={index} course={item} />
          ))}
        </Grid>
        {!isFetching && list.length === 0 && (
          <EmptyMessage
            icon="completed"
            title="No Completed Courses"
            description="There are no new recommendations at this time. Please check back later."
          />
        )}
      </WithLoader>
    </div>
  );
};

export default Completed;
