import { FC, useCallback, useMemo, useState } from 'react';
import styles from './style.module.scss';
import Typography from '@/ui/typography';
import { useLocale, useTranslations } from 'next-intl';
import moment from 'moment';
import {
  AvailableSlotMultiday,
  IMultidayModule,
  IMultidaySession,
} from '@/types/domain/learning/course';
import IMultisessionCourseStore from '@/stores/learning/courses/multisession';
import { ChevronDown, Pin, CalendarTimeSlot, ClockFrame } from '@/ui/icons';
import dictionary from '@/dictionaries';
import momentTimeZone from 'moment-timezone';

interface ISlot {
  session: AvailableSlotMultiday;
}

const Slot: FC<ISlot> = ({ session }) => {
  const [isSessionOpen, setIsSessionOpen] = useState(false);
  const isBookingRejected = session?.booked.toLowerCase() === 'rejected';
  const selectedSessionFromStore = IMultisessionCourseStore((x) => x.selected);
  const setSelectedSessionInStore = IMultisessionCourseStore(
    (x) => x.setSelected,
  );
  const locale = useLocale();
  const t = useTranslations();

  const isSelected = useMemo(
    () => selectedSessionFromStore?.id === session?.id,
    [selectedSessionFromStore, session?.id],
  );

  const formatLocalTime = (dateString: string | undefined) => {
    const dateTime = dateString?.split('T');
    const date = dateTime?.[0];
    const time = dateTime?.[1]?.split('.')?.[0];
    const dubaiLocalDate = momentTimeZone.tz(`${date}T${time}`, 'Asia/Dubai');
    const utcTime = dubaiLocalDate.clone().tz('UTC');
    const systemDefaultTz = utcTime.clone().tz(momentTimeZone.tz.guess());

    return systemDefaultTz;
  };

  const isStartDateInPast = session.events.some((event) => {
    const currentDate = moment().locale(locale);
    return formatLocalTime(event.startdate)
      .locale(locale)
      .isBefore(currentDate);
  });

  const handleToggleOpen = useCallback(() => {
    setIsSessionOpen((prevIsOpen) => !prevIsOpen);
  }, []);

  const handleSelectSession = useCallback(() => {
    if (isStartDateInPast) return;
    if (session && !isBookingRejected) {
      setSelectedSessionInStore(session);
    }
  }, [
    session,
    setSelectedSessionInStore,
    isBookingRejected,
    isStartDateInPast,
  ]);

  const formattedSessionDate = useMemo(() => {
    if (!session?.seminar_date) {
      return '';
    }

    let outputLocale = 'en'; // Default to English
    // Use locale if it's 'ar' or 'en', ignoring case and regional codes for this basic check
    if (locale && ['ar', 'en'].includes(locale.toLowerCase().substring(0, 2))) {
      outputLocale = locale.toLowerCase().substring(0, 2);
    }

    const [startDateString, endDateString] = session.seminar_date.split(' - ');

    if (!startDateString || !endDateString) {
      console.warn(
        'Could not split seminar_date into start and end dates:',
        session.seminar_date,
      );
      return session.seminar_date; // Fallback
    }

    const inputFormat = 'DD MMM YY';
    const parsingLocale = 'en'; // Default parsing locale

    const startMoment = moment(
      startDateString.trim(),
      inputFormat,
      parsingLocale,
    );
    const endMoment = moment(endDateString.trim(), inputFormat, parsingLocale);

    if (!startMoment.isValid() || !endMoment.isValid()) {
      console.warn(
        'Invalid date format for session parts. Original:',
        session.seminar_date,
        'Start part:',
        startDateString,
        'End part:',
        endDateString,
      );
      return session.seminar_date; // Fallback to original string
    }

    const outputFormat = 'D MMM';

    const formattedStart = startMoment
      .locale(outputLocale)
      .format(outputFormat);
    const formattedEnd = endMoment.locale(outputLocale).format(outputFormat);

    return `${formattedStart} - ${formattedEnd}`;
  }, [session?.seminar_date, locale]);

  return (
    <li
      className={`${styles.session} ${isSelected ? styles.selectedSession : ''} ${isBookingRejected ? styles.rejected : ''} ${isStartDateInPast ? styles.disabled : ''}`}
      aria-labelledby={`session-title-${session?.id}`}
    >
      <div className={styles.sessionHeader}>
        <div
          className={`${styles.radioBtn} ${isSelected ? styles.selected : ''}`}
          onClick={handleSelectSession}
          role="radio"
          aria-checked={isSelected}
          tabIndex={isStartDateInPast ? -1 : 0}
          onKeyDown={(e) =>
            !isStartDateInPast &&
            (e.key === 'Enter' || e.key === ' ') &&
            handleSelectSession()
          }
        ></div>
        <div className={styles.sessionInfo}>
          <div className={styles.sessionTitle}>
            <Typography as="span" id={`session-title-${session?.id}`}>
              {session?.name}
            </Typography>
          </div>
          <div className={styles.sessionDate}>
            <Typography as="span">{formattedSessionDate}</Typography>
          </div>
        </div>
        <div
          className={`${styles.collapseIcon} ${isSessionOpen ? styles.open : ''}`}
          onClick={handleToggleOpen}
          role="button"
          tabIndex={0}
          aria-expanded={isSessionOpen}
          aria-controls={`session-details-${session?.id}`}
          onKeyDown={(e) =>
            (e.key === 'Enter' || e.key === ' ') && handleToggleOpen()
          }
        >
          <ChevronDown />
        </div>
      </div>

      {isSessionOpen && (
        <div
          className={styles.sessionDetails}
          id={`session-details-${session?.id}`}
          role="region"
          aria-labelledby={`session-details-heading-${session?.id}`}
        >
          <div className={styles.sessionDetailsHeader}>
            <Typography
              as="span"
              id={`session-details-heading-${session?.id}`}
              dictionary={dictionary.courseDetails}
            ></Typography>
          </div>
          <div className={styles.sessionDetailsBody}>
            {session?.events?.map((module: IMultidayModule, i) => (
              <section
                key={module.id}
                className={styles.moduleSetion}
                aria-labelledby={`module-title-${module.id}`}
              >
                <Typography
                  as="h4"
                  className={styles.moduleTitle}
                  id={`module-title-${module.id}`}
                >
                  {t(dictionary.module)} {i + 1}
                </Typography>
                <ul className={styles.modulesWrapper}>
                  {module?.sessions?.map((session: IMultidaySession) => (
                    <li key={session.id} className={styles.module}>
                      <Typography as="strong" className={styles.moduleTitle}>
                        {t(dictionary.session)} {i + 1}
                      </Typography>
                      <div className={styles.eventInfo}>
                        <div className={styles.eventTimeDate}>
                          <Typography as="span" className={styles.time}>
                            <ClockFrame aria-hidden="true" />
                            {`${formatLocalTime(session.startdatetime)
                              .locale(locale)
                              .format('h:mm a')} - ${formatLocalTime(
                              session.enddatetime,
                            )
                              .locale(locale)
                              .format('h:mm a')} `}
                          </Typography>
                          <Typography as="span" className={styles.date}>
                            <CalendarTimeSlot aria-hidden="true" />
                            {moment(session.startdatetime)
                              .locale(locale === 'ar' ? 'ar' : 'en')
                              .format(' DD MMM YYYY')}
                          </Typography>
                        </div>
                        {session.location?.room && (
                          <Typography as="span" className={styles.location}>
                            <Pin aria-hidden="true" />
                            {session.location.room}
                          </Typography>
                        )}
                      </div>
                    </li>
                  ))}
                </ul>
              </section>
            ))}
          </div>
        </div>
      )}
    </li>
  );
};

export default Slot;
