@use 'mixins' as *;

.reviewSubmitPopup {
  background-color: var(--white);
  display: none;
}

.reviewSubmitPopupOpened {
  border: 0;
  border-radius: size(24);
  display: flex;
  flex-direction: column;
  margin: auto;
  min-height: size(262);
  @include forMarginPadding(padding, size(40), size(24), size(32), size(24));
  width: size(430);
}

.reviewSubmitPopupTitle {
  font-size: size(28);
  font-weight: 700;
}

.reviewSubmitPopupActions {
  display: flex;
  gap: size(8);
  margin-top: auto;
}

.reviewSubmitPopupCancel {
  background-color: transparent;
  color: var(--grey-900);
}
