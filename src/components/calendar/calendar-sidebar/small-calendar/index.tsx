import { FC, useCallback } from 'react';
import styles from './style.module.scss';
import { useLocalizedMonths } from '@/components/calendar/constants';
import IFormatedEvent from '@/types/domain/calendar/formated-event';
import useCalendarView from '@/hooks/calendar/useCalendarView';
import { ArrowDownFrame } from '@/ui/icons';
import { useLocale } from 'next-intl';

type TimePeriod = 'daily' | 'weekly' | 'monthly';

interface ISmallCalendar {
  isMyCalendarTab?: boolean;
  events: IFormatedEvent[];
  selectedTimePeriod: TimePeriod;
  onTimePeriodChange: (newPeriod: TimePeriod) => void;
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
}

const SmallCalendar: FC<ISmallCalendar> = ({
  events,
  selectedTimePeriod,

  selectedDate,
  onDateSelect,
}) => {
  const month = selectedDate.getMonth();
  const year = selectedDate.getFullYear();
  const day = selectedDate.getDate();

  const localizedMonths = useLocalizedMonths();
  const localizedMonthName = localizedMonths[month];
  const locale = useLocale();
  const mainCalendarContent = useCalendarView({
    month: month,
    year: year,
    day: day,
    selectedDate: selectedDate,
    events: events,
    type: 'small',
    onDateSelect: onDateSelect,
    activePeriod: selectedTimePeriod,
  });

  const handlePrevClick = useCallback(() => {
    const newDate = new Date(selectedDate);
    newDate.setMonth(newDate.getMonth() - 1);
    onDateSelect(newDate);
  }, [selectedDate, onDateSelect]);

  const handleNextClick = useCallback(() => {
    const newDate = new Date(selectedDate);
    newDate.setMonth(newDate.getMonth() + 1);
    onDateSelect(newDate);
  }, [selectedDate, onDateSelect]);

  return (
    <div className={`${styles.smallCalendar}`}>
      <div className={styles.topBar}>
        <div className={styles.dateDisplay}>
          {localizedMonthName} {year}
        </div>
        <div className={styles.navButtons}>
          <button
            className={`${styles.navButton} ${styles.navButtonPrev} ${locale == 'ar' ? styles.rtl : ''}`}
            onClick={handlePrevClick}
          >
            <ArrowDownFrame />
          </button>
          <button
            className={`${styles.navButton} ${styles.navButtonNext} ${locale == 'ar' ? styles.rtl : ''}`}
            onClick={handleNextClick}
          >
            <ArrowDownFrame />
          </button>
        </div>
      </div>
      <div className={styles.calendar}>{mainCalendarContent}</div>
    </div>
  );
};

export default SmallCalendar;
