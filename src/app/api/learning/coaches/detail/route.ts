import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchCoachDetailService } from '@/services/totara/coaches';

/**
 * Route to get the coach detail
 */
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const coachId = request.nextUrl.searchParams.get('id') || '';
    // Fetching coaches categories
    const response = await TotaraFetchCoachDetailService(coachId);
    // Return the response
    return NextResponse.json(response.data);
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
