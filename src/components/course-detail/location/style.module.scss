@use 'mixins' as *;

.item {
  border-radius: size(12);
  padding: size(10);
  margin-bottom: size(16);
  max-width: size(336);
  border: 1px solid var(--grey-500);

  .slotName {
    font-size: size(18);
    font-weight: 700;
    margin-bottom: size(8);
  }

  .row {
    display: flex;
    align-items: center;
    margin-bottom: size(8);
    font-size: size(16);

    & svg {
      fill: var(--grey-900);
      width: size(20);
      height: size(20);
      @include forMarginPadding(margin, size(0), size(8), size(0), size(0));
      flex: none;
    }
  }
}

.cohortName {
  font-size: size(20);
}

.event {
  border-radius: size(12);
  padding: size(20);
  margin-bottom: size(20);
  border: 1px solid var(--grey-500);
  max-width: size(600);

  .eventName {
    display: block;
    margin-bottom: size(16);
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .collapseIcon {
      align-items: center;
      background-color: transparent;
      border-radius: 50%;
      border: 1px solid var(--disabled-border);
      cursor: pointer;
      display: flex;
      justify-content: center;
      height: size(34);
      width: size(34);
      min-height: size(34);
      min-width: size(34);

      svg {
        fill: var(--grey-900);
        height: size(14);
        width: size(14);
      }

      &.open {
        transform: rotate(180deg);
      }
    }
  }
}
