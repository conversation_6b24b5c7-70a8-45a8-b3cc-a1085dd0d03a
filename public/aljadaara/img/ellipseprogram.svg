<svg width="1301" height="1311" viewBox="0 0 1301 1311" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.5" clip-path="url(#clip0_354_609)">
<g filter="url(#filter0_f_354_609)">
<path d="M192.939 532.85C167.555 653.765 328.324 760.715 552.026 771.728C775.728 782.741 529.418 671.581 554.803 550.665C580.187 429.749 867.652 344.867 643.95 333.854C420.248 322.84 218.323 411.934 192.939 532.85Z" fill="url(#paint0_linear_354_609)"/>
</g>
<g filter="url(#filter1_f_354_609)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M934.929 438.655C957.04 518.149 945.445 609.746 876.276 654.732C806.993 699.791 719.4 672.248 654.564 620.996C586.709 567.358 531.318 486.835 561.19 405.662C592.04 321.83 686.509 284.538 775.491 292.394C854.917 299.406 913.562 361.837 934.929 438.655Z" fill="url(#paint1_linear_354_609)"/>
</g>
<g filter="url(#filter2_f_354_609)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M939.329 732.299C946.946 779.055 982.364 823.918 967.932 869.037C953.51 914.122 901.807 933.504 865.324 963.657C832.269 990.976 800.567 1017.2 763.205 1038.24C715.64 1065.03 666.398 1125.53 616.247 1103.96C562.847 1080.99 584.795 992.134 549.186 946.18C520.137 908.693 458.522 908.057 433.315 867.885C405.956 824.283 400.373 769.637 406.327 718.508C412.761 663.251 420.366 594.723 468.391 566.661C519.062 537.053 583.669 594.835 640.819 581.491C695.007 568.839 724.005 495.234 779.636 493.989C834.636 492.758 889.438 529.532 920.067 575.24C949.938 619.816 930.701 679.338 939.329 732.299Z" fill="url(#paint2_linear_354_609)"/>
</g>
</g>
<defs>
<filter id="filter0_f_354_609" x="82.6718" y="225.302" width="753.576" height="654.812" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="53.8155" result="effect1_foregroundBlur_354_609"/>
</filter>
<filter id="filter1_f_354_609" x="433.308" y="171.78" width="631.367" height="625.27" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="59.795" result="effect1_foregroundBlur_354_609"/>
</filter>
<filter id="filter2_f_354_609" x="273.821" y="363.606" width="827.885" height="875.268" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="65.1765" result="effect1_foregroundBlur_354_609"/>
</filter>
<linearGradient id="paint0_linear_354_609" x1="486.097" y1="326.082" x2="464.369" y2="767.413" gradientUnits="userSpaceOnUse">
<stop stop-color="#7DA1C4"/>
<stop offset="1" stop-color="#8C958F"/>
</linearGradient>
<linearGradient id="paint1_linear_354_609" x1="519.219" y1="344.658" x2="817.071" y2="717.369" gradientUnits="userSpaceOnUse">
<stop offset="1" stop-color="#7DA1C4"/>
</linearGradient>
<linearGradient id="paint2_linear_354_609" x1="553.107" y1="276.882" x2="164.903" y2="931.553" gradientUnits="userSpaceOnUse">
<stop stop-color="#B4AFA5"/>
<stop offset="1" stop-color="#D5C8BE"/>
</linearGradient>
<clipPath id="clip0_354_609">
<rect width="1073.32" height="1058.37" fill="white" transform="translate(1022.31) rotate(75)"/>
</clipPath>
</defs>
</svg>
