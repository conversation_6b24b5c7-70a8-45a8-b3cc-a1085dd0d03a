import { FC } from 'react';
import { StaticImageData } from 'next/image';
import Typography from '@/ui/typography';

import styles from '../style.module.scss';
import Link from 'next/link';

interface IItem {
  image?: string | StaticImageData;
  description?: string;
  link?: string;
}

const Item: FC<IItem> = (props) => {
  const { description, link } = props;

  return (
    <div className={styles.card}>
      <Link className={styles.certLink} href={link as string} target="_blank">
        {description && (
          <div className={styles.data}>
            <Typography as="span" className={styles.text}>
              {description}
            </Typography>
          </div>
        )}
      </Link>
    </div>
  );
};

export default Item;
