name: WebViewFrontend-CI
trigger:
  branches:
    include:
      - main
      - staging
      - develop
      - release/*
      - hotfix/*
      - azure-*
      - feat/*
  paths:
    exclude:
      - pipeline/**
stages:
  - stage: DevBuild
    condition: eq(variables['Build.SourceBranchName'], 'develop')
    displayName: Dev Build Stage
    jobs:
      - job: WebViewBuild
        displayName: WebView Build
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: Bash@3
            displayName: 'Get Branch name'
            inputs:
              targetType: inline
              script: |
                echo "##vso[task.setvariable variable=BRANCH;]$(Build.SourceBranchName)"
          - task: Bash@3
            displayName: Get short git hash
            inputs:
              targetType: inline
              script: |
                BUILD_HASH=`git rev-parse --short=7 HEAD`
                echo ""
                echo "Full git hash:  $(Build.SourceVersion)"
                echo "Short git hash: $BUILD_HASH"
                echo "##vso[task.setvariable variable=BUILD_HASH;]$BUILD_HASH"
          - task: Bash@3
            displayName: 'Set ENV variable'

            inputs:
              targetType: inline
              script: |
                echo "##vso[task.setvariable variable=NODE_ENV;]production"
                echo "##vso[task.setvariable variable=BASE_URL;]http://localhost:3000"
                echo "##vso[task.setvariable variable=INTERNAL_API;]http://localhost:3000/api"
                echo "##vso[task.setvariable variable=LIFERAY_API;]https://admin.stg.workspace.dge.gov.ae/o"
                echo "##vso[task.setvariable variable=TOTARA_API;]https://adsgstaging.elearning.ae/webservice/rest/server.php"
                echo "##vso[task.setvariable variable=TOTARA_AUTH_BASE_URL;]https://adsgstaging.elearning.ae"
                echo "##vso[task.setvariable variable=UAE_PASS_URL;]https://stg-id.uaepass.ae"
                echo "##vso[task.setvariable variable=UAE_PASS_ID;]sandbox_stage"
                echo "##vso[task.setvariable variable=UAE_PASS_STATE;]ShNP22hyl1jUU2RGjTRkpg=="
                echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_API_KEY;]AIzaSyCmrM6RqX6P-t0saUx9lsN3LWXbWapwW5g"
                echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_AUTH_DOMAIN;]tomouh-web-development.firebaseapp.com"
                echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_PROJECT_ID;]tomouh-web-development"
                echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_STORAGE_BUCKET;]tomouh-web-development.firebasestorage.app"
                echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_MESSAGING_SENDER_ID;]779958795170"
                echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_APP_ID;]1:779958795170:web:ec7bfe44ef94f455c3d0fb"
                echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_MEASUREMENT_ID;]G-M3YHKZ0F95"
          - task: Bash@3
            displayName: 'Set Docker build arguments to env file'
            inputs:
              targetType: inline
              script: |
                echo "The branch name is $(BRANCH)"  
                echo "BUILD_HASH=$(BUILD_HASH)" >> .env
                echo "NODE_ENV=$(NODE_ENV)" >> .env
                echo "BASE_URL=$(BASE_URL)" >> .env
                echo "INTERNAL_API=$(INTERNAL_API)" >> .env
                echo "LIFERAY_API=$(LIFERAY_API)" >> .env
                echo "TOTARA_API=$(TOTARA_API)" >> .env
                echo "TOTARA_AUTH_BASE_URL=$(TOTARA_AUTH_BASE_URL)" >> .env
                echo "UAE_PASS_URL=$(UAE_PASS_URL)" >> .env
                echo "UAE_PASS_ID=$(UAE_PASS_ID)" >> .env
                echo "UAE_PASS_STATE=$(UAE_PASS_STATE)" >> .env
                echo "NEXT_PUBLIC_FIREBASE_CONFIG_API_KEY=$(NEXT_PUBLIC_FIREBASE_CONFIG_API_KEY)" >> .env
                echo "NEXT_PUBLIC_FIREBASE_CONFIG_AUTH_DOMAIN=$(NEXT_PUBLIC_FIREBASE_CONFIG_AUTH_DOMAIN)" >> .env
                echo "NEXT_PUBLIC_FIREBASE_CONFIG_PROJECT_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_PROJECT_ID)" >> .env
                echo "NEXT_PUBLIC_FIREBASE_CONFIG_STORAGE_BUCKET=$(NEXT_PUBLIC_FIREBASE_CONFIG_STORAGE_BUCKET)" >> .env
                echo "NEXT_PUBLIC_FIREBASE_CONFIG_MESSAGING_SENDER_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_MESSAGING_SENDER_ID)" >> .env
                echo "NEXT_PUBLIC_FIREBASE_CONFIG_APP_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_APP_ID)" >> .env
                echo "NEXT_PUBLIC_FIREBASE_CONFIG_MEASUREMENT_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_MEASUREMENT_ID)" >> .env
                cat .env

          - task: Docker@1
            displayName: 'Build an image'
            inputs:
              containerregistrytype: 'Azure Container Registry'
              azureSubscriptionEndpoint: 'SCP-Gov-Academy-WS-PRD'
              azureContainerRegistry: 'acrgovacademywsprduaen.azurecr.io'
              command: 'Build an Image'
              dockerFile: '**/Dockerfile'
              arguments: |
                --build-arg BUILD_HASH=$(BUILD_HASH)
                --build-arg NODE_ENV=$(NODE_ENV)
                --build-arg BASE_URL=$(BASE_URL)
                --build-arg INTERNAL_API=$(INTERNAL_API)
                --build-arg LIFERAY_API=$(LIFERAY_API)
                --build-arg TOTARA_API=$(TOTARA_API)
                --build-arg TOTARA_AUTH_BASE_URL=$(TOTARA_AUTH_BASE_URL)
                --build-arg UAE_PASS_URL=$(UAE_PASS_URL)
                --build-arg UAE_PASS_ID=$(UAE_PASS_ID)
                --build-arg UAE_PASS_STATE=$(UAE_PASS_STATE)
                --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_API_KEY=$(NEXT_PUBLIC_FIREBASE_CONFIG_API_KEY)
                --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_AUTH_DOMAIN=$(NEXT_PUBLIC_FIREBASE_CONFIG_AUTH_DOMAIN)
                --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_PROJECT_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_PROJECT_ID)
                --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_STORAGE_BUCKET=$(NEXT_PUBLIC_FIREBASE_CONFIG_STORAGE_BUCKET)
                --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_MESSAGING_SENDER_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_MESSAGING_SENDER_ID)
                --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_APP_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_APP_ID)
                --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_MEASUREMENT_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_MEASUREMENT_ID)
              imageName: '$(Build.Repository.Name):build-$(Build.SourceBranchName)-$(Build.SourceVersion)'

          - task: Docker@1
            displayName: 'Push image to ACR'
            inputs:
              containerregistrytype: 'Azure Container Registry'
              azureSubscriptionEndpoint: 'SCP-Gov-Academy-WS-PRD'
              azureContainerRegistry: 'acrgovacademywsprduaen.azurecr.io'
              command: 'Push an image'
              imageName: '$(Build.Repository.Name):build-$(Build.SourceBranchName)-$(Build.SourceVersion)'

      - deployment: DevDeploymentJob
        displayName: Deployment Job
        dependsOn: WebViewBuild
        pool: 'SelfHostedAgent-DevAKS'
        environment: development
        strategy:
          runOnce:
            deploy:
              steps:
                - task: Bash@3
                  displayName: 'Deploy to Dev AKS'
                  inputs:
                    targetType: inline
                    script: |
                      echo "Getting current deployed image"
                      kubectl get deployment dge-ga-webview -o=jsonpath='{.spec.template.spec.containers[0].image}'
                      cd /home/<USER>/helmrepo/dge-webview-dev
                      helm upgrade  dge-ga-webview . --set image.tag=build-$(Build.SourceBranchName)-$(Build.SourceVersion)
                      sleep 60
                      echo "Getting latest deployed image"
                      kubectl get deployment dge-ga-webview -o=jsonpath='{.spec.template.spec.containers[0].image}'

  - stage: StageBuild
    condition: eq(variables['Build.SourceBranchName'], 'staging')
    displayName: Stage Build Stage
    jobs:
      - deployment: WebViewStageBuild
        displayName: WebView StageBuild
        pool:
          vmImage: 'ubuntu-latest'
        environment: staging
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - task: Bash@3
                  displayName: 'Get Branch name'
                  inputs:
                    targetType: inline
                    script: |
                      echo "##vso[task.setvariable variable=BRANCH;]$(Build.SourceBranchName)"
                - task: Bash@3
                  displayName: Get short git hash
                  inputs:
                    targetType: inline
                    script: |
                      BUILD_HASH=`git rev-parse --short=7 HEAD`
                      echo ""
                      echo "Full git hash:  $(Build.SourceVersion)"
                      echo "Short git hash: $BUILD_HASH"
                      echo "##vso[task.setvariable variable=BUILD_HASH;]$BUILD_HASH"
                - task: Bash@3
                  displayName: 'Set ENV variable'

                  inputs:
                    targetType: inline
                    script: |
                      echo "##vso[task.setvariable variable=NODE_ENV;]"
                      echo "##vso[task.setvariable variable=BASE_URL;]"
                      echo "##vso[task.setvariable variable=INTERNAL_API;]"
                      echo "##vso[task.setvariable variable=LIFERAY_API;]"
                      echo "##vso[task.setvariable variable=TOTARA_API;]"
                      echo "##vso[task.setvariable variable=TOTARA_AUTH_BASE_URL;]"
                      echo "##vso[task.setvariable variable=UAE_PASS_URL;]"
                      echo "##vso[task.setvariable variable=UAE_PASS_ID;]"
                      echo "##vso[task.setvariable variable=UAE_PASS_STATE;]"
                      echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_API_KEY;]AIzaSyBwlJpxSTl2-iofogeYHiWzXQg0FsiEqac"
                      echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_AUTH_DOMAIN;]tomouh-web-staging.firebaseapp.com"
                      echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_PROJECT_ID;]tomouh-web-staging"
                      echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_STORAGE_BUCKET;]tomouh-web-staging.firebasestorage.app"
                      echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_MESSAGING_SENDER_ID;]823155132568"
                      echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_APP_ID;]1:823155132568:web:98cb771a2e204fe256e8ed"
                      echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_MEASUREMENT_ID;]G-L5QVC6S2VZ"
                - task: Bash@3
                  displayName: 'Set Docker build arguments to env file'
                  inputs:
                    targetType: inline
                    script: |
                      echo "The branch name is $(BRANCH)"
                      echo "BUILD_HASH=$(BUILD_HASH)" >> .env  
                      echo "NODE_ENV=$(NODE_ENV)" >> .env
                      echo "BASE_URL=$(BASE_URL)" >> .env
                      echo "INTERNAL_API=$(INTERNAL_API)" >> .env
                      echo "LIFERAY_API=$(LIFERAY_API)" >> .env
                      echo "TOTARA_API=$(TOTARA_API)" >> .env
                      echo "TOTARA_AUTH_BASE_URL=$(TOTARA_AUTH_BASE_URL)" >> .env
                      echo "UAE_PASS_URL=$(UAE_PASS_URL)" >> .env
                      echo "UAE_PASS_ID=$(UAE_PASS_ID)" >> .env
                      echo "UAE_PASS_STATE=$(UAE_PASS_STATE)" >> .env
                      echo "NEXT_PUBLIC_FIREBASE_CONFIG_API_KEY=$(NEXT_PUBLIC_FIREBASE_CONFIG_API_KEY)" >> .env
                      echo "NEXT_PUBLIC_FIREBASE_CONFIG_AUTH_DOMAIN=$(NEXT_PUBLIC_FIREBASE_CONFIG_AUTH_DOMAIN)" >> .env
                      echo "NEXT_PUBLIC_FIREBASE_CONFIG_PROJECT_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_PROJECT_ID)" >> .env
                      echo "NEXT_PUBLIC_FIREBASE_CONFIG_STORAGE_BUCKET=$(NEXT_PUBLIC_FIREBASE_CONFIG_STORAGE_BUCKET)" >> .env
                      echo "NEXT_PUBLIC_FIREBASE_CONFIG_MESSAGING_SENDER_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_MESSAGING_SENDER_ID)" >> .env
                      echo "NEXT_PUBLIC_FIREBASE_CONFIG_APP_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_APP_ID)" >> .env
                      echo "NEXT_PUBLIC_FIREBASE_CONFIG_MEASUREMENT_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_MEASUREMENT_ID)" >> .env
                      cat .env
                - task: Bash@3
                  displayName: 'Check Dockerfile existence'
                  inputs:
                    targetType: inline
                    script: |
                      ls -la $(Build.SourcesDirectory)
                - task: Docker@1
                  displayName: 'Build an image'
                  inputs:
                    containerregistrytype: 'Azure Container Registry'
                    azureSubscriptionEndpoint: 'SCP-Gov-Academy-WS-PRD'
                    azureContainerRegistry: 'acrgovacademywsprduaen.azurecr.io'
                    command: 'Build an Image'
                    dockerFile: '$(Build.SourcesDirectory)/Dockerfile'
                    arguments: |
                      --build-arg BUILD_HASH=$(BUILD_HASH)
                      --build-arg NODE_ENV=$(NODE_ENV)
                      --build-arg BASE_URL=$(BASE_URL)
                      --build-arg INTERNAL_API=$(INTERNAL_API)
                      --build-arg LIFERAY_API=$(LIFERAY_API)
                      --build-arg TOTARA_API=$(TOTARA_API)
                      --build-arg TOTARA_AUTH_BASE_URL=$(TOTARA_AUTH_BASE_URL)
                      --build-arg UAE_PASS_URL=$(UAE_PASS_URL)
                      --build-arg UAE_PASS_ID=$(UAE_PASS_ID)
                      --build-arg UAE_PASS_STATE=$(UAE_PASS_STATE)
                      --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_API_KEY=$(NEXT_PUBLIC_FIREBASE_CONFIG_API_KEY)
                      --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_AUTH_DOMAIN=$(NEXT_PUBLIC_FIREBASE_CONFIG_AUTH_DOMAIN)
                      --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_PROJECT_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_PROJECT_ID)
                      --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_STORAGE_BUCKET=$(NEXT_PUBLIC_FIREBASE_CONFIG_STORAGE_BUCKET)
                      --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_MESSAGING_SENDER_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_MESSAGING_SENDER_ID)
                      --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_APP_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_APP_ID)
                      --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_MEASUREMENT_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_MEASUREMENT_ID)
                    imageName: '$(Build.Repository.Name):build-$(Build.SourceBranchName)-$(Build.SourceVersion)'

                - task: Docker@1
                  displayName: 'Push image to ACR'
                  inputs:
                    containerregistrytype: 'Azure Container Registry'
                    azureSubscriptionEndpoint: 'SCP-Gov-Academy-WS-PRD'
                    azureContainerRegistry: 'acrgovacademywsprduaen.azurecr.io'
                    command: 'Push an image'
                    imageName: '$(Build.Repository.Name):build-$(Build.SourceBranchName)-$(Build.SourceVersion)'
      - deployment: StgDeploymentJob
        displayName: Deployment Job
        dependsOn: WebViewStageBuild
        pool: 'SelfHostedAgent-StgAKS'
        environment: staging
        strategy:
          runOnce:
            deploy:
              steps:
                - task: Bash@3
                  displayName: 'Deploy to Stg AKS'
                  inputs:
                    targetType: inline
                    script: |
                      echo "Getting current deployed image"
                      kubectl get deployment dge-ga-webview -o=jsonpath='{.spec.template.spec.containers[0].image}'
                      cd /home/<USER>/helmrepo/dge-webview-stage
                      helm upgrade  dge-ga-webview . --set image.tag=build-$(Build.SourceBranchName)-$(Build.SourceVersion)
                      sleep 60
                      echo "Getting latest deployed image"
                      kubectl get deployment dge-ga-webview -o=jsonpath='{.spec.template.spec.containers[0].image}'
  - stage: ProdBuild
    condition: eq(variables['Build.SourceBranchName'], 'main')
    displayName: Prod Build Stage
    jobs:
      - job: WebViewStageBuild
        displayName: WebView ProdBuild
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: Bash@3
            displayName: 'Get Branch name'
            inputs:
              targetType: inline
              script: |
                echo "##vso[task.setvariable variable=BRANCH;]$(Build.SourceBranchName)"
          - task: Bash@3
            displayName: Get short git hash
            inputs:
              targetType: inline
              script: |
                BUILD_HASH=`git rev-parse --short=7 HEAD`
                echo ""
                echo "Full git hash:  $(Build.SourceVersion)"
                echo "Short git hash: $BUILD_HASH"
                echo "##vso[task.setvariable variable=BUILD_HASH;]$BUILD_HASH"
          - task: Bash@3
            displayName: 'Set ENV variable'

            inputs:
              targetType: inline
              script: |
                echo "##vso[task.setvariable variable=NODE_ENV;]"
                echo "##vso[task.setvariable variable=BASE_URL;]"
                echo "##vso[task.setvariable variable=INTERNAL_API;]"
                echo "##vso[task.setvariable variable=LIFERAY_API;]"
                echo "##vso[task.setvariable variable=TOTARA_API;]"
                echo "##vso[task.setvariable variable=TOTARA_AUTH_BASE_URL;]"
                echo "##vso[task.setvariable variable=UAE_PASS_URL;]"
                echo "##vso[task.setvariable variable=UAE_PASS_ID;]"
                echo "##vso[task.setvariable variable=UAE_PASS_STATE;]"
                echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_API_KEY;]AIzaSyDE9NL5uLqxgLS_1z-4DoBmU9HaEB-Wgk8"
                echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_AUTH_DOMAIN;]tomouh-web-production.firebaseapp.com"
                echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_PROJECT_ID;]tomouh-web-production"
                echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_STORAGE_BUCKET;]tomouh-web-production.firebasestorage.app"
                echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_MESSAGING_SENDER_ID;]147358848580"
                echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_APP_ID;]1:147358848580:web:ab66e0f1ff3c8f12708e11"
                echo "##vso[task.setvariable variable=NEXT_PUBLIC_FIREBASE_CONFIG_MEASUREMENT_ID;]G-WZQEVK7NKP"
          - task: Bash@3
            displayName: 'Set Docker build arguments to env file'
            inputs:
              targetType: inline
              script: |
                echo "The branch name is $(BRANCH)"
                echo "BUILD_HASH=$(BUILD_HASH)" >> .env  
                echo "NODE_ENV=$(NODE_ENV)" >> .env
                echo "BASE_URL=$(BASE_URL)" >> .env
                echo "INTERNAL_API=$(INTERNAL_API)" >> .env
                echo "LIFERAY_API=$(LIFERAY_API)" >> .env
                echo "TOTARA_API=$(TOTARA_API)" >> .env
                echo "TOTARA_AUTH_BASE_URL=$(TOTARA_AUTH_BASE_URL)" >> .env
                echo "UAE_PASS_URL=$(UAE_PASS_URL)" >> .env
                echo "UAE_PASS_ID=$(UAE_PASS_ID)" >> .env
                echo "UAE_PASS_STATE=$(UAE_PASS_STATE)" >> .env
                echo "NEXT_PUBLIC_FIREBASE_CONFIG_API_KEY=$(NEXT_PUBLIC_FIREBASE_CONFIG_API_KEY)" >> .env
                echo "NEXT_PUBLIC_FIREBASE_CONFIG_AUTH_DOMAIN=$(NEXT_PUBLIC_FIREBASE_CONFIG_AUTH_DOMAIN)" >> .env
                echo "NEXT_PUBLIC_FIREBASE_CONFIG_PROJECT_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_PROJECT_ID)" >> .env
                echo "NEXT_PUBLIC_FIREBASE_CONFIG_STORAGE_BUCKET=$(NEXT_PUBLIC_FIREBASE_CONFIG_STORAGE_BUCKET)" >> .env
                echo "NEXT_PUBLIC_FIREBASE_CONFIG_MESSAGING_SENDER_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_MESSAGING_SENDER_ID)" >> .env
                echo "NEXT_PUBLIC_FIREBASE_CONFIG_APP_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_APP_ID)" >> .env
                echo "NEXT_PUBLIC_FIREBASE_CONFIG_MEASUREMENT_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_MEASUREMENT_ID)" >> .env
                cat .env

          - task: Docker@1
            displayName: 'Build an image'
            inputs:
              containerregistrytype: 'Azure Container Registry'
              azureSubscriptionEndpoint: 'SCP-Gov-Academy-WS-PRD'
              azureContainerRegistry: 'acrgovacademywsprduaen.azurecr.io'
              command: 'Build an Image'
              dockerFile: '**/Dockerfile'
              arguments: |
                --build-arg BUILD_HASH=$(BUILD_HASH)
                --build-arg NODE_ENV=$(NODE_ENV)
                --build-arg BASE_URL=$(BASE_URL)
                --build-arg INTERNAL_API=$(INTERNAL_API)
                --build-arg LIFERAY_API=$(LIFERAY_API)
                --build-arg TOTARA_API=$(TOTARA_API)
                --build-arg TOTARA_AUTH_BASE_URL=$(TOTARA_AUTH_BASE_URL)
                --build-arg UAE_PASS_URL=$(UAE_PASS_URL)
                --build-arg UAE_PASS_ID=$(UAE_PASS_ID)
                --build-arg UAE_PASS_STATE=$(UAE_PASS_STATE)
                --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_API_KEY=$(NEXT_PUBLIC_FIREBASE_CONFIG_API_KEY)
                --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_AUTH_DOMAIN=$(NEXT_PUBLIC_FIREBASE_CONFIG_AUTH_DOMAIN)
                --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_PROJECT_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_PROJECT_ID)
                --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_STORAGE_BUCKET=$(NEXT_PUBLIC_FIREBASE_CONFIG_STORAGE_BUCKET)
                --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_MESSAGING_SENDER_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_MESSAGING_SENDER_ID)
                --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_APP_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_APP_ID)
                --build-arg NEXT_PUBLIC_FIREBASE_CONFIG_MEASUREMENT_ID=$(NEXT_PUBLIC_FIREBASE_CONFIG_MEASUREMENT_ID)
              imageName: '$(Build.Repository.Name):build-$(Build.SourceBranchName)-$(Build.SourceVersion)'

          - task: Docker@1
            displayName: 'Push image to ACR'
            inputs:
              containerregistrytype: 'Azure Container Registry'
              azureSubscriptionEndpoint: 'SCP-Gov-Academy-WS-PRD'
              azureContainerRegistry: 'acrgovacademywsprduaen.azurecr.io'
              command: 'Push an image'
              imageName: '$(Build.Repository.Name):build-$(Build.SourceBranchName)-$(Build.SourceVersion)'
