@use 'mixins' as *;

.card {
  display: flex;
  @include borderRadius(var(--radius));
  background: var(--white);
  @include forMarginPadding(padding, size(16), size(16), size(16), size(16));
  overflow: hidden;
  border: solid 1px var(--grey-400);
  overflow: hidden;

  &:hover {
    div[class*='bg'] {
      transform: scale(1.1, 1.1);
      transition: 0.4s transform cubic-bezier(0.77, 0.23, 0.23, 0.81);

      &:before {
        opacity: 0.7;
        transition: 0.4s all cubic-bezier(0.77, 0.23, 0.23, 0.81);
      }
    }

    h4[class*='title'] {
      text-decoration: underline;
    }
  }
}

.bg {
  width: size(139);
  height: size(139);
  @include borderRadius(size(8));
  overflow: hidden;
  flex-shrink: 0;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  transform: scale(1, 1);
  transition: 0.4s transform cubic-bezier(0.77, 0.23, 0.23, 0.81);

  a {
    width: 100%;
    height: 100%;
    position: absolute;
    @include leftToRight(0);
    top: 0;
    z-index: 1;
  }
}

.imageWrapper {
  width: size(139);
  height: size(139);
  @include borderRadius(size(8));
  overflow: hidden;
  flex-shrink: 0;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
}

.content {
  @include forMarginPadding(padding, size(0), size(0), size(0), size(16));
  display: flex;
  flex-direction: column;
  width: 60%;
}

.heading {
  font-size: size(16);
}

.top {
  display: flex;
  align-items: center;
  width: 100%;

  p {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    font-weight: 400;
    font-size: size(12);
  }

  button {
    @include forMarginPadding(margin, size(0), size(0), size(0), auto);
  }

  svg[class*='spinner'] {
    height: size(22);
    width: size(22);
    @include forMarginPadding(margin, size(0), size(0), size(0), auto);
  }
}

.bottom {
  @include forMarginPadding(margin, auto, 0, 0, 0);

  h4 {
    font-weight: 700;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
  }

  nav[class*='list'] {
    span {
      color: var(--grey-800);
      font-size: size(12);
    }

    ul {
      li {
        @include forMarginPadding(margin, size(0), size(8), size(0), size(0));

        &:last-child {
          @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
        }
      }
    }
  }
}

.continueLabelGamma {
  font-size: size(16);
  text-decoration: underline;
  text-transform: lowercase;
  margin-bottom: size(0);

  &::first-letter {
    text-transform: uppercase;
  }
}
