import { NextRequest, NextResponse } from 'next/server';
import { ExternalChatService } from '@/services/external/ai/chat';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';

// Server side api for AI chat.
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    const response = await ExternalChatService(data);

    return NextResponse.json({
      data: response.data,
    });
  } catch (error) {
    return ErrorResponse(error as AxiosError);
  }
}
