import { FC } from 'react';
import Image from 'next/image';
import Typography from '@/ui/typography';
import styles from './style.module.scss';

import profileImage from '@public/images/job-profile-image.jpg';

interface IItem {
  peerId: number;
  name: string;
  title: string;
  onClick: (id: number) => void;
}

const Item: FC<IItem> = (props) => {
  // Deconstruct props
  const { peerId, name, title, onClick } = props;

  /**
   * Method to handle click on item
   */
  const handleClick = () => {
    // Check if onClick is defined
    if (onClick) {
      // Call onClick
      onClick(peerId);
    }
  };

  return (
    <div className={styles.profile} onClick={handleClick}>
      <div className={styles.pic}>
        <Image
          src={profileImage}
          alt=""
          className="img-fluid"
          height={100}
          width={100}
        />
      </div>
      <div className={styles.data}>
        <Typography as="h4" className={styles.name}>
          {name.length > 40 ? `${name.substring(0, 40)}...` : name}
        </Typography>
        <Typography as="p" className={styles.role}>
          {title}
        </Typography>
      </div>
    </div>
  );
};

export default Item;
