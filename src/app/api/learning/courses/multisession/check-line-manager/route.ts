import { ErrorResponse } from '@/utils/fetcher';
import { NextResponse } from 'next/server';
import { AxiosError } from 'axios';
import { TotaraCheckLineManagerService } from '@/services/totara/courses/multisession';

export async function GET() {
  try {
    const response = await TotaraCheckLineManagerService();
    // Return the response

    return NextResponse.json({
      lineManagerCheck: response?.data,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error as AxiosError);
  }
}
