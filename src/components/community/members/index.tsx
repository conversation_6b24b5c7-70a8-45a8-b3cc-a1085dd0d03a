import { ICommunityMemberDetails } from '@/types/domain/my-network/communityDetails';
import MembersItem from './members-item';
import Paper from '@/ui/paper';
import { useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';

interface IMemberProps {
  members?: ICommunityMemberDetails[];
}

//Compoment to display community details members tab
const Members: React.FC<IMemberProps> = ({ members }) => {
  const t = useTranslations();
  return (
    <Paper title={t(dictionary.Members)}>
      {members &&
        members.map((member) => {
          return <MembersItem member={member} key={member.id} />;
        })}
    </Paper>
  );
};

export default Members;
