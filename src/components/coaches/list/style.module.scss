@use 'mixins' as *;

.list {
  min-height: size(100);
  position: relative;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: size(16);
}

.isEmpty {
  display: flex;
}

.card {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  background: var(--white);
  @include borderRadius(var(--radius));
  @include forMarginPadding(padding, size(16), size(16), size(16), size(16));
  overflow: hidden;

  height: size(438);
  vertical-align: top;
  border: 1px solid var(--grey-400);
  position: relative;
}

.photo {
  @include borderRadius(16px);
  overflow: hidden;
  @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
  height: size(220);

  .bookmark {
    display: flex;
    justify-content: center;
    align-items: center;
    @include borderRadius(50%);
    position: absolute;
    top: size(22);
    @include rightToLeft(size(22));
    backdrop-filter: blur(5px);
    background: rgba($color: #000000, $alpha: 0.2);
    cursor: pointer;
    width: size(36);
    height: size(36);

    svg {
      fill: var(--always-white);
      width: size(24);
      height: size(24);
    }
  }
}

.data {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.dataHeader {
  display: flex;
  align-items: baseline;
}

.title {
  display: inline-block;
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  font-size: size(20);
  letter-spacing: size(0);
  line-height: 1.2;
  padding-bottom: 0;
  transition: all 0.5s linear;
  background: linear-gradient(
    to bottom,
    var(--grey-900) 0%,
    var(--grey-900) 98%
  );
  background-size: 0 1px;
  background-repeat: no-repeat;
  background-position: left 100%;
  font-weight: 600;

  width: 100%;
  cursor: pointer;

  @include rtl {
    background-position: right 100%;
  }
}

.rating {
  display: flex;
  align-items: center;
  line-height: 1;
  @include rightToLeft(size(16));

  i {
    width: 16px;
    height: 16px;
    position: relative;
    @include forMarginPadding(margin, size(0), size(5), size(0), size(0));
  }

  span {
    font-size: size(16);
    line-height: 1.2;
  }
}

.designation {
  display: flex;
  align-items: flex-start;
  gap: size(12);
  font-size: size(14);
  line-height: 1.2;
  letter-spacing: size(0);
  @include forMarginPadding(margin, size(15), size(0), size(0), size(0));

  svg {
    fill: var(--grey-900);
    width: size(16);
  }
}

.coachExperience {
  display: flex;
  align-items: center;
  @include forMarginPadding(margin, size(15), size(0), size(16), size(0));

  .experienceLogos {
    display: flex;

    img {
      width: size(30);
      height: size(30);
      @include borderRadius(50%);
      border: 1px solid black;
    }

    .logoImgOverlap {
      @include forMarginPadding(margin, size(0), size(0), size(0), size(-15));
    }
  }

  .experienceText {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(10));
  }
}

.right {
  width: 20%;
}

.line {
  color: var(--grey-800);
  background-color: var(--grey-800);
  height: 1;
  @include forMarginPadding(margin, size(25), size(0), size(25), size(0));
}

.skills {
  margin-top: auto;
  display: flex;
  flex-wrap: wrap;
  gap: size(10);
  border-top: size(1) solid var(--grey-400);
  @include forMarginPadding(padding, size(20), size(0), size(0), size(0));

  .skillItem {
    display: flex;
    justify-content: center;
    @include borderRadius(40px);
    @include forMarginPadding(padding, size(8), size(16), size(8), size(16));
    background-color: var(--bg-shade-10);
    font-size: size(16);

    .skill {
      color: var(--grey-900);
    }
  }
}

@include for-all-phone() {
  .list {
    grid-template-columns: repeat(1, 1fr);
  }

  .card {
    display: flex;
    align-items: center;
    min-height: initial;
  }

  .photo {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }

  .data {
    @include forMarginPadding(padding, size(0), size(0), size(0), size(16));
  }
}
