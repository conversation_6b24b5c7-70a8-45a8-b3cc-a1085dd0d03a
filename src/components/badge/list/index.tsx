import { FC } from 'react';
import Skeleton from '@/ui/skeleton';
import Badges from '@/ui/badges';
import useBadgesStore from '@/stores/learning/badges';
import IBadge from '@/types/domain/learning/badge';

// Styles
import styles from './style.module.scss';
import Paper from '@/ui/paper';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';
import EmptyMessage from '@/ui/empty-message';

interface IBadgeList {
  onBadgeClick?: (badge: IBadge) => void;
}

const BadgesList: FC<IBadgeList> = ({ onBadgeClick }) => {
  // Getting loading state badges, available badges and user badges
  const {
    isFetching,
    available: availableBadges,
    user: userBadges,
  } = useBadgesStore();

  const t = useTranslations();

  // Return JSX
  return (
    <>
      <Paper
        title={userBadges.length ? dictionary.myBadgesAndAchievements : ''}
        titleVariant="large"
      >
        <div
          className={
            styles.badgeList + ` ${!userBadges.length ? styles.isEmpty : ''}`
          }
        >
          {isFetching && <Skeleton />}
          {userBadges.length ? (
            userBadges.map((badge) => (
              <Badges
                key={'badge-' + badge.id}
                icon={badge?.image_url}
                title={badge?.name}
                helpTxt={t(dictionary.badgeEarnedOn)}
                date={badge?.completed_date}
                onClick={() => (onBadgeClick ? onBadgeClick(badge) : null)}
              />
            ))
          ) : (
            <EmptyMessage
              icon="badge"
              title={dictionary.earnMoreBadges}
              description={
                t(dictionary.youHaventEarnedAnyBadgesYet) +
                ' ' +
                t(
                  dictionary.startExploringAndCompletingActivitiesToUnlockYourFirstBadge,
                )
              }
            />
          )}
        </div>
      </Paper>
      <Paper title={dictionary.earnMoreBadges} titleVariant="large">
        <div className={styles.badgeList}>
          {isFetching && <Skeleton />}
          {availableBadges.map((badge) => (
            <Badges
              key={'badge-' + badge.id}
              icon={badge?.image_url}
              title={badge?.name}
              viewText={t(dictionary.viewCriteria)}
              varient="secondary"
              onClick={() => (onBadgeClick ? onBadgeClick(badge) : null)}
            />
          ))}
        </div>
      </Paper>
    </>
  );
};

export default BadgesList;
