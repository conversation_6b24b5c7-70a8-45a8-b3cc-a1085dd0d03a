import { getCase } from '@/services/external/support-center';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';
import { NextResponse } from 'next/server';

// Server side api that gets case details.
export async function GET(
  request: Request,
  { params }: { params: Promise<{ caseId: string }> },
) {
  try {
    const { caseId } = await params;
    const response = await getCase(caseId);

    return NextResponse.json({
      data: response.data,
    });
  } catch (error) {
    return ErrorResponse(error as AxiosError);
  }
}
