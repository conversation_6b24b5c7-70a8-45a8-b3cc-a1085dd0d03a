@use 'mixins' as *;

.sideBar {
  @include forMarginPadding(padding, size(24), size(16), size(16), size(16));
  @include borderRadius(var(--radius));
  background: var(--white);
  min-height: size(200);
  position: relative;
}

.title {
  text-transform: uppercase;
  color: var(--grey-700);
}

.profileImageContainer {
  position: relative;
  display: inline-block;
  @include forMarginPadding(margin, size(0), size(0), size(10), size(0));

  .profileInfoImage {
    width: size(140);
    height: size(140);
    overflow: hidden;
    @include borderRadius(50%);
    display: block;
  }
}

.profileUpdatePhotoButton {
  align-items: center;
  background: var(--grey-900);
  border-radius: 50%;
  border: 1px solid var(--grey-500);
  cursor: pointer;
  display: flex;
  justify-content: center;
  height: size(34);
  width: size(34);
  position: absolute;
  right: size(3);
  bottom: size(7);
  @include rtl {
    left: size(3);
    right: auto;
  }
  @include for-dark-theme() {
    background: var(--white);
    border: 1px solid var(--grey-900);
  }
  svg {
    height: size(20);
    width: size(20);
    fill: var(--white);
    @include for-dark-theme() {
      fill: var(--grey-900);
    }
  }
}

.profileGeneralInfoContainer {
  @include forMarginPadding(padding, size(0), size(0), size(10), size(0));

  border-bottom: 1px solid var(--grey-400);
  .profileInfoName {
    font-size: size(28);
    text-decoration: none;
    color: var(--grey-900);
    font-weight: 700;
    display: block;
    text-transform: capitalize;
    word-wrap: break-word;
    @include forMarginPadding(margin, size(0), size(0), size(10), size(0));
  }
}

.profileInfoIcon {
  font-size: size(14);
  text-decoration: none;
  color: var(--grey-700);
  @include forMarginPadding(margin, size(10), size(0), size(8), size(0));

  svg ~ span {
    @include forMarginPadding(padding, size(0), size(7), size(0), size(7));
  }
  svg {
    fill: var(--grey-700);
    width: size(13);
  }
}

.profileTextIconInfo {
  font-size: size(14);
  text-decoration: none;
  color: var(--grey-700);
  @include forMarginPadding(margin, size(10), size(0), size(8), size(0));
  span {
    display: flex;
  }
  svg {
    fill: var(--grey-700);
    width: size(13);
  }
}

.profileTextInfo {
  @include forMarginPadding(margin, size(0), size(0), size(0), size(10));

  display: flex;
  flex-direction: column;
}

.profileBlueTextInfo {
  color: var(--text-link-blue);
}

.profileGeneralInfoOrganization {
  @include forMarginPadding(margin, size(0), size(15), size(0), size(15));
  @include forMarginPadding(padding, size(10), size(0), size(10), size(0));

  border-bottom: 1px solid var(--grey-400);

  .profileOrganizationTitle {
    font-size: size(22);
    text-decoration: none;
    color: var(--grey-900);
    font-weight: 700;
    display: block;
    text-transform: capitalize;
    word-wrap: break-word;
    @include forMarginPadding(margin, size(10), size(0), size(10), size(0));
    svg ~ span {
      @include forMarginPadding(padding, size(0), size(7), size(0), size(7));
    }
    svg {
      fill: var(--grey-900);
      width: size(22);
    }
  }

  .profileOrganizationText {
    @include forMarginPadding(margin, size(0), size(0), size(10), size(0));

    span {
      display: flex;
      justify-content: space-between;
    }
  }
}

.profileGrayText {
  color: var(--grey-700);
}

.profileContactInfoContainer {
  @include forMarginPadding(margin, size(24), size(0), size(0), size(0));
  @include forMarginPadding(padding, size(0), size(0), size(20), size(0));
  border-bottom: 1px solid var(--grey-400);
}

.profileReportsInfoContainer {
  @include forMarginPadding(margin, size(24), size(0), size(0), size(0));
}

.viewProfileButton {
  background-color: transparent;
  border: 0;
  color: var(--text-link-blue);
  cursor: pointer;
  p {
    margin: 0;
  }
}

.viewProfile {
  @include forMarginPadding(padding, size(8), size(0), size(0), size(0));
}

.reportsWrapper {
  display: flex;
}

.pic {
  @include borderRadius(50%);
  flex-shrink: 0;
  overflow: hidden;
  width: size(85);
  height: size(85);
  @include forMarginPadding(margin, size(0), size(16), size(0), size(0));

  img {
    transform: scale(1);
    transition: 0.2s all ease-in-out;
  }
}

.profileDetailsButton {
  position: sticky;
  bottom: size(24);
  width: 100%;
  @include forMarginPadding(margin, auto, size(0), size(0), size(0));
}

.containerWorkWith {
  @include forMarginPadding(margin, size(30), size(0), size(0), size(0));

  @include forMarginPadding(padding, size(20), size(0), size(10), size(0));
  border-top: 1px solid var(--grey-400);

  ul {
    display: grid;
    grid-template-columns: repeat(3, 1fr);

    li {
      @include forMarginPadding(padding, size(0), size(8), size(8), size(8));
      position: relative;
      list-style: none;
    }
  }
}

.workWithItem {
  display: flex;
}

.workWithItemImage {
  @include borderRadius(50%);
  flex-shrink: 0;
  overflow: hidden;
  width: size(40);
  height: size(40);
  @include forMarginPadding(margin, size(0), size(16), size(0), size(0));

  img {
    transform: scale(1);
    transition: 0.2s all ease-in-out;
  }
}

.workWithItemData {
  p {
    @include forMarginPadding(margin, size(0), size(0), size(4), size(0));
  }
}

.chartWrapper {
  @include forMarginPadding(margin, size(0), size(24), size(0), size(0));
}

.itemChart {
  display: flex;
  align-items: center;
  border: 1px solid var(--progressBg);
  @include borderRadius(var(--radius));

  @include forMarginPadding(padding, size(11), size(16), size(11), size(16));
  cursor: pointer;
  .itemChartImage {
    @include borderRadius(50%);
    flex-shrink: 0;
    overflow: hidden;
    width: size(70);
    height: size(70);
    @include forMarginPadding(margin, size(0), size(16), size(0), size(0));

    img {
      transform: scale(1);
      transition: 0.2s all ease-in-out;
    }
  }

  .itemChartData {
    p,
    h4 {
      @include forMarginPadding(margin, size(0), size(0), size(4), size(0));
    }
  }
}

.verticalLine {
  width: 1px;
  height: 16px;
  background-color: var(--progressBg);
  margin: 0 auto;
  &:first-child {
    display: none;
  }
}
