import { useEffect } from 'react';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';
import Paper from '@/ui/paper';
import Grid from '@/ui/grid';
import EmptyMessage from '@/ui/empty-message';
import useTrendingCoursesStore from '@/stores/learning/courses/trending';
import Course from '@/components/course';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';

const TrendingCoursesList = () => {
  // Getting loading state
  const isFetching = useTrendingCoursesStore((x) => x.isFetching);

  // Getting fetch action
  const onFetch = useTrendingCoursesStore((x) => x.fetch);

  // Getting trending courses
  const selected = useTrendingCoursesStore((x) => x.selected);
  const t = useTranslations();
  useEffect(() => {
    if (isFetching === null) {
      // Fetching trending courses
      onFetch();
    }
  }, [onFetch, isFetching]);

  // Return JSX
  return (
    <WithLoader loading={isFetching} loader={<Loading />}>
      <Paper>
        {!isFetching && selected && selected.courses?.length > 0 ? (
          <Grid xs={1} sm={3} md={4} gap="lg">
            {selected.courses.map((item) => {
              return <Course key={item.courseid} course={item} />;
            })}
          </Grid>
        ) : (
          <EmptyMessage
            icon="search"
            title={t(dictionary.noSearchResultsFound)}
            description={t(
              dictionary.pleaseCheckThatAllWordsAreSpelledCorrectly,
            )}
          />
        )}
      </Paper>
    </WithLoader>
  );
};

export default TrendingCoursesList;
