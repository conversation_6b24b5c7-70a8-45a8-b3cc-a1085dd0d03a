@use 'mixins' as *;

.confirmationDialog {
  display: none;
}

.confirmationDialogOpened {
  border: 0;
  border-radius: size(24);
  display: flex;
  flex-direction: column;
  gap: size(24);
  margin: auto;
  min-height: size(262);
  @include forMarginPadding(padding, size(40), size(32), size(32), size(32));
  width: size(430);
}

.confirmationDialogTitle {
  font-size: size(28);
  font-weight: 700;
  color: var(--grey-900);
  @include forMarginPadding(margin, size(0), size(0), size(25), size(0));
}

.confirmationDialogMessage {
  font-size: size(18);
  color: var(--grey-800);
  @include forMarginPadding(margin, size(0), size(0), size(25), size(0));
}

.confirmationDialogActions {
  display: flex;
  margin-top: auto;

  .confirmationDialogClose {
    width: 100%;
  }
}

.iconWrapper {
  width: size(48);
  height: size(48);
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--success-900);
  margin-bottom: size(12);
  @include borderRadius(50%);

  & svg {
    width: size(36);
    height: size(36);
    fill: var(--white);
  }
}

.slot {
  background-color: var(--bg-shade-4);
  border-radius: size(12);
  display: flex;
  flex-direction: column;
  gap: size(11);
  @include forMarginPadding(padding, size(24), size(24), size(24), size(24));

  .slotItem {
    display: flex;
    flex-direction: row;
    align-items: center;

    gap: size(8);

    span {
      font-size: size(20px);
    }

    svg {
      fill: var(--grey-900);
      height: size(24);
      width: size(24);
    }
  }
}

@include for-dark-theme() {
  .slot {
    background-color: var(--bg-card-1);
  }
}
