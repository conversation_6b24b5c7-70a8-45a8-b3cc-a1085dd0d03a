@use 'mixins' as *;

.category {
  min-height: size(200);
  position: relative;
  background: var(--white);
  overflow: hidden;
  @include forMarginPadding(padding, size(34), size(24), size(22), size(24));
  @include borderRadius(var(--radius));
  @include forMarginPadding(margin, size(0), size(0), size(34), size(0));
}

.list {
  ul {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    @include forMarginPadding(padding, size(0), size(0), size(0), size(0));

    li {
      list-style: none;
      border-bottom: solid 1px var(--grey-400);
      @include forMarginPadding(padding, size(16), size(0), size(16), size(0));

      &:first-child {
        @include forMarginPadding(padding, size(0), size(0), size(12), size(0));
      }

      &:last-child {
        border: none;
      }
    }
  }
}

.head {
  display: flex;
  align-items: center;
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));

  h5 {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    font-weight: 700;
  }

  a {
    @include forMarginPadding(margin, size(0), size(0), size(0), auto);
    font-size: size(14);
  }
}

@include for-all-phone() {
  .list {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    white-space: nowrap;

    ul {
      display: flex;
      align-items: flex-start;

      li {
        border: none;
        @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
        @include forMarginPadding(margin, size(0), size(20), size(0), size(0));
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        &:first-child,
        &:last-child {
          @include forMarginPadding(
            padding,
            size(0),
            size(0),
            size(0),
            size(0)
          );
        }
      }
    }
  }

  .category {
    min-height: initial;
    @include forMarginPadding(margin, size(0), size(0), size(40), size(0));
    @include forMarginPadding(padding, size(25), size(16), size(25), size(16));
    //@include forMarginPadding(margin, size(0), size(0), size(20), size(0));
  }
}

.viewAllButton {
  background-color: transparent;
  border: 0;
  color: var(--grey-800);
  cursor: pointer;

  p {
    margin-bottom: 0;
  }
}

.viewAllWrapper {
  border-top: solid 1px var(--grey-400);
  text-align: center;
  @include forMarginPadding(padding, size(18), size(0), size(8), size(0));
}

.categoriesWrapper {
  height: calc(100vh - size(180));
  width: calc(100% - size(30));
  @include forMarginPadding(padding, size(0), size(10), size(0), size(0));
  overflow: auto;

  button[class*='linkItem'] {
    font-size: size(16);
    width: 100%;
    @include forMarginPadding(padding, size(24), size(0), size(24), size(0));
  }

  nav[class*='list'] {
    ul {
      li {
        @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
      }
    }
  }

  @media (min-width: 576px) {
    &::-webkit-scrollbar-track {
      background-color: var(--white);
    }

    &::-webkit-scrollbar {
      width: size(8);
      background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: size(8);
      background-color: var(--grey-900);
    }
  }
}
