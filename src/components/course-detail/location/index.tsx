import dictionary from '@/dictionaries';
import useCourseDetailStore from '@/stores/learning/courses/detail';
import Typography from '@/ui/typography';
import Slot from './slot';
import MultiDay from './multi-day';

const LocationTab = () => {
  const courseDetail = useCourseDetailStore((state) => state.detail);

  return (
    <>
      <Typography as="h4" dictionary={dictionary.location} />
      <div>
        {courseDetail?.course_day === 'One Day' &&
          courseDetail?.available_slots?.map((slot) => (
            <Slot key={slot.id} slot={slot} />
          ))}
        {courseDetail?.course_day === 'Multi Day' && <MultiDay />}
      </div>
    </>
  );
};

export default LocationTab;
