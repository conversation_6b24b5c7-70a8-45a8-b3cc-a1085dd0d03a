import { FC, useState } from 'react';
import WithLoader from '@/ui/skeleton/with-loader';
import Paper from '@/ui/paper';
import Item from './item';
import Loading from './loading';

import styles from './style.module.scss';
import EmptyMessage from '@/ui/empty-message';
import dictionary from '@/dictionaries';
import profileImage from '@public/images/job-profile-image.jpg';
import DialogDrawer from '@/ui/dialog-drawer';
import GeneralInfo from '../side-panel/general-info';
import ContactInfo from '../side-panel/contact-info';
import ReportInfo from '../side-panel/report-info';
import Button from '@/ui/form/button';
import Typography from '@/ui/typography';

interface IList {
  onClick?: (id: string) => void;
}

const peersList = [
  {
    id: '1',
    name: '<PERSON> (<PERSON>)',
    work: 'Undersecretary',
    department: 'Department of Government Enablemant',
  },
  {
    id: '2',
    name: '<PERSON><PERSON><PERSON>',
    work: 'Undersecretary',
    department: 'Department of Government Enablemant',
  },
  {
    id: '3',
    name: '<PERSON><PERSON><PERSON>',
    work: 'Undersecretary',
    department: 'Department of Government Enablemant',
  },
  {
    id: '4',
    name: 'Sophia Mare',
    work: 'Director General',
    department: 'Department of Government Enablemant',
  },
  {
    id: '5',
    name: 'Mohammad Mansouri',
    work: 'Director General',
    department: 'Department of Government Enablemant',
  },
  {
    id: '6',
    name: 'Mohsen Mokhtari',
    work: 'Director General',
    department: 'Department of Government Enablemant',
  },
  {
    id: '7',
    name: 'Abdullah Ahmed (You)',
    work: 'Undersecretary',
    department: 'Department of Government Enablemant',
  },
  {
    id: '8',
    name: 'H.E Ahmed Sheikh Abdullah',
    work: 'Undersecretary',
    department: 'Department of Government Enablemant',
  },
  {
    id: '9',
    name: 'Mahsa Jannaty',
    work: 'Undersecretary',
    department: 'Department of Government Enablemant',
  },
  {
    id: '10',
    name: 'Sophia Mare',
    work: 'Director General',
    department: 'Department of Government Enablemant',
  },
  {
    id: '11',
    name: 'Mohammad Mansouri',
    work: 'Director General',
    department: 'Department of Government Enablemant',
  },
  {
    id: '12',
    name: 'Mohsen Mokhtari',
    work: 'Director General',
    department: 'Department of Government Enablemant',
  },
];

const List: FC<IList> = () => {
  const [openViewProfileDetails, setOpenViewProfileDetails] = useState(false);
  const isFetching = false;

  const closeProfileDetails = () => {
    setOpenViewProfileDetails(false);
  };

  const viewProfileDetails = () => {
    setOpenViewProfileDetails(true);
  };

  const onAddToContacts = () => {
    throw new Error('Function not implemented.');
  };

  const onClick = () => {
    viewProfileDetails();
  };

  return (
    <>
      <Paper>
        <div className={styles.list}>
          <WithLoader loader={<Loading />} loading={isFetching}>
            {peersList?.map((x) => (
              <Item
                photo={profileImage.src}
                id={x.id}
                key={x.id}
                name={x.name}
                department={x.department}
                work={x.work}
                onClick={onClick}
              />
            ))}
          </WithLoader>
          {peersList?.length === 0 && !isFetching ? (
            <EmptyMessage
              icon="search"
              title={'No peers found with that name'}
              description={'No peers yet'}
            />
          ) : null}
        </div>
      </Paper>
      <DialogDrawer
        isOpened={openViewProfileDetails}
        onClose={closeProfileDetails}
      >
        <div className={styles.wrapper}>
          <GeneralInfo />
          <ContactInfo />
          <ReportInfo />
          <Button
            type="button"
            onClick={onAddToContacts}
            color="black"
            className="dialog-form-action"
          >
            <Typography as="span" dictionary={dictionary.addToContacts} />
          </Button>
        </div>
      </DialogDrawer>
    </>
  );
};

export default List;
