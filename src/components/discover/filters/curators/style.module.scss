@use 'mixins' as *;

.filter {
  border-bottom: size(1) solid var(--grey-400);
}

.filterHeader {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: size(24) size(0) size(24);
  position: relative;

  .accordionAction {
    width: size(32);
    height: size(32);
    border: size(1) solid var(--grey-400);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out;

    svg {
      width: size(8);
      height: size(14);
      transform: rotate(-90deg);
      fill: var(--grey-900);
      transition: fill 0.3s ease-in-out;
    }

    &:hover {
      background-color: var(--grey-900);

      svg {
        fill: var(--white);
      }
    }
  }

  .accordionAction.openState {
    svg {
      transform: rotate(90deg);
    }
  }

  .accordionAction.closeState {
    svg {
      transform: rotate(-90deg);
    }
  }
}

.filterContent {
  padding-bottom: size(24);
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.listContainer {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  position: relative;
  overflow: hidden;
  list-style: none;
}

.curatorsSelectionDialog {
  height: calc(100% - size(135));
}

.curatorDialogList {
  display: flex;
  flex-direction: column;
  margin-top: size(32);

  height: calc(100% - size(188));
  padding-bottom: size(8);
  overflow: auto;

  scrollbar-width: thin;

  .curatorDialogItem {
    margin-right: size(24);
    padding-bottom: size(22);
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    @include rtl {
      margin-left: size(24);
      margin-right: 0;
    }

    &:not(:first-child) {
      padding-top: size(22);
    }

    &:not(:last-child) {
      border-bottom: size(1) solid var(--disabled-border);
    }

    input {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
    }

    .labelText {
      font-size: size(16);
      color: var(--grey-900);
      line-height: size(20);
    }

    .checkmark {
      width: size(20);
      height: size(20);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }

  .curatorDialogItem.notChecked .checkmark {
    border-radius: size(4);
    border: size(1) solid #1e1e1e66;
  }

  .curatorDialogItem.checked .checkmark {
    background-color: var(--grey-900);
    border-radius: size(4);
    border: size(1) solid var(--grey-900);
    svg {
      width: size(12);
      fill: var(--white);
    }
  }

  .curatorDialogItem.active {
    background-color: red;
  }
}

.curatorsSearch {
  border: size(1) solid #00000059;
  border-radius: size(4);
  padding: size(0) size(16) size(0) size(20);
  margin-right: size(24);
  height: size(55);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  @include rtl {
    margin-left: size(24);
    margin-right: 0;
  }

  input::placeholder {
    color: var(--grey-600);
  }

  input::-webkit-search-cancel-button {
    -webkit-appearance: none;
    appearance: none;
    display: none;
  }
  input {
    width: 100%;
    height: size(14);
    border: none;
    outline: none;
    font-size: size(16);
    color: var(--grey-900);
  }
  .searchIcon {
    width: size(24);
    height: size(24);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    svg {
      width: 100%;
      fill: var(--grey-900);
      fill-rule: evenodd;
    }
  }
  .deleteIcon {
    width: size(14);
    border: none;
    outline: none;
    background: none;
    svg {
      width: 100%;
      fill: var(--grey-900);
      fill-rule: evenodd;
    }
  }
}

.showMoreButton {
  background-color: transparent;
  border: none;
  color: var(--grey-900);
  font-size: size(14);
  text-decoration: underline;
  @include forMarginPadding(margin, size(20), size(0), size(0), size(0));
  cursor: pointer;
  transition: 0.3s all ease-in-out;

  &:hover {
    text-decoration: none;
  }
}
ul.curatorsMobileList {
  max-height: size(84);
  overflow: clip;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 8px;
  padding: 0;
  margin: 0;
  list-style: none;
  li {
    button {
      @include forMarginPadding(
        padding,
        size(12),
        size(15),
        size(12),
        size(15)
      );
      line-height: size(10);
      span {
        line-height: size(10);
      }
    }
  }

  .chipListItem {
    list-style: none;
    margin: 0;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    order: 1;
  }

  .hiddenCountChipListItem {
    list-style: none;
    margin: 0;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    order: 2;
  }
}
.curatorsShowMore {
  margin-top: size(20);
  background: transparent;
  border: none;
  color: var(--grey-900);
  font-size: size(14);
  text-decoration: underline;
}

.curatorsListPopup {
  list-style: none;
  li {
    padding-bottom: size(20);
  }
}
.curatorDialogFooter {
  position: absolute;
  bottom: 0;
  left: 0;
  width: calc(100% - size(48));
  width: 100%;
  background: red;
  display: flex;
  justify-content: center;
  padding: size(24) size(48) size(48);
  border-radius: size(24) size(24) 0 0;
  box-shadow: 8px 0px 10px 0px #00000014;
  background-color: var(--white);
}

@include for-dark-theme() {
  .curatorsSearch {
    border: size(1) solid rgba(255, 255, 255, 0.3490196078);
    input {
      background-color: transparent;
      outline: none;
    }
  }

  .curatorDialogItem.notChecked {
    .checkmark {
      border: size(1) solid #ffffff66;
    }
  }

  .curatorDialogFooter {
    box-shadow: 8px 0px 10px 0px #ffffff04;
  }
}

@include for-all-phone() {
  .curatorDialogFooter {
    padding: size(24) size(24) size(48);
  }
}

.loadingContainer {
  height: size(40);

  > div {
    min-height: size(20) !important;
    height: size(20) !important;
    max-height: size(20) !important;
    padding: 0 !important;
  }
}
