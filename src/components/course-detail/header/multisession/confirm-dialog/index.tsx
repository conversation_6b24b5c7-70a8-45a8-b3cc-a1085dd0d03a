import { FC, useRef, useEffect, useMemo } from 'react';
import styles from './style.module.scss';
import Typography from '@/ui/typography';
import Button from '@/ui/form/button';
import dictionary from '@/dictionaries';
import { CalendarRounded, Clock } from '@/ui/icons';
import { PopupMessageMultisession } from '@/stores/learning/courses/multisession';
import useCourseDetailStore from '@/stores/learning/courses/detail';

import moment from 'moment';
import 'moment/locale/ar';
import { useLocale } from 'next-intl';

interface IConfirmationDialog {
  isOpen?: boolean;
  onClose: () => void;
  PopupMessageMultisession?: PopupMessageMultisession;
  courseId: string;
}

const ConfirmationDialog: FC<IConfirmationDialog> = ({
  isOpen,
  onClose,
  PopupMessageMultisession,
  courseId,
}) => {
  const { title, message, slot, refreshData } = PopupMessageMultisession || {};
  const locale = useLocale();

  const dialogRef = useRef<HTMLDialogElement>(null);
  const onFetch = useCourseDetailStore((state) => state.fetch);

  const formatedSessionDate = useMemo(() => {
    const seminarDate = slot?.seminar_date;
    if (!seminarDate) {
      return '';
    }
    const currentLocale = ['ar', 'en'].includes(locale) ? locale : 'en';
    const parts = seminarDate.split(' - ');
    const startDateString = parts[0];
    const endDateString = parts[1];

    const startMoment = moment(startDateString, 'DD MMM YY', 'en').locale(
      currentLocale,
    );
    const endMoment = moment(endDateString, 'DD MMM YY', 'en').locale(
      currentLocale,
    );

    if (!startMoment.isValid() || !endMoment.isValid()) {
      console.warn('Invalid date format for cohort:', slot?.seminar_date);
      return slot?.seminar_date; // Fallback to original string
    }

    // Output format: "D MMM" (e.g., "5 May")
    const formatString = 'D MMM';

    const formattedStart = startMoment.format(formatString);
    const formattedEnd = endMoment.format(formatString);

    return `${formattedStart} - ${formattedEnd}`;
  }, [slot?.seminar_date, locale]);

  useEffect(() => {
    if (isOpen) {
      dialogRef.current?.showModal();
    } else {
      dialogRef.current?.close();
    }
  }, [isOpen]);

  // Return JSX
  return (
    <dialog
      ref={dialogRef}
      className={
        styles.confirmationDialog +
        ` ${isOpen ? styles.confirmationDialogOpened : ''}`
      }
    >
      <div className={styles.confirmationDialogTop}>
        <Typography as="h2" className={styles.confirmationDialogTitle}>
          {title}
        </Typography>
        {message && (
          <Typography as="p" className={styles.confirmationDialogMessage}>
            {message}
          </Typography>
        )}
        {slot && (
          <div className={styles.slot}>
            <div className={styles.slotItem}>
              <Clock />

              <span className={styles.dateText}>{slot?.name}</span>
            </div>
            <div className={styles.slotItem}>
              <CalendarRounded />

              <span className={styles.dateText}>{formatedSessionDate}</span>
            </div>
          </div>
        )}
      </div>
      <div className={styles.confirmationDialogActions}>
        <Button
          dictionary={dictionary.close}
          className={styles.confirmationDialogClose}
          onClick={() => {
            onClose();
            if (refreshData) {
              onFetch(courseId);
            }
          }}
        />
      </div>
    </dialog>
  );
};

export default ConfirmationDialog;
