import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import serverFetcher from '@/utils/fetcher/server-fetcher';

const query = `
  query mobile_findlearning_enrolment_info($courseid: core_id!) {
    enrolmentInfo: core_enrol_course_info(courseid: $courseid) {
      isComplete: is_complete
      isEnrolled: is_enrolled
      guestAccess: guest_access
      canEnrol: can_enrol
      priviledged: can_view
      enrolmentOptions: enrolment_options {
        id
        type
        roleName: role_name
        customName: custom_name
        sortOrder: sort_order
        passwordRequired: password_required
      }
    }
  }
`;

// Route to get the favorite courses
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const id = request.nextUrl.searchParams.get('id') || '';

    // Fetch the data
    const response = await serverFetcher.query({
      operationName: 'mobile_findlearning_enrolment_info',
      query: query,
      variables: {
        courseid: id,
      },
    });

    // Return the response
    return NextResponse.json({
      ...response?.data?.data?.enrolmentInfo,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
