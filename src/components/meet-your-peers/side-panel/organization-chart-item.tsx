import profileImage from '@public/images/job-profile-image.jpg';

import Image from 'next/image';
import styles from './style.module.scss';
import Typography from '@/ui/typography';
import { FC } from 'react';

interface IOrganizationChartItem {
  fullname: string;
  work: string;
  onClick: () => void;
}

const OrganizationChartItem: FC<IOrganizationChartItem> = (props) => {
  const { fullname, work, onClick } = props;

  const handleClick = () => {
    onClick();
  };

  return (
    <>
      <div className={styles.verticalLine}></div>
      <div className={styles.itemChart} onClick={handleClick}>
        <div className={styles.itemChartImage}>
          <Image
            src={profileImage}
            alt=""
            className="img-fluid"
            height={100}
            width={100}
          />
        </div>
        <div className={styles.itemChartData}>
          <Typography as="h4" className={styles.name}>
            {fullname}
          </Typography>
          <Typography as="p" className={styles.role}>
            {work}
          </Typography>
        </div>
      </div>
    </>
  );
};
export default OrganizationChartItem;
