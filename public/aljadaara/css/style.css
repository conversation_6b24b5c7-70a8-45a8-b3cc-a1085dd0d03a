body, html {
  margin: 0px;
  background-color: #F2EDEA;
  padding: 0px;
  overflow-x: hidden; }

body * {
  box-sizing: border-box;
  font-family: "Helvetica" ,sans-serif; }

@font-face {
  font-family: "Helvetica";
  src: url("../fonts/helvetica_regular.otf");
  font-weight: normal;
  font-style: normal; }
@font-face {
  font-family: "Codecpro";
  src: url("../fonts/CodecPro-Bold.ttf");
  font-weight: normal;
  font-style: normal; }
@font-face {
  font-family: "Helvetica Neue";
  src: url("../fonts/HelveticaNeue-Roman.otf");
  font-weight: normal;
  font-style: normal; }
@font-face {
  font-family: "Helvetica Neue";
  src: url("../fonts/helvetica-neue-thin.ttf");
  font-weight: 100;
  font-style: normal; }
@font-face {
  font-family: "Helvetica";
  src: url("../fonts/helvetica_light.otf");
  font-weight: 300;
  font-style: normal; }
@font-face {
  font-family: "Helvetica";
  src: url("../fonts/helvetica_bold.otf");
  font-weight: bold;
  font-style: normal; }
.container {
  max-width: 1710px;
  width: 100%;
  padding-left: 24px;
  padding-right: 24px;
  margin-left: auto;
  margin-right: auto; }

.faq__wrapper {
  position: relative;
  padding-top: 200px;
  padding-bottom: 160px; }
  .faq__wrapper:before {
    content: "";
    position: absolute;
    left: 0px;
    bottom: 0px;
    width: 100%;
    height: calc(100% + 400px);
    background: linear-gradient(to top, #7CA1C3, #F2EDEA); }
  .faq__wrapper .faq__inner {
    position: relative;
    z-index: 2; }
    .faq__wrapper .faq__inner > .elem {
      border-top: 2px solid #D8D8D8;
      padding-top: 80px;
      margin-bottom: 120px;
      display: flex;
      align-items: flex-start;
      justify-content: space-between; }
      .faq__wrapper .faq__inner > .elem > .list {
        max-width: 1032px;
        width: 100%; }
        .faq__wrapper .faq__inner > .elem > .list > .faq__element {
          border-top: 1px solid #D9D9D9; }
          .faq__wrapper .faq__inner > .elem > .list > .faq__element.opened > .head > .icon > span:nth-child(2) {
            opacity: 0; }
          .faq__wrapper .faq__inner > .elem > .list > .faq__element .content {
            display: none;
            padding-bottom: 24px; }
            .faq__wrapper .faq__inner > .elem > .list > .faq__element .content ul {
              padding: 0px;
              list-style-type: none;
              margin: 0px;
              margin-bottom: 25px; }
              .faq__wrapper .faq__inner > .elem > .list > .faq__element .content ul.numbered {
                list-style-type: decimal;
                padding-left: 25px; }
              .faq__wrapper .faq__inner > .elem > .list > .faq__element .content ul:last-child {
                margin-bottom: 0px; }
              .faq__wrapper .faq__inner > .elem > .list > .faq__element .content ul > li {
                margin-bottom: 0px;
                font-size: 22px;
                line-height: 140%;
                font-weight: 300;
                color: #000000; }
                .faq__wrapper .faq__inner > .elem > .list > .faq__element .content ul > li span {
                  font-weight: bold; }
                .faq__wrapper .faq__inner > .elem > .list > .faq__element .content ul > li:last-child {
                  margin-bottom: 0px; }
            .faq__wrapper .faq__inner > .elem > .list > .faq__element .content > p {
              margin: 0px;
              font-size: 22px;
              line-height: 140%;
              font-weight: 300;
              color: #000000;
              margin-bottom: 25px; }
              .faq__wrapper .faq__inner > .elem > .list > .faq__element .content > p a {
                font-weight: bold;
                color: #000;
                text-decoration: underline; }
              .faq__wrapper .faq__inner > .elem > .list > .faq__element .content > p span {
                font-weight: bold; }
              .faq__wrapper .faq__inner > .elem > .list > .faq__element .content > p:last-child {
                margin-bottom: 0px; }
          .faq__wrapper .faq__inner > .elem > .list > .faq__element .head {
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 24px 0px; }
            .faq__wrapper .faq__inner > .elem > .list > .faq__element .head .icon {
              display: inline-flex;
              align-items: center;
              justify-content: center;
              position: relative;
              min-width: 20px;
              max-width: 20px;
              min-height: 20px;
              max-height: 20px; }
              .faq__wrapper .faq__inner > .elem > .list > .faq__element .head .icon > span:nth-child(2) {
                min-height: 18px;
                min-width: 2px;
                background-color: #000;
                position: absolute;
                transition: .3s ease all; }
              .faq__wrapper .faq__inner > .elem > .list > .faq__element .head .icon > span:nth-child(1) {
                min-width: 18px;
                max-width: 18px;
                min-height: 2px;
                background-color: #000000;
                position: absolute; }
            .faq__wrapper .faq__inner > .elem > .list > .faq__element .head > p {
              margin: 0px;
              font-size: 24px;
              line-height: 28px;
              color: #000000;
              font-weight: 400;
              font-family: "Helvetica Neue" ,sans-serif; }
      .faq__wrapper .faq__inner > .elem > p {
        white-space: nowrap;
        margin: 0px;
        margin-right: 60px;
        font-size: 24px;
        line-height: 28px;
        color: #000000;
        font-weight: 400;
        text-transform: uppercase; }
      .faq__wrapper .faq__inner > .elem:last-child {
        margin-block: 0px; }
  .faq__wrapper .title {
    position: relative;
    z-index: 2;
    margin-bottom: 90px; }
    .faq__wrapper .title > h6 {
      margin: 0px;
      font-size: 72px;
      line-height: 72px;
      color: #000000;
      font-weight: 300; }

.faq__wrapper .faq__inner > .elem > .list > .faq__element .content {
  max-width: 840px; }

.faq__wrapper .faq__inner > .elem > .list > .faq__element .head .icon {
  margin-left: 15px; }

.contact__us {
  padding-top: 80px;
  position: relative;
  z-index: 2;
  padding-bottom: 30px; }
  .contact__us video {
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    border-radius: 10px;
    object-fit: cover; }
  .contact__us .outer__contact {
    min-height: 0px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    padding: 60px;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    border: 1px solid #fff;
    background: linear-gradient(to bottom right, #7CA1C3, #F2EDEA); }
    .contact__us .outer__contact:before {
      background: radial-gradient(circle, #f2edea 0%, #f2edea 100%);
      content: "";
      backdrop-filter: blur(30px);
      opacity: .6;
      position: absolute;
      left: 0px;
      top: 0px;
      width: 100%;
      height: 100%; }
    .contact__us .outer__contact .btn {
      position: relative;
      z-index: 2; }
      .contact__us .outer__contact .btn > a {
        display: inline-flex;
        text-decoration: underline;
        font-size: 40px;
        line-height: 48px;
        color: #000000;
        font-weight: bold; }
    .contact__us .outer__contact .top {
      position: relative;
      z-index: 2;
      display: flex;
      flex-direction: column;
      align-items: flex-start; }
      .contact__us .outer__contact .top > span {
        display: block;
        margin-bottom: 55px;
        font-size: 24px;
        line-height: 28px;
        color: #000000;
        font-weight: 400;
        text-transform: uppercase; }
      .contact__us .outer__contact .top > p {
        margin: 0px;
        font-size: 40px;
        line-height: 55px;
        font-weight: 300;
        color: #000000; }

.process__wrapper {
  padding-bottom: 70px;
  padding-top: 250px;
  position: relative; }
  .process__wrapper > img {
    position: absolute;
    left: 0px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none; }
  .process__wrapper .outer__process {
    z-index: 2;
    position: relative; }
    .process__wrapper .outer__process .progress__wrapper {
      max-width: 1550px;
      margin-left: auto;
      margin-right: auto;
      position: relative; }
    .process__wrapper .outer__process .step > span {
      display: inline-flex;
      margin-bottom: 15px;
      font-size: 20px;
      line-height: 30px;
      color: #000000;
      font-weight: 500;
      text-transform: uppercase; }
    .process__wrapper .outer__process .step > p {
      margin: 0px;
      font-size: 22px;
      line-height: 140%;
      font-weight: 300;
      color: #000000; }
    .process__wrapper .outer__process .step > h6 {
      margin-top: 0px;
      margin-bottom: 8px;
      font-size: 36px;
      line-height: 44px;
      font-weight: 400;
      font-family: "Helvetica Neue" ,sans-serif; }
    .process__wrapper .outer__process .step.first {
      position: absolute;
      max-width: 535px;
      top: 22px;
      right: 145px; }
    .process__wrapper .outer__process .step.last {
      padding-left: 65px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: -200px; }
      .process__wrapper .outer__process .step.last .inn {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between; }
        .process__wrapper .outer__process .step.last .inn .video__ {
          display: flex;
          align-items: center;
          justify-content: center; }
          .process__wrapper .outer__process .step.last .inn .video__ > img {
            max-width: 740px;
            border-radius: 10px;
            height: 565px;
            object-fit: cover; }
        .process__wrapper .outer__process .step.last .inn .left > p {
          margin: 0px;
          font-size: 30px;
          line-height: 36px;
          font-weight: 300;
          color: #000000;
          margin-top: 80px;
          margin-bottom: 80px; }
        .process__wrapper .outer__process .step.last .inn .left .btn {
          display: flex;
          justify-content: flex-start;
          align-items: center; }
          .process__wrapper .outer__process .step.last .inn .left .btn > a {
            display: inline-flex;
            min-height: 64px;
            padding-left: 40px;
            padding-right: 40px;
            font-size: 28px;
            line-height: 36px;
            color: #000000;
            text-transform: uppercase;
            border: 1px solid #000000;
            border-radius: 250px;
            text-decoration: none;
            font-family: "Helvetica Neue" ,sans-serif;
            transition: .3s ease all;
            justify-content: center;
            align-items: center; }
            .process__wrapper .outer__process .step.last .inn .left .btn > a:hover {
              background-color: #000;
              color: #fff; }
        .process__wrapper .outer__process .step.last .inn .left > h6 {
          margin-top: 0px;
          margin-bottom: 45px;
          font-size: 72px;
          line-height: 125%;
          color: #000000;
          font-weight: 300; }
          .process__wrapper .outer__process .step.last .inn .left > h6 span {
            font-weight: bold; }
    .process__wrapper .outer__process .step.second {
      position: absolute;
      max-width: 535px;
      top: 535px;
      left: 480px; }
    .process__wrapper .outer__process .media {
      display: flex;
      justify-content: center;
      align-items: center; }
      .process__wrapper .outer__process .media img {
        min-width: 1550px;
        max-width: 1550px; }
    .process__wrapper .outer__process > .top {
      text-align: center;
      max-width: 700px;
      margin-left: auto;
      margin-right: auto;
      margin-bottom: 95px; }
      .process__wrapper .outer__process > .top > h6 {
        margin-top: 0px;
        margin-bottom: 30px;
        font-size: 72px;
        line-height: 72px;
        color: #000000;
        font-weight: 300; }
      .process__wrapper .outer__process > .top > p {
        margin: 0px;
        font-size: 30px;
        line-height: 38px;
        color: #000000;
        font-weight: 300; }

.candidate__info {
  padding-top: 100px;
  padding-bottom: 70px; }
  .candidate__info .outer__candidate .list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: 205px; }
    .candidate__info .outer__candidate .list > .el {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 34px 0px;
      border-bottom: 1px solid #B4AFA5; }
      .candidate__info .outer__candidate .list > .el > p {
        margin: 0px;
        font-size: 22px;
        line-height: 140%;
        font-weight: 300;
        color: #000000; }
      .candidate__info .outer__candidate .list > .el > span {
        min-width: 40px;
        align-items: center;
        justify-content: center;
        max-width: 40px;
        display: inline-flex;
        margin-right: 32px; }
        .candidate__info .outer__candidate .list > .el > span img {
          max-width: 100%; }
  .candidate__info .outer__candidate > h6 {
    margin-top: 0px;
    margin-bottom: 90px;
    font-size: 72px;
    line-height: 72px;
    color: #000000;
    font-weight: 300; }

.key__principles {
  padding: 125px 0px; }
  .key__principles .outer__principles {
    position: relative;
    z-index: 2; }
    .key__principles .outer__principles > h6 {
      margin-top: 0px;
      margin-bottom: 80px;
      font-size: 72px;
      line-height: 72px;
      font-weight: 300;
      color: #000000; }
    .key__principles .outer__principles .grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-column-gap: 199px;
      grid-row-gap: 105px; }
      .key__principles .outer__principles .grid .elem {
        border-top: 1px solid #B4AFA5;
        padding-top: 34px; }
        .key__principles .outer__principles .grid .elem > p {
          margin: 0px;
          font-size: 22px;
          line-height: 120%;
          font-weight: 300;
          color: #000000; }

.program__vision {
  padding-top: 125px;
  padding-bottom: 70px; }
  .program__vision .outer__vision {
    display: flex;
    align-items: flex-start;
    justify-content: space-between; }
    .program__vision .outer__vision > h2 {
      margin: 0px;
      margin-right: 45px;
      font-size: 72px;
      line-height: 72px;
      color: #000000;
      font-weight: 300; }
    .program__vision .outer__vision > p {
      margin: 0px;
      font-size: 40px;
      max-width: 910px;
      line-height: 55px;
      font-weight: 300;
      font-size: 40px;
      color: #000000; }
      .program__vision .outer__vision > p span {
        font-weight: bold; }

.objectives {
  padding-top: 70px;
  padding-bottom: 100px;
  position: relative;
  overflow: hidden; }
  .objectives > img {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    margin-left: 190px; }
  .objectives .outer__objectives {
    padding: 80px 60px;
    border-radius: 10px;
    position: relative; }
    .objectives .outer__objectives .grid {
      position: relative;
      z-index: 2;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-column-gap: 40px; }
      .objectives .outer__objectives .grid > .el {
        padding-right: 30px;
        padding-left: 32px;
        padding-top: 60px;
        padding-bottom: 25px;
        border-left: 1px solid #000000; }
        .objectives .outer__objectives .grid > .el > span {
          display: inline-flex;
          margin-bottom: 40px;
          align-items: center;
          justify-content: center; }
        .objectives .outer__objectives .grid > .el > h6 {
          margin-top: 0px;
          margin-bottom: 30px;
          font-size: 36px;
          line-height: 44px;
          font-weight: 400;
          font-family: "Helvetica Neue" ,sans-serif;
          color: #000000; }
        .objectives .outer__objectives .grid > .el > p {
          margin: 0px;
          font-size: 22px;
          line-height: 140%;
          font-weight: 300;
          color: #000000;
          max-width: 395px; }
    .objectives .outer__objectives:before {
      content: "";
      position: absolute;
      left: 0px;
      top: 0px;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle, #7DA1C4 0%, #F2EDEA 120%);
      backdrop-filter: blur(20px);
      opacity: .5;
      border-radius: 10px; }
    .objectives .outer__objectives > .top {
      margin-bottom: 80px;
      position: relative;
      z-index: 2; }
      .objectives .outer__objectives > .top > h3 {
        margin-top: 0px;
        margin-bottom: 15px;
        font-size: 72px;
        line-height: 72px;
        font-weight: 300;
        color: #000000; }
      .objectives .outer__objectives > .top > p {
        margin: 0px;
        color: #000000;
        font-size: 30px;
        line-height: 36px;
        font-weight: 300; }

.partners {
  padding-top: 70px;
  padding-bottom: 120px;
  position: relative; }
  .partners > img {
    position: absolute;
    left: 0px;
    bottom: -350px; }
  .partners .outer__partners {
    width: 100%;
    padding-top: 90px;
    padding-bottom: 120px;
    position: relative;
    border-radius: 10px;
    padding-left: 24px;
    padding-right: 24px; }
    .partners .outer__partners:before {
      border-radius: 10px;
      content: "";
      position: absolute;
      left: 0px;
      top: 0px;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle, #7DA1C4 0%, #F2EDEA 100%);
      opacity: .5; }
    .partners .outer__partners .inn__box {
      max-width: 1300px;
      position: relative;
      z-index: 2;
      margin-left: auto;
      margin-right: auto;
      text-align: center; }
      .partners .outer__partners .inn__box > p {
        margin: 0px;
        font-size: 40px;
        line-height: 55px;
        font-weight: 300;
        color: #000000; }
      .partners .outer__partners .inn__box > h6 {
        margin-top: 0px;
        margin-bottom: 55px;
        font-size: 72px;
        line-height: 72px;
        color: #000000;
        font-weight: 300; }

.hero__section {
  padding: 20px;
  opacity: 0;
  transition: .6s ease all; }
  .hero__section .float__btn {
    position: absolute;
    right: 40px;
    z-index: 3;
    top: 55px; }
    .hero__section .float__btn > a {
      font-family: "Codecpro" ,sans-serif;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      text-decoration: none;
      min-height: 40px;
      border-radius: 250px;
      border: 1px solid #FFFFFF;
      text-decoration: none;
      padding-left: 20px;
      padding-right: 20px;
      font-size: 16px;
      line-height: 24px;
      font-weight: bold;
      color: #FFFFFF;
      transition: .3s ease all; }
      .hero__section .float__btn > a:hover {
        background-color: #fff;
        color: #000; }
  .hero__section > .inner__hero {
    position: relative;
    display: flex;
    width: 100%;
    min-height: calc(100vh - 40px); }
    .hero__section > .inner__hero .launch__date {
      z-index: 3;
      top: auto;
      position: absolute;
      right: 40px;
      bottom: 65px; }
      .hero__section > .inner__hero .launch__date > p {
        font-size: 20px;
        line-height: 24px;
        font-weight: 300;
        color: #000000;
        margin: 0px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-height: 60px;
        padding-left: 30px;
        padding-right: 30px;
        border-radius: 250px;
        border-radius: 250px;
        background-color: rgba(255, 255, 255, 0.8); }
        .hero__section > .inner__hero .launch__date > p img {
          margin: 0px 12px; }
        .hero__section > .inner__hero .launch__date > p span {
          font-weight: bold; }
    .hero__section > .inner__hero .float__logos {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      position: absolute;
      top: 58px;
      left: 43px;
      z-index: 3; }
      .hero__section > .inner__hero .float__logos > img {
        max-height: 45px;
        margin-right: 40px; }
        .hero__section > .inner__hero .float__logos > img:last-child {
          margin-right: 0px; }
    .hero__section > .inner__hero .container {
      max-width: 100%;
      padding-left: 40px;
      padding-right: 40px; }
    .hero__section > .inner__hero .top__head {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      top: 40px;
      z-index: 3;
      border-radius: 70px;
      background-color: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24px 32px; }
      .hero__section > .inner__hero .top__head .logo {
        margin: 0px 30px;
        display: inline-flex;
        align-items: center;
        justify-content: center; }
        .hero__section > .inner__hero .top__head .logo > a {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          transition: .3s ease all; }
          .hero__section > .inner__hero .top__head .logo > a:hover {
            opacity: .7; }
      .hero__section > .inner__hero .top__head .icon {
        display: flex;
        min-width: 150px;
        max-width: 150px;
        justify-content: space-between;
        align-items: center; }
        .hero__section > .inner__hero .top__head .icon > a {
          font-size: 20px;
          line-height: 32px;
          color: #000000;
          font-family: "Helvetica Neue" ,sans-serif;
          font-weight: 400;
          text-decoration: none;
          transition: .3s ease all; }
          .hero__section > .inner__hero .top__head .icon > a:hover {
            opacity: .7; }
        .hero__section > .inner__hero .top__head .icon .spacer {
          min-width: 30px;
          min-height: 1px;
          background-color: #000000;
          display: inline-flex; }
    .hero__section > .inner__hero:before {
      content: "";
      position: absolute;
      left: 0px;
      top: 0px;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle, rgba(0, 0, 0, 0) 0%, black 100%);
      opacity: .4;
      z-index: 2;
      pointer-events: none;
      border-radius: 10px; }
    .hero__section > .inner__hero .bottom {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      position: absolute;
      left: 40px;
      bottom: 34px;
      z-index: 2;
      width: calc(100% - 80px); }
      .hero__section > .inner__hero .bottom .right {
        display: flex;
        align-items: center;
        justify-content: flex-end; }
        .hero__section > .inner__hero .bottom .right > img {
          max-width: 305px; }
      .hero__section > .inner__hero .bottom > a {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-height: 60px;
        border-radius: 250px;
        background-color: #FFFFFF;
        border: 1px solid #FFFFFF;
        text-decoration: none;
        font-size: 28px;
        line-height: 34px;
        font-weight: 400;
        color: #000000;
        min-width: 205px;
        text-transform: uppercase;
        padding-bottom: 2px; }
    .hero__section > .inner__hero .container {
      display: flex; }
    .hero__section > .inner__hero .outer__hero {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      position: relative;
      z-index: 2; }
      .hero__section > .inner__hero .outer__hero > h1 {
        margin: 0px;
        font-size: 105px;
        line-height: 110px;
        margin-top: -100px;
        color: #FFFFFF;
        font-weight: 100;
        font-family: "Helvetica Neue" ,sans-serif;
        letter-spacing: -0.01em; }
        .hero__section > .inner__hero .outer__hero > h1 .second__row {
          padding-left: 90px;
          font-weight: 100;
          font-family: "Helvetica Neue" ,sans-serif;
          position: relative;
          top: -20px; }
        .hero__section > .inner__hero .outer__hero > h1 .first__row {
          font-family: "Helvetica Neue" ,sans-serif;
          padding-left: 170px;
          font-weight: 400;
          color: #FFFFFF; }
    .hero__section > .inner__hero > video {
      position: absolute;
      left: 0px;
      top: 0px;
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 10px; }

.faq__wrapper:before {
  pointer-events: none; }

.process__wrapper > img {
  opacity: .7; }

.process__wrapper .container {
  position: relative;
  z-index: 2; }

.process__wrapper > img {
  pointer-events: none; }

.load__animation {
  position: fixed;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  z-index: 8; }
  .load__animation.loaded .inn .txtone {
    height: 120px; }
  .load__animation.loaded .inn .txttwo {
    top: 0px; }
  .load__animation .inn {
    position: relative;
    z-index: 2;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column; }
    .load__animation .inn .t__box {
      min-height: 120px;
      text-align: center;
      width: 100%;
      position: relative;
      overflow: hidden; }
    .load__animation .inn .bottom {
      position: absolute;
      left: 50%;
      transform: translateX(-50%) translateY(30%);
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      height: 400px;
      overflow: hidden;
      bottom: -200px;
      z-index: 2;
      opacity: 0;
      transition: 1.2s ease all; }
      .load__animation .inn .bottom.loaded {
        transform: translateX(-50%) translateY(0%);
        opacity: 1; }
      .load__animation .inn .bottom > p {
        margin-top: 0px;
        margin-bottom: 80px;
        font-size: 24px;
        line-height: 140%;
        color: #FFFFFF;
        font-weight: 400; }
      .load__animation .inn .bottom video {
        width: 750px;
        border-radius: 10px;
        object-fit: cover; }
    .load__animation .inn .txttwo {
      display: flex;
      justify-content: center;
      align-items: flex-start;
      overflow: hidden;
      transition: 1.2s ease all;
      position: relative;
      top: 120px; }
      .load__animation .inn .txttwo > h2 {
        margin-top: 0px;
        font-size: 105px;
        font-family: "Helvetica Neue" ,sans-serif;
        font-weight: 400;
        color: #fff;
        margin: 0px;
        white-space: nowrap; }
    .load__animation .inn .txtone {
      margin-bottom: 3px;
      overflow: hidden;
      height: 0px;
      transition: 1.2s ease all; }
      .load__animation .inn .txtone > h2 {
        margin-top: 0px;
        font-size: 105px;
        font-family: "Helvetica Neue" ,sans-serif;
        font-weight: 100;
        color: #fff;
        margin: 0px;
        white-space: nowrap; }
  .load__animation > video {
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    object-fit: cover; }

.float__menu, .float__menu--sub {
  position: fixed;
  top: -110px;
  transition: .3s ease all;
  left: 0px;
  width: 100%;
  z-index: 4; }
  .float__menu.visible, .float__menu--sub.visible {
    top: 15px; }
  .float__menu ul, .float__menu--sub ul {
    width: 100%;
    padding: 30px 60px;
    border-radius: 10px;
    list-style-type: none;
    margin: 0px;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between; }
    .float__menu ul > li, .float__menu--sub ul > li {
      width: 100%;
      text-align: center; }
      .float__menu ul > li > a, .float__menu--sub ul > li > a {
        font-size: 20px;
        font-weight: 300;
        line-height: 26px;
        color: #000000;
        text-decoration: none; }
        .float__menu ul > li > a.current, .float__menu--sub ul > li > a.current {
          font-weight: bold; }

.objectives .outer__objectives .grid > .el > span {
  min-height: 90px;
  margin-bottom: 60px; }

.objectives .outer__objectives .grid > .el {
  padding-top: 21px;
  padding-bottom: 23px; }

.contact__us .outer__contact .top > p {
  margin-bottom: 70px; }

.partners .outer__partners {
  padding: 160px 0px; }

.partners .outer__partners:before {
  content: none; }

.learn__info {
  padding-top: 100px;
  padding-bottom: 100px;
  position: relative; }
  .learn__info > img {
    position: absolute;
    left: 0px;
    top: -200px;
    pointer-events: none;
    opacity: .5; }
  .learn__info .outer__learn {
    position: relative;
    z-index: 2;
    display: flex;
    justify-content: space-between; }
    .learn__info .outer__learn .box {
      width: 100%;
      max-width: 1040px;
      padding: 60px;
      background: linear-gradient(to bottom right, rgba(250, 249, 249, 0.56), rgba(226, 223, 223, 0.67));
      backdrop-filter: blur(40px);
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-column-gap: 35px;
      grid-row-gap: 40px; }
      .learn__info .outer__learn .box > .el {
        padding: 32px 40px;
        background-color: rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(30px);
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px; }
        .learn__info .outer__learn .box > .el > h6 {
          margin-top: 0px;
          margin-bottom: 30px;
          font-size: 28px;
          line-height: 39px;
          color: #000000;
          font-weight: 400;
          font-family: "Helvetica Neue", sans-serif; }
        .learn__info .outer__learn .box > .el > p {
          margin: 0px;
          font-size: 22px;
          line-height: 110%;
          font-weight: 300;
          color: #000000; }
        .learn__info .outer__learn .box > .el > .img {
          display: inline-flex;
          align-items: center;
          min-height: 80px;
          max-height: 80px;
          margin-bottom: 45px;
          justify-content: center; }
          .learn__info .outer__learn .box > .el > .img img {
            max-height: 80px; }
    .learn__info .outer__learn .desc {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      max-width: 660px;
      margin-right: 40px; }
      .learn__info .outer__learn .desc .media {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 40px; }
        .learn__info .outer__learn .desc .media img {
          max-width: 100%;
          border-radius: 10px;
          object-fit: cover; }
      .learn__info .outer__learn .desc .top__part .btn {
        display: flex;
        justify-content: flex-start;
        margin-top: 40px; }
        .learn__info .outer__learn .desc .top__part .btn > a {
          display: inline-flex;
          min-height: 58px;
          padding-left: 40px;
          padding-right: 40px;
          font-size: 20px;
          line-height: 30px;
          color: #000000;
          text-transform: uppercase;
          border: 1px solid #000000;
          border-radius: 250px;
          text-decoration: none;
          font-family: "Helvetica Neue", sans-serif;
          transition: .3s ease all;
          justify-content: center;
          align-items: center; }
          .learn__info .outer__learn .desc .top__part .btn > a:hover {
            background-color: #000;
            color: #fff; }
      .learn__info .outer__learn .desc .top__part > p {
        margin: 0px;
        font-size: 30px;
        line-height: 38px;
        color: #000000;
        font-weight: 300; }
      .learn__info .outer__learn .desc .top__part > h6 {
        margin-top: 0px;
        margin-bottom: 25px;
        font-size: 72px;
        line-height: 72px;
        font-weight: 300;
        color: #000000; }

.program__benefits {
  padding: 100px 0px;
  position: relative; }
  .program__benefits > .right__ellipse {
    position: absolute;
    pointer-events: none;
    top: 240px;
    right: 0px;
    opacity: .6; }
  .program__benefits > .left__ellipse {
    position: absolute;
    left: 0px;
    pointer-events: none;
    top: 280px;
    opacity: .6; }
  .program__benefits .outer__program {
    position: relative;
    z-index: 2; }
    .program__benefits .outer__program .grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-column-gap: 25px; }
      .program__benefits .outer__program .grid > .elem {
        padding: 55px 40px;
        background-color: rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(40px);
        padding-bottom: 80px; }
        .program__benefits .outer__program .grid > .elem .desc > .el {
          margin-bottom: 32px;
          padding-bottom: 32px;
          border-bottom: 1px solid #B4AFA5; }
          .program__benefits .outer__program .grid > .elem .desc > .el:last-child {
            margin-bottom: 0px; }
          .program__benefits .outer__program .grid > .elem .desc > .el:last-child {
            border-bottom: 0px;
            padding-bottom: 0px; }
          .program__benefits .outer__program .grid > .elem .desc > .el > p {
            margin: 0px;
            font-size: 24px;
            line-height: 120%;
            color: #000000;
            font-weight: 300; }
        .program__benefits .outer__program .grid > .elem .desc > h6 {
          margin-top: 0px;
          margin-bottom: 30px;
          font-size: 28px;
          line-height: 39px;
          color: #000000;
          font-weight: 400;
          font-family: "Helvetica Neue" ,sans-serif; }
        .program__benefits .outer__program .grid > .elem > .media {
          display: inline-flex;
          align-items: center;
          margin-bottom: 60px;
          justify-content: center; }
          .program__benefits .outer__program .grid > .elem > .media img {
            min-height: 90px;
            max-height: 90px; }
    .program__benefits .outer__program > h6 {
      margin-top: 0px;
      margin-bottom: 80px;
      font-size: 72px;
      line-height: 72px;
      color: #000000;
      font-weight: 300; }

.who__can--apply {
  padding: 90px 0px;
  position: relative; }
  .who__can--apply > img {
    position: absolute;
    pointer-events: none;
    left: 50%;
    margin-left: 45px;
    transform: translateX(-50%);
    bottom: -250px; }
  .who__can--apply .outer__apply {
    position: relative; }
    .who__can--apply .outer__apply > h6 {
      margin-top: 0px;
      margin-bottom: 60px;
      font-size: 72px;
      line-height: 72px;
      color: #000000;
      font-weight: 300; }
    .who__can--apply .outer__apply .box {
      display: flex;
      border-radius: 10px;
      justify-content: space-between;
      background: linear-gradient(to bottom right, rgba(250, 249, 249, 0.56), rgba(226, 223, 223, 0.67));
      padding: 65px; }
      .who__can--apply .outer__apply .box .desc {
        width: 100%;
        max-width: 640px;
        margin-left: 50px; }
        .who__can--apply .outer__apply .box .desc .btn {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          margin-top: 70px; }
          .who__can--apply .outer__apply .box .desc .btn > a {
            display: inline-flex;
            min-height: 64px;
            padding-left: 40px;
            padding-right: 40px;
            font-size: 28px;
            line-height: 36px;
            color: #000000;
            text-transform: uppercase;
            border: 1px solid #000000;
            border-radius: 250px;
            text-decoration: none;
            font-family: "Helvetica Neue", sans-serif;
            transition: .3s ease all;
            justify-content: center;
            align-items: center; }
            .who__can--apply .outer__apply .box .desc .btn > a:hover {
              background-color: #000;
              color: #fff; }
        .who__can--apply .outer__apply .box .desc > .el {
          padding: 44px  0px;
          border-bottom: 1px solid #B4AFA5; }
          .who__can--apply .outer__apply .box .desc > .el > p {
            margin: 0px;
            font-size: 22px;
            line-height: 140%;
            font-weight: 300;
            color: #000000; }
      .who__can--apply .outer__apply .box .media {
        max-width: 820px;
        width: 100%; }
        .who__can--apply .outer__apply .box .media > img {
          max-width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 10px; }

.hero__sub .inner__hero {
  min-height: 0px;
  padding-top: 280px;
  padding-bottom: 320px; }
  .hero__sub .inner__hero .media {
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%; }
    .hero__sub .inner__hero .media > img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 10px; }

.look__forward {
  padding-top: 160px;
  position: relative; }
  .look__forward .outer__forward {
    position: relative;
    z-index: 3; }
    .look__forward .outer__forward .top__part {
      text-align: center;
      margin-bottom: 180px; }
      .look__forward .outer__forward .top__part > p {
        margin: 0px;
        font-size: 28px;
        line-height: 36px;
        color: #000000;
        font-weight: 400; }
      .look__forward .outer__forward .top__part > h6 {
        margin-top: 0px;
        margin-bottom: 45px;
        font-size: 72px;
        line-height: 99px;
        font-weight: 300;
        color: #000; }
        .look__forward .outer__forward .top__part > h6 span {
          font-weight: bold; }
  .look__forward > img {
    position: absolute;
    left: 0px;
    bottom: 0px;
    width: 100%;
    pointer-events: none; }
  .look__forward .outer__forward > .bottom {
    display: flex;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid rgba(0, 0, 0, 0.15);
    justify-content: space-between;
    padding-bottom: 30px; }
    .look__forward .outer__forward > .bottom > p {
      margin: 0px;
      font-size: 13px;
      line-height: 20px;
      color: #FFFFFF;
      font-weight: 400; }
    .look__forward .outer__forward > .bottom > a {
      text-decoration: none;
      font-size: 13px;
      line-height: 18px;
      color: #FFFFFF;
      transition: .3s ease all; }
      .look__forward .outer__forward > .bottom > a:hover {
        opacity: .7; }

.overview__program {
  padding-top: 90px;
  padding-bottom: 80px; }
  .overview__program .outer__overview {
    display: flex;
    position: relative;
    z-index: 2;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start; }
    .overview__program .outer__overview > p {
      margin: 0px;
      font-size: 24px;
      line-height: 140%;
      color: #000000;
      font-weight: 300;
      max-width: 510px;
      margin-left: auto; }
    .overview__program .outer__overview > h2 {
      margin-top: 0px;
      margin-bottom: 80px;
      max-width: 1080px;
      margin-right: auto;
      font-weight: 300;
      font-size: 72px;
      line-height: 72px;
      font-family: "Helvetica Neue" ,sans-serif; }
      .overview__program .outer__overview > h2 span {
        font-family: "Helvetica Neue" ,sans-serif;
        font-weight: 500; }

.learn__program {
  padding: 90px 0px;
  position: relative; }
  .learn__program > img {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: -350px;
    pointer-events: none; }
  .learn__program .outer__learn {
    padding: 60px;
    position: relative;
    border-radius: 10px;
    z-index: 2;
    background: radial-gradient(circle, #7DA1C4 0%, #F2EDEA 120%); }
    .learn__program .outer__learn .grid {
      max-width: 1560px;
      margin-left: auto;
      margin-right: auto;
      width: 100%;
      display: flex;
      justify-content: space-between;
      padding-left: 60px;
      padding-right: 60px; }
      .learn__program .outer__learn .grid > .elem {
        max-width: 380px; }
        .learn__program .outer__learn .grid > .elem > h6 {
          margin-top: 0px;
          margin-bottom: 30px;
          font-size: 36px;
          line-height: 44px;
          font-family: "Helvetica Neue" ,sans-serif;
          color: #000000;
          font-weight: 400; }
        .learn__program .outer__learn .grid > .elem > p {
          margin: 0px;
          font-size: 22px;
          line-height: 140%;
          font-weight: 300;
          color: #000000; }
    .learn__program .outer__learn .media {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 80px; }
      .learn__program .outer__learn .media img {
        max-width: 100%;
        border-radius: 10px;
        object-fit: cover; }
    .learn__program .outer__learn > h6 {
      margin-top: 0px;
      margin-bottom: 40px;
      font-size: 72px;
      line-height: 72px;
      color: #000000;
      font-weight: 300; }

.engage__wrapper {
  padding: 90px 0px;
  position: relative; }
  .engage__wrapper > img {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none; }
  .engage__wrapper .outer__engage {
    position: relative;
    z-index: 2; }
    .engage__wrapper .outer__engage .grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-column-gap: 40px;
      grid-row-gap: 40px; }
      .engage__wrapper .outer__engage .grid > .el {
        background: linear-gradient(to bottom right, rgba(250, 249, 249, 0.56), rgba(226, 223, 223, 0.67));
        backdrop-filter: blur(20px);
        padding: 60px; }
        .engage__wrapper .outer__engage .grid > .el > span {
          display: inline-flex;
          margin-bottom: 50px;
          min-height: 95px;
          max-height: 95px;
          justify-content: flex-start;
          align-items: center; }
          .engage__wrapper .outer__engage .grid > .el > span img {
            max-height: 95px; }
        .engage__wrapper .outer__engage .grid > .el > p {
          margin: 0px;
          font-size: 22px;
          line-height: 28px;
          font-weight: 300;
          color: #000000; }
        .engage__wrapper .outer__engage .grid > .el > h6 {
          margin-top: 0px;
          margin-bottom: 25px;
          font-size: 36px;
          line-height: 44px;
          font-family: "Helvetica Neue" ,sans-serif;
          color: #000000;
          font-weight: 400; }
    .engage__wrapper .outer__engage > h6 {
      margin-top: 0px;
      margin-bottom: 50px;
      font-size: 72px;
      line-height: 72px;
      color: #000000;
      font-weight: 300; }

.implement__wrapper {
  padding: 90px 0px; }
  .implement__wrapper .outer__implement .boxes {
    display: grid;
    grid-template-columns: repeat(2, 1fr); }
    .implement__wrapper .outer__implement .boxes .box {
      padding: 60px; }
      .implement__wrapper .outer__implement .boxes .box:nth-child(1) {
        border-top-left-radius: 10px;
        border-bottom-left-radius: 10px;
        background-color: #E7DFDA;
        backdrop-filter: blur(20px); }
      .implement__wrapper .outer__implement .boxes .box:nth-child(2) {
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
        position: relative; }
        .implement__wrapper .outer__implement .boxes .box:nth-child(2) .text, .implement__wrapper .outer__implement .boxes .box:nth-child(2) .media {
          position: relative;
          z-index: 2; }
        .implement__wrapper .outer__implement .boxes .box:nth-child(2) > video {
          position: absolute;
          top: 0px;
          right: 0px;
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-top-right-radius: 10px;
          border-bottom-right-radius: 10px;
          opacity: .4; }
        .implement__wrapper .outer__implement .boxes .box:nth-child(2):before {
          content: "";
          position: absolute;
          z-index: 2px;
          top: 0px;
          left: 0px;
          width: 100%;
          height: 100%;
          background: linear-gradient(to bottom right, rgba(211, 205, 205, 0.56), rgba(211, 210, 210, 0.67));
          border-top-right-radius: 10px;
          border-bottom-right-radius: 10px; }
      .implement__wrapper .outer__implement .boxes .box .text > p {
        margin-top: 0px;
        margin-bottom: 30px;
        font-size: 36px;
        line-height: 44px;
        font-family: "Helvetica Neue" ,sans-serif;
        font-weight: 400;
        color: #000000; }
      .implement__wrapper .outer__implement .boxes .box .text > span {
        font-size: 22px;
        line-height: 140%;
        font-weight: 300;
        color: #000000;
        display: block;
        max-width: 640px; }
      .implement__wrapper .outer__implement .boxes .box .media {
        display: flex;
        align-items: center;
        justify-content: center; }
        .implement__wrapper .outer__implement .boxes .box .media:nth-child(1) {
          margin-bottom: 50px; }
        .implement__wrapper .outer__implement .boxes .box .media:nth-child(2) {
          margin-top: 50px; }
        .implement__wrapper .outer__implement .boxes .box .media > img {
          max-width: 100%;
          border-radius: 10px; }
      .implement__wrapper .outer__implement .boxes .box:nth-child(1) {
        padding-right: 45px; }
      .implement__wrapper .outer__implement .boxes .box:nth-child(2) {
        padding-right: 45px; }
  .implement__wrapper .outer__implement > h6 {
    margin-top: 0px;
    margin-bottom: 80px;
    font-size: 72px;
    line-height: 72px;
    font-weight: 300;
    color: #000000;
    padding-left: 60px; }

.outer__hero > h6 {
  margin-top: -100px;
  margin-bottom: 14px;
  font-size: 40px;
  line-height: 70px;
  color: #FFFFFF;
  font-weight: bold;
  font-family: "Helvetica Neue" ,sans-serif; }

.hero__section > .inner__hero .outer__hero > h1 {
  margin-top: 0px; }

.learn__info .outer__learn .box > .el > h6 {
  margin-bottom: 0px; }

footer {
  margin-top: 0px;
  padding-bottom: 10px;
  position: relative;
  top: 0px; }

.faq__wrapper {
  margin-bottom: -55px; }

footer .outer__footer {
  position: relative;
  z-index: 2;
  padding-top: 20px;
  padding-bottom: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid rgba(0, 0, 0, 0.15); }
  footer .outer__footer > p {
    margin: 0px;
    font-size: 13px;
    line-height: 20px;
    font-family: "Helvetica" ,sans-serif;
    font-weight: 400;
    color: #fff; }
  footer .outer__footer > a {
    text-decoration: none;
    display: inline-flex;
    font-size: 13px;
    line-height: 20px;
    color: #FFFFFF;
    font-family: "Helvetica" ,sans-serif; }

.process__more--wrapper {
  margin-top: 180px;
  width: calc(100% - 120px);
  margin-left: auto;
  margin-right: auto;
  background: radial-gradient(circle, #f2edea 0%, #f2edea 100%);
  border-radius: 10px; }

.process__wrapper {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-top: 150px;
  padding-bottom: 180px;
  position: relative; }
  .process__wrapper:before {
    content: "";
    border-radius: 10px;
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom right, #7CA1C3, #F2EDEA);
    opacity: .3; }

.process__wrapper .outer__process .step.first {
  top: 60px;
  right: 270px; }

.process__wrapper .outer__process .step.second {
  top: 350px;
  left: auto;
  right: 290px; }

.process__wrapper .outer__process .step.three {
  top: 745px;
  position: absolute;
  left: auto;
  right: 181px; }

.process__wrapper .outer__process .step.four {
  top: 785px;
  position: absolute;
  right: auto;
  left: 214px; }

.process__wrapper .outer__process .step.last {
  margin-top: -105px; }

.experiences__wrapper {
  padding-top: 240px;
  padding-bottom: 135px;
  position: relative; }
  .experiences__wrapper > img {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    pointer-events: none;
    opacity: .6;
    bottom: -200px; }
  .experiences__wrapper .experiences {
    width: calc(100% - 120px);
    margin-left: auto;
    margin-right: auto;
    position: relative;
    padding: 60px;
    z-index: 3; }
    .experiences__wrapper .experiences .outer__experiences {
      display: flex;
      position: relative;
      z-index: 3;
      justify-content: space-between; }
      .experiences__wrapper .experiences .outer__experiences .slider__wrapper {
        width: 100%;
        max-width: 445px;
        padding-top: 100px; }
        .experiences__wrapper .experiences .outer__experiences .slider__wrapper .btns {
          display: flex;
          justify-content: flex-start;
          margin-top: 45px; }
          .experiences__wrapper .experiences .outer__experiences .slider__wrapper .btns > a {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 54px;
            max-width: 54px;
            min-height: 54px;
            max-height: 54px;
            border-radius: 250px;
            margin-right: 10px;
            transition: .3s ease all;
            background-color: #D1D7DE; }
            .experiences__wrapper .experiences .outer__experiences .slider__wrapper .btns > a:last-child {
              margin-right: 0px; }
            .experiences__wrapper .experiences .outer__experiences .slider__wrapper .btns > a svg path {
              transition: .3s ease all; }
            .experiences__wrapper .experiences .outer__experiences .slider__wrapper .btns > a:hover {
              background-color: #989FA6; }
              .experiences__wrapper .experiences .outer__experiences .slider__wrapper .btns > a:hover svg path {
                stroke: #fff;
                fill: #fff; }
        .experiences__wrapper .experiences .outer__experiences .slider__wrapper .slick-slide {
          margin: 0 15px; }
        .experiences__wrapper .experiences .outer__experiences .slider__wrapper .slick-list {
          margin: 0 -15px; }
        .experiences__wrapper .experiences .outer__experiences .slider__wrapper .slick-prev, .experiences__wrapper .experiences .outer__experiences .slider__wrapper .slick-next {
          display: none !important; }
        .experiences__wrapper .experiences .outer__experiences .slider__wrapper .slide__inn > p {
          margin: 0px;
          font-size: 22px;
          line-height: 120%;
          color: #000000;
          font-weight: 300; }
        .experiences__wrapper .experiences .outer__experiences .slider__wrapper .slide__inn > h6 {
          margin-top: 0px;
          padding-top: 35px;
          padding-bottom: 35px;
          border-bottom: 1px solid #B4AFA5;
          font-size: 36px;
          line-height: 44px;
          font-family: "Helvetica Neue" ,sans-serif;
          color: #000000;
          margin-bottom: 25px;
          font-weight: 400; }
      .experiences__wrapper .experiences .outer__experiences .media {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        margin-right: 50px;
        max-width: 640px; }
        .experiences__wrapper .experiences .outer__experiences .media img {
          max-width: 100%;
          border-radius: 10px; }
      .experiences__wrapper .experiences .outer__experiences > .desc {
        max-width: 405px;
        display: flex;
        flex-direction: column;
        margin-right: 50px;
        justify-content: space-between; }
        .experiences__wrapper .experiences .outer__experiences > .desc > p {
          margin: 0px;
          font-size: 22px;
          line-height: 140%;
          font-weight: 300;
          color: #000000; }
        .experiences__wrapper .experiences .outer__experiences > .desc > h2 {
          margin-top: 0px;
          margin-bottom: 0px;
          font-size: 72px;
          line-height: 72px;
          color: #000000;
          font-weight: 300; }
    .experiences__wrapper .experiences:before {
      content: "";
      position: absolute;
      left: 0px;
      top: 0px;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle, #7DA1C4 0%, #F2EDEA 120%);
      opacity: .3;
      backdrop-filter: blur(40px);
      border-radius: 10px; }

.experiences__wrapper .experiences .outer__experiences > .desc > h2 {
  margin-bottom: 30px; }

.ready__ {
  padding-top: 120px;
  padding-bottom: 120px; }
  .ready__ .outer__ready .btn {
    display: flex;
    justify-content: center;
    align-items: center; }
  .ready__ .outer__ready .btn > a {
    display: inline-flex;
    min-height: 58px;
    padding-left: 40px;
    padding-right: 40px;
    font-size: 20px;
    line-height: 30px;
    color: #000000;
    text-transform: uppercase;
    border: 1px solid #000000;
    border-radius: 250px;
    text-decoration: none;
    font-family: "Helvetica Neue", sans-serif;
    transition: .3s ease all;
    justify-content: center;
    align-items: center; }
    .ready__ .outer__ready .btn > a:hover {
      background-color: #000;
      color: #fff; }
  .ready__ .outer__ready > h6 {
    text-align: center;
    margin-top: 0px;
    margin-bottom: 40px;
    font-size: 72px;
    line-height: 84px;
    color: #000000;
    font-weight: 300; }
    .ready__ .outer__ready > h6 span {
      font-weight: bold; }

@media (max-width: 1740px) {
  .experiences__wrapper {
    padding: 90px 0px; }

  .experiences__wrapper > img {
    max-height: 1000px; }

  .experiences__wrapper .experiences {
    width: calc(100% - 48px); }

  .experiences__wrapper .experiences .outer__experiences > .desc > h2 {
    font-size: 62px;
    line-height: 62px; }

  .process__more--wrapper {
    margin-top: 90px; }

  .process__more--wrapper {
    width: calc(100% - 50px); } }
@media (max-width: 1640px) {
  .experiences__wrapper .experiences .outer__experiences .media {
    margin-left: auto;
    margin-right: auto;
    margin-top: 30px;
    margin-bottom: 30px; }

  .experiences__wrapper .experiences .outer__experiences .slider__wrapper {
    padding-top: 0px;
    max-width: 100%; }

  .experiences__wrapper .experiences .outer__experiences > .desc {
    margin-right: 0px; }

  .experiences__wrapper .experiences .outer__experiences {
    flex-direction: column; }

  .experiences__wrapper .experiences .outer__experiences > .desc {
    max-width: 100%;
    margin-bottom: 25px;
    width: 100%; }

  .experiences__wrapper .experiences {
    padding: 35px; }

  .experiences__wrapper .experiences .outer__experiences > .desc > h2 {
    font-size: 52px;
    line-height: 52px; }

  .experiences__wrapper .experiences .outer__experiences .media img {
    min-width: 450px; }

  .implement__wrapper .outer__implement > h6 {
    padding-left: 0px; }

  .implement__wrapper {
    padding: 60px 0px; }

  .implement__wrapper .outer__implement .boxes .box {
    padding: 35px; }

  .implement__wrapper .outer__implement .boxes .box:nth-child(1) {
    padding-left: 35px; }

  .implement__wrapper .outer__implement .boxes .box:nth-child(2) {
    padding-right: 35px; }

  .engage__wrapper .outer__engage > h6, .implement__wrapper .outer__implement > h6 {
    font-size: 60px;
    line-height: 72px;
    margin-bottom: 25px; }

  .engage__wrapper {
    padding: 40px 0px; }

  .learn__program .outer__learn > h6 {
    font-size: 60px;
    line-height: 72px;
    margin-bottom: 25px; }

  .learn__program .outer__learn {
    padding: 35px; }

  .learn__program .outer__learn .grid > .elem > h6 {
    font-size: 30px;
    line-height: 38px;
    margin-bottom: 20px; }

  .learn__program .outer__learn .grid > .elem > p {
    font-size: 19px; }

  .learn__program .outer__learn .media {
    margin-bottom: 45px; }

  .learn__program .outer__learn .grid {
    padding-left: 10px;
    padding-right: 10px; }

  .learn__program .outer__learn .grid > .elem {
    max-width: 340px; }

  .look__forward .outer__forward .top__part > h6 {
    font-size: 60px;
    line-height: 72px;
    margin-bottom: 25px; }

  .overview__program .outer__overview > h2 {
    font-size: 60px;
    line-height: 72px; }

  .overview__program .outer__overview > p {
    font-size: 21px; }

  .overview__program {
    padding-top: 50px;
    padding-bottom: 50px; }

  .look__forward .outer__forward .top__part > p {
    font-size: 24px;
    line-height: 32px; }

  .look__forward {
    padding-top: 110px; }

  .look__forward .outer__forward .top__part {
    margin-bottom: 100px; }

  .learn__info {
    padding: 60px 0px; }

  .who__can--apply .outer__apply .box {
    padding: 40px; }

  .who__can--apply .outer__apply .box .desc {
    max-width: 580px; }

  .who__can--apply .outer__apply .box .desc > .el {
    padding: 30px  0px; }

  .who__can--apply .outer__apply > h6 {
    margin-bottom: 40px;
    font-size: 60px;
    line-height: 60px; }

  .who__can--apply {
    padding: 50px 0px; }

  .program__benefits .outer__program > h6 {
    font-size: 60px;
    line-height: 60px; }

  .program__benefits .outer__program > h6 {
    margin-bottom: 40px; }

  .program__benefits {
    padding: 50px 0px; }

  .program__benefits .outer__program .grid > .elem {
    padding: 35px 20px; }

  .learn__info .outer__learn .desc {
    max-width: 440px; }

  .learn__info .outer__learn .box > .el > .img {
    margin-bottom: 25px; }

  .learn__info .outer__learn .box > .el > h6 {
    margin-bottom: 20px;
    font-size: 24px;
    line-height: 32px; }

  .learn__info .outer__learn .box > .el > p {
    font-size: 19px; }

  .learn__info .outer__learn .box {
    padding: 35px; }

  .learn__info .outer__learn .box {
    grid-column-gap: 25px;
    grid-row-gap: 25px; }

  .learn__info .outer__learn .desc .top__part > h6 {
    font-size: 54px;
    line-height: 54px; }

  .learn__info .outer__learn .desc .top__part > p {
    font-size: 24px;
    line-height: 32px; }

  .learn__info .outer__learn .desc .top__part .btn {
    margin-top: 25px; }

  .candidate__info .outer__candidate > h6 {
    font-size: 60px;
    line-height: 60px;
    margin-bottom: 45px; }

  .candidate__info .outer__candidate .list > .el {
    padding: 20px 0px; }

  .candidate__info .outer__candidate .list {
    grid-column-gap: 80px; }

  .process__wrapper .outer__process .progress__wrapper {
    max-width: 1200px; }

  .process__wrapper .outer__process .step.last {
    padding-left: 50px; }

  .process__wrapper > img {
    opacity: .4; }

  .process__wrapper .outer__process .step.last .inn .left {
    position: relative;
    top: 0px; }

  .process__wrapper .outer__process .step.last .inn .left > h6 {
    font-size: 50px;
    margin-bottom: 25px; }

  .process__wrapper .outer__process .step.last .inn .video__ > img {
    max-width: 540px;
    height: 420px; }

  .process__wrapper .outer__process .step.last {
    padding-top: 50px; }

  .process__wrapper .outer__process .step.second {
    top: 390px;
    left: 545px; }

  .process__wrapper .outer__process .media img {
    min-width: 1200px;
    max-width: 1200px; }

  .process__wrapper .outer__process .step.first {
    top: 0px;
    right: 0px; }

  .process__wrapper .outer__process .step.second {
    left: 370px; }

  .process__wrapper .outer__process > .top > p {
    font-size: 24px;
    line-height: 32px; }

  .process__wrapper .outer__process > .top {
    margin-bottom: 45px; }

  .process__wrapper .outer__process .step > h6 {
    font-size: 30px;
    line-height: 38px; }

  .process__wrapper .outer__process .step > p {
    font-size: 19px; }

  .process__wrapper .outer__process > .top > h6 {
    font-size: 60px;
    line-height: 60px; }

  .process__wrapper {
    padding-top: 170px;
    padding-bottom: 50px; }

  .contact__us .outer__contact .top > p {
    font-size: 32px;
    line-height: 44px; }

  .contact__us .outer__contact .top > span {
    font-size: 21px;
    line-height: 25px; }

  .contact__us .outer__contact {
    padding: 35px; }

  .contact__us .outer__contact .btn > a {
    font-size: 32px;
    line-height: 40px; }

  .objectives .outer__objectives > .top {
    margin-bottom: 80px; }

  .key__principles .outer__principles .grid {
    grid-column-gap: 80px;
    grid-row-gap: 50px; }

  .key__principles {
    padding: 60px 0px; }

  .partners .outer__partners .inn__box > h6, .key__principles .outer__principles > h6 {
    font-size: 60px;
    line-height: 60px;
    margin-bottom: 35px; }

  .partners .outer__partners .inn__box > p {
    font-size: 32px;
    line-height: 44px; }

  .partners .outer__partners {
    padding-top: 60px;
    padding-bottom: 80px; }

  .partners {
    padding-top: 50px;
    padding-bottom: 70px; }

  .partners > img {
    max-width: 640px; }

  .partners .outer__partners .inn__box {
    max-width: 1040px; }

  .program__vision .outer__vision > h2, .objectives .outer__objectives > .top > h3 {
    font-size: 60px;
    line-height: 60px; }

  .program__vision .outer__vision > p {
    font-size: 32px;
    line-height: 44px; }

  .program__vision {
    padding-top: 70px;
    padding-bottom: 45px; }

  .process__wrapper .outer__process .step.first {
    top: 45px;
    right: 195px; }

  .process__wrapper .outer__process .step.second {
    left: auto;
    top: 265px;
    right: 167px; }

  .process__wrapper .outer__process .step.three {
    top: 560px;
    right: 98px; }

  .process__wrapper .outer__process .step.four {
    top: 598px;
    left: 163px; }

  .process__wrapper .outer__process .step.last .inn .left > p {
    font-size: 24px;
    line-height: 30px;
    margin: 50px 0px; }

  .process__wrapper .outer__process .step.last {
    padding-top: 22px; } }
.t__box:nth-child(1) {
  margin-top: -100px; }

@media (max-width: 1440px) {
  .outer__hero > h6 {
    font-size: 32px; }

  .program__benefits .outer__program .grid {
    grid-template-columns: repeat(2, 1fr);
    grid-row-gap: 25px; }

  .learn__info {
    padding: 30px 0px; }

  .learn__info .outer__learn {
    flex-direction: column; }

  .learn__info .outer__learn .desc {
    max-width: 740px;
    width: 100%;
    margin-left: auto;
    margin-right: auto; }

  .learn__info .outer__learn .box {
    max-width: 740px;
    margin-top: 35px;
    margin-left: auto;
    margin-right: auto; }

  .learn__info .outer__learn .box > .el {
    padding: 24px 32px; }

  .load__animation .inn .txtone > h2 {
    font-size: 85px; }

  .load__animation .inn .txttwo > h2 {
    font-size: 85px; }

  .load__animation.loaded .inn .txtone {
    height: 100px; }

  .load__animation .inn .t__box {
    min-height: 100px; }

  .load__animation .inn .bottom > p {
    font-size: 20px;
    margin-bottom: 35px; }

  .objectives .outer__objectives .grid > .el > p {
    font-size: 19px; }

  .objectives .outer__objectives .grid > .el > h6 {
    margin-bottom: 20px;
    font-size: 30px;
    line-height: 38px; }

  .objectives .outer__objectives .grid > .el > span {
    margin-bottom: 25px; }

  .objectives .outer__objectives .grid > .el {
    padding: 40px 20px; }

  .objectives .outer__objectives > .top > p {
    font-size: 24px;
    line-height: 30px; }

  .program__vision .outer__vision > p {
    font-size: 26px;
    line-height: 34px; }

  .program__vision .outer__vision > h2 {
    font-size: 50px;
    line-height: 50px;
    white-space: nowrap; }

  .hero__section > .inner__hero .outer__hero > h1 {
    margin-top: 0px; }

  .hero__section > .inner__hero .outer__hero > h1 {
    font-size: 70px;
    line-height: 77px; }

  .hero__section > .inner__hero .outer__hero > h1 .second__row {
    top: -8px; }

  .faq__wrapper .faq__inner > .elem > p {
    min-width: 280px; }

  .faq__wrapper .faq__inner > .elem > .list {
    max-width: 840px; }

  .faq__wrapper .title > h6 {
    font-size: 64px;
    line-height: 64px; }

  .faq__wrapper .title {
    margin-bottom: 50px; }

  .faq__wrapper {
    padding-top: 120px;
    padding-bottom: 80px; } }
@media (max-width: 1280px) {
  .ready__ {
    padding: 100px 0px; }

  .ready__ .outer__ready .btn > a {
    min-height: 50px;
    padding-left: 24px;
    padding-right: 24px;
    font-size: 24px;
    line-height: 30px; }

  .ready__ .outer__ready > h6 {
    font-size: 60px;
    line-height: 70px;
    margin-bottom: 25px; }

  .experiences__wrapper .experiences .outer__experiences > .desc > h2 {
    font-size: 42px;
    line-height: 48px; }

  .engage__wrapper .outer__engage > h6, .implement__wrapper .outer__implement > h6 {
    font-size: 48px;
    line-height: 60px; }

  .engage__wrapper .outer__engage .grid > .el {
    padding: 35px; }

  .engage__wrapper .outer__engage .grid > .el > span {
    margin-bottom: 25px; }

  .engage__wrapper .outer__engage .grid > .el > h6 {
    font-size: 30px;
    line-height: 36px;
    margin-bottom: 15px; }

  .learn__program {
    padding: 50px 0px; }

  .learn__program .outer__learn .grid {
    padding-left: 0px;
    padding-right: 0px; }

  .learn__program .outer__learn .grid {
    flex-direction: column;
    align-items: flex-start; }

  .learn__program .outer__learn .grid > .elem {
    max-width: 100%;
    padding-bottom: 25px;
    margin-bottom: 25px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.3); }
    .learn__program .outer__learn .grid > .elem:last-child {
      margin-bottom: 0px;
      padding-bottom: 0px;
      border-bottom: 0px; }

  .learn__program .outer__learn .grid > .elem > h6 br {
    display: none; }

  .learn__program .outer__learn > h6 {
    font-size: 48px;
    line-height: 60px; }

  .learn__program .outer__learn .grid > .elem > h6 {
    font-size: 24px;
    line-height: 30px;
    margin-bottom: 12px; }

  .overview__program .outer__overview > h2 {
    font-size: 48px;
    line-height: 60px;
    margin-bottom: 40px; }

  .overview__program .outer__overview > p {
    font-size: 19px; }

  .look__forward .outer__forward .top__part > h6 {
    font-size: 44px;
    line-height: 56px; }

  .look__forward .outer__forward .top__part > p {
    font-size: 21px;
    line-height: 28px; }

  .look__forward .outer__forward .top__part {
    margin-bottom: 60px; }

  .learn__info .outer__learn .desc .top__part .btn > a, .who__can--apply .outer__apply .box .desc .btn > a {
    min-height: 50px;
    padding-left: 24px;
    padding-right: 24px;
    font-size: 24px;
    line-height: 30px; }

  .who__can--apply .outer__apply .box {
    flex-direction: column; }

  .who__can--apply .outer__apply .box .media {
    max-width: 100%;
    margin-bottom: 35px; }

  .who__can--apply .outer__apply .box .desc {
    max-width: 100%;
    margin-left: 0px; }

  .float__menu, .float__menu--sub {
    display: none !important; }

  .program__benefits .outer__program .grid > .elem {
    backdrop-filter: none; }

  .process__wrapper {
    padding-top: 80px;
    padding-bottom: 35px; }

  .process__wrapper .outer__process > .top > h6 {
    font-size: 50px;
    line-height: 50px;
    margin-bottom: 15px; }

  .process__wrapper .outer__process .media img {
    min-width: 940px;
    max-width: 940px; }

  .process__wrapper .outer__process .step.first {
    max-width: 370px; }

  .process__wrapper .outer__process .step.first > span {
    font-size: 16px;
    margin-bottom: 10px; }
  .process__wrapper .outer__process .step.first > h6 {
    font-size: 19px;
    line-height: 25px; }
  .process__wrapper .outer__process .step.first > p {
    font-size: 16px; }

  .process__wrapper .outer__process > .top {
    margin-bottom: 25px; }

  .process__wrapper .outer__process .step > h6 {
    font-size: 24px;
    line-height: 30px; }

  .process__wrapper .outer__process .step.first {
    max-width: 440px; }

  .process__wrapper .outer__process .step.first {
    right: 0px;
    top: 6px; }

  .process__wrapper .outer__process .step.second {
    left: 270px;
    top: 297px; }

  .process__wrapper .outer__process .step.last .inn .left > h6 {
    font-size: 42px; }

  .process__wrapper .outer__process .step.last .inn .left .btn > a {
    min-height: 50px;
    padding-left: 24px;
    padding-right: 24px;
    font-size: 24px;
    line-height: 30px; }

  .process__wrapper .outer__process .step.last {
    padding-left: 50px; }

  .process__wrapper .outer__process .step.last .inn .video__ > img {
    max-width: 400px;
    height: 350px; }

  .process__wrapper .outer__process .step.last .inn {
    padding: 40px 0px; }

  .process__wrapper .outer__process .progress__wrapper {
    max-width: 940px; }

  .process__wrapper {
    padding-top: 45px; }

  .process__wrapper .outer__process .step.first {
    right: 133px;
    top: 38px; }

  .process__wrapper .outer__process .step.second {
    left: auto; }

  .process__wrapper .outer__process .step > h6 {
    font-size: 19px;
    line-height: 25px; }

  .process__wrapper .outer__process .step > p {
    font-size: 16px; }

  .process__wrapper .outer__process .step > h6 {
    margin-bottom: 3px; }

  .process__wrapper .outer__process .step.second {
    top: 215px;
    right: 71px; }

  .process__wrapper .outer__process .step.three {
    top: 450px;
    right: 12px; }

  .process__wrapper .outer__process .step.four {
    top: 452px;
    left: 88px; }

  .process__wrapper .outer__process .step.last .inn .left > p {
    margin: 25px 0px;
    font-size: 20px;
    line-height: 26px; }

  .process__wrapper .outer__process {
    max-width: 860px;
    margin-left: auto;
    margin-right: auto; }

  .process__wrapper .outer__process .step.first {
    right: 95px; }

  .process__wrapper .outer__process .step.four {
    max-width: 270px; } }
@media (max-width: 1200px) {
  .experiences__wrapper {
    padding: 45px 0px; }

  .ready__ .outer__ready > h6 {
    font-size: 50px;
    line-height: 58px; }

  .ready__ {
    padding: 60px 0px; }

  .experiences__wrapper .experiences {
    padding: 25px 0px; }

  .implement__wrapper .outer__implement .boxes .box {
    padding: 25px !important; }

  .implement__wrapper .outer__implement .boxes .box:nth-child(1) {
    border-top-right-radius: 10px !important;
    border-bottom-left-radius: 0px !important; }

  .implement__wrapper .outer__implement .boxes .box:nth-child(2):before {
    border-top-right-radius: 0px;
    border-bottom-left-radius: 10px; }

  .implement__wrapper .outer__implement .boxes .box:nth-child(2) {
    overflow: hidden; }

  .implement__wrapper .outer__implement .boxes .box:nth-child(2) {
    border-top-right-radius: 0px !important;
    border-bottom-left-radius: 10px !important; }

  .implement__wrapper .outer__implement .boxes {
    grid-template-columns: 1fr; }

  .implement__wrapper .outer__implement .boxes .box .media:nth-child(1) {
    margin-bottom: 25px; }

  .implement__wrapper .outer__implement .boxes .box .text > p {
    font-size: 30px;
    line-height: 38px;
    margin-bottom: 15px; }

  .implement__wrapper .outer__implement .boxes .box .media:nth-child(2) {
    margin-top: 25px; }

  .partners .outer__partners {
    padding: 50px 0px; }

  .contact__us .outer__contact .top > p br {
    display: none; }

  .load__animation .inn .txtone > h2 {
    font-size: 55px; }

  .load__animation .inn .txttwo > h2 {
    font-size: 55px; }

  .load__animation.loaded .inn .txtone {
    height: 65px; }

  .load__animation .inn .t__box {
    min-height: 65px; }

  .candidate__info .outer__candidate > h6 {
    margin-bottom: 25px;
    font-size: 50px;
    line-height: 50px; }

  .candidate__info {
    padding-top: 60px;
    padding-bottom: 45px; }

  .candidate__info .outer__candidate .list {
    grid-template-columns: 1fr; }

  .contact__us .outer__contact .top > p {
    font-size: 27px;
    line-height: 34px; }

  .contact__us .outer__contact .top > span {
    font-size: 18px;
    line-height: 22px; }

  .contact__us .outer__contact .btn > a {
    font-size: 26px;
    line-height: 34px; }

  .key__principles .outer__principles .grid {
    grid-row-gap: 40px;
    grid-column-gap: 40px; }

  .key__principles .outer__principles .grid .elem > p {
    font-size: 19px; }

  .partners .outer__partners .inn__box > h6 {
    font-size: 50px;
    line-height: 50px;
    margin-bottom: 25px; }

  .partners .outer__partners .inn__box > p {
    font-size: 27px;
    line-height: 34px; }

  .partners .outer__partners .inn__box {
    max-width: 940px; }

  .key__principles .outer__principles > h6 {
    font-size: 50px;
    line-height: 50px; }

  .objectives .outer__objectives > .top > h3 {
    font-size: 50px;
    line-height: 50px; }

  .objectives .outer__objectives {
    padding: 40px 30px; }

  .objectives .outer__objectives > .top {
    margin-bottom: 25px; }

  .objectives .outer__objectives > .top > p {
    font-size: 21px;
    line-height: 26px; }

  .objectives .outer__objectives .grid > .el {
    padding: 30px 0px; }

  .objectives .outer__objectives .grid > .el > p {
    max-width: 100%; }

  .objectives .outer__objectives .grid > .el > h6 {
    margin-bottom: 12px;
    font-size: 24px;
    line-height: 30px; }

  .objectives .outer__objectives .grid {
    grid-template-columns: 1fr; }

  .objectives .outer__objectives .grid > .el {
    border-left: 0px;
    border-bottom: 1px solid #000; }

  .program__vision .outer__vision {
    flex-direction: column;
    align-items: flex-start; }

  .program__vision {
    padding-top: 45px; }

  .program__vision .outer__vision > h2 {
    margin-right: 0px;
    margin-bottom: 20px; }

  .program__vision .outer__vision > p {
    max-width: 100%; }

  .hero__section > .inner__hero .outer__hero > h1 {
    font-size: 70px;
    line-height: 74px; }

  .hero__section > .inner__hero .outer__hero > h1 .first__row {
    padding-left: 90px; }

  .hero__section > .inner__hero .outer__hero > h1 .second__row {
    padding-left: 45px; }

  .hero__section > .inner__hero .bottom .right > img {
    max-width: 240px; }

  .faq__wrapper .faq__inner > .elem {
    flex-direction: column;
    align-items: flex-start; }

  .faq__wrapper .faq__inner > .elem > .list {
    max-width: 100%;
    margin-top: 30px; }

  .faq__wrapper .faq__inner > .elem {
    padding-top: 45px;
    margin-bottom: 70px; }

  .faq__wrapper .title > h6 {
    font-size: 56px;
    line-height: 56px; }

  .faq__wrapper .title {
    margin-bottom: 25px; }

  .faq__wrapper {
    padding-top: 80px;
    padding-bottom: 100px !important; }

  .faq__wrapper .faq__inner > .elem {
    padding-top: 35px;
    margin-bottom: 35px; }

  .contact__us .outer__contact .top > span {
    margin-bottom: 45px; }

  .contact__us .outer__contact .top > p {
    margin-bottom: 45px; } }
.progress__wrapper .line {
  display: none; }

.progress__wrapper > .step > img {
  display: none; }

@media (max-width: 991px) {
  .implement__wrapper .outer__implement > h6 {
    font-size: 40px;
    line-height: 48px; }

  .implement__wrapper {
    padding: 40px 0px; }

  .engage__wrapper .outer__engage > h6 {
    font-size: 40px;
    line-height: 48px; }

  .engage__wrapper .outer__engage .grid > .el > h6 {
    font-size: 26px;
    line-height: 32px; }

  .engage__wrapper .outer__engage .grid > .el > p {
    font-size: 19px;
    line-height: 25px; }

  .progress__wrapper > .step {
    top: 0px !important;
    left: 0px !important; }

  .process__wrapper .outer__process .step.four {
    margin-bottom: 45px; }

  .process__wrapper .outer__process .step.three {
    margin-bottom: 45px; }

  .outer__hero > h6 {
    margin-bottom: 0px;
    font-size: 25px;
    line-height: 40px; }

  .learn__program {
    padding: 35px 0px; }

  .learn__program .outer__learn > h6 {
    font-size: 40px;
    line-height: 48px; }

  .learn__program .outer__learn .grid > .elem > p {
    font-size: 16px;
    line-height: 20px; }

  .learn__program .outer__learn .grid > .elem > h6 {
    font-size: 21px;
    line-height: 25px; }

  .learn__program .outer__learn .media {
    margin-bottom: 25px; }

  .learn__program .outer__learn > h6 {
    margin-bottom: 15px; }

  .learn__program .outer__learn {
    padding: 20px 15px; }

  .overview__program .outer__overview > h2 {
    font-size: 40px;
    line-height: 48px; }

  .overview__program .outer__overview > p {
    font-size: 17px; }

  .look__forward .outer__forward .top__part > h6 br {
    display: none; }

  .hero__sub .inner__hero {
    padding-top: 120px;
    padding-bottom: 160px; }

  .hero__section > .inner__hero .float__logos {
    top: 33px;
    left: 20px; }
    .hero__section > .inner__hero .float__logos > img {
      max-height: 35px; }

  .look__forward .outer__forward .top__part > h6 {
    font-size: 36px;
    line-height: 42px; }

  .look__forward .outer__forward .top__part > p {
    font-size: 18px;
    line-height: 25px; }

  .look__forward .outer__forward .top__part {
    margin-bottom: 35px; }

  .look__forward .outer__forward > .bottom {
    padding-top: 10px;
    padding-bottom: 15px; }

  .hero__section > .inner__hero .launch__date {
    right: 20px;
    bottom: 20px; }

  .hero__section > .inner__hero .launch__date > p {
    font-size: 16px;
    line-height: 20px;
    min-height: 44px;
    padding-left: 12px;
    padding-right: 12px; }

  .who__can--apply .outer__apply > h6 {
    font-size: 40px;
    line-height: 40px;
    margin-bottom: 25px; }

  .who__can--apply .outer__apply .box {
    padding: 25px; }

  .who__can--apply .outer__apply .box {
    margin-bottom: 0px; }

  .who__can--apply .outer__apply .box .media {
    margin-bottom: 0px; }

  .who__can--apply .outer__apply .box .desc .btn {
    margin-top: 35px; }

  .program__benefits .outer__program .grid {
    grid-template-columns: 1fr; }

  .program__benefits .outer__program > h6 {
    font-size: 40px;
    line-height: 40px;
    margin-bottom: 25px; }

  .program__benefits .outer__program .grid > .elem .desc > .el {
    margin-bottom: 20px;
    padding-bottom: 20px; }

  .program__benefits .outer__program .grid > .elem .desc > .el > p {
    font-size: 20px; }

  .program__benefits .outer__program .grid > .elem .desc > h6 {
    margin-bottom: 22px;
    font-size: 24px;
    line-height: 29px; }

  .program__benefits .outer__program .grid > .elem > .media {
    margin-bottom: 30px; }

  .learn__info .outer__learn .box {
    backdrop-filter: none; }

  .learn__info .outer__learn .desc .top__part > h6 {
    font-size: 40px;
    line-height: 40px;
    margin-bottom: 12px; }

  .learn__info .outer__learn .desc .top__part > p {
    font-size: 20px;
    line-height: 28px; }

  .learn__info .outer__learn .desc {
    max-width: 100%; }

  .learn__info .outer__learn .box {
    max-width: 100%;
    margin-top: 25px; }

  .contact__us {
    padding-top: 0px; }

  .load__animation .inn .txtone > h2 {
    font-size: 34px; }

  .load__animation .inn .txttwo > h2 {
    font-size: 34px; }

  .load__animation.loaded .inn .txtone {
    height: 44px; }

  .load__animation .inn .t__box {
    min-height: 44px; }

  .load__animation .inn .bottom video {
    width: 440px; }

  .load__animation .inn .bottom {
    bottom: -280px; }

  .candidate__info {
    padding: 35px 0px; }

  .candidate__info .outer__candidate > h6 {
    font-size: 40px;
    line-height: 48px;
    margin-bottom: 15px; }

  .candidate__info .outer__candidate .list > .el > span {
    margin-right: 15px; }

  .candidate__info .outer__candidate .list > .el > p {
    font-size: 19px; }

  .line .curr {
    position: absolute;
    width: 100%;
    height: 15%;
    background: linear-gradient(to bottom, #F5EDE8, rgba(0, 0, 0, 0.7)); }

  .progress__wrapper > .step {
    position: relative !important;
    left: 0px !important;
    top: 0px !important; }

  .progress__wrapper > .step.last > img {
    top: 5px; }

  .progress__wrapper > .step > img {
    display: inline-flex;
    position: absolute;
    left: -39px;
    top: 1px; }

  .progress__wrapper {
    position: relative; }
    .progress__wrapper .line {
      position: absolute;
      width: 2px;
      height: 100%;
      left: 0px;
      top: 0px;
      display: inline-flex;
      background-color: #C9C0BB; }

  .process__wrapper {
    padding: 25px 0px; }

  .process__wrapper .outer__process .step.last .inn {
    padding: 0px; }

  .process__wrapper .outer__process .step.last .inn {
    display: block; }

  .process__wrapper .outer__process .step.last .inn .video__ {
    position: relative;
    height: 0px;
    padding-bottom: 75%;
    margin-top: 25px; }
    .process__wrapper .outer__process .step.last .inn .video__ video {
      position: absolute;
      left: 0px;
      top: 0px;
      width: 100%;
      height: 100%;
      max-width: 100%;
      border-radius: 10px; }

  .process__wrapper .outer__process .step.last .inn .left > h6 {
    font-size: 36px;
    line-height: 42px; }

  .process__wrapper .outer__process > .top > h6 {
    font-size: 40px;
    line-height: 48px; }

  .process__wrapper .outer__process > .top > p {
    font-size: 20px;
    line-height: 28px; }

  .process__wrapper .outer__process .progress__wrapper {
    position: relative;
    padding-left: 25px; }

  .process__wrapper .outer__process .step.first {
    margin-bottom: 45px; }

  .process__wrapper .outer__process .step.last {
    padding-left: 0px;
    padding-top: 0px;
    margin-top: 0px; }

  .process__wrapper .outer__process .media {
    display: none; }

  .process__wrapper .outer__process .step.first {
    position: static; }

  .process__wrapper .outer__process .step.second {
    position: static;
    margin-bottom: 45px; }

  .process__wrapper .outer__process .step.last {
    position: static; }

  .key__principles {
    padding: 40px 0px; }

  .key__principles .outer__principles .grid .elem > p {
    font-size: 17px; }

  .key__principles .outer__principles > h6 {
    font-size: 40px;
    line-height: 40px;
    margin-bottom: 20px; }

  .key__principles {
    padding: 30px 0px; }

  .key__principles .outer__principles .grid {
    grid-template-columns: repeat(2, 1fr); }

  .partners .outer__partners .inn__box > h6 {
    font-size: 40px;
    line-height: 40px; }

  .partners .outer__partners .inn__box > p {
    font-size: 24px;
    line-height: 32px; }

  .objectives > img {
    margin-left: 50px; }

  .objectives {
    padding-top: 20px;
    padding-bottom: 30px; }

  .objectives .outer__objectives > .top > h3 {
    font-size: 40px;
    line-height: 40px; }

  .objectives .outer__objectives > .top > p {
    font-size: 19px;
    line-height: 24px; }
    .objectives .outer__objectives > .top > p br {
      display: none; }

  .program__vision .outer__vision > h2 {
    font-size: 40px;
    line-height: 40px; }

  .program__vision .outer__vision > p {
    font-size: 22px;
    line-height: 28px; }

  .hero__section .float__btn {
    top: 33px; }

  .hero__section > .inner__hero .outer__hero > h1 {
    font-size: 50px;
    line-height: 56px; }

  .hero__section > .inner__hero .bottom {
    left: 20px;
    width: calc(100% - 40px);
    bottom: 20px; }

  .hero__section > .inner__hero .top__head .icon > a {
    font-size: 17px;
    line-height: 26px; }

  .hero__section > .inner__hero .bottom > a {
    min-height: 48px;
    font-size: 22px;
    line-height: 26px;
    min-width: 140px; }

  .hero__section > .inner__hero .bottom .right > img {
    max-width: 200px; }

  .hero__section > .inner__hero .top__head .icon {
    min-width: 115px;
    max-width: 115px; }

  .hero__section > .inner__hero .top__head {
    padding: 12px 20px; }

  .hero__section > .inner__hero .top__head .logo {
    margin: 0px 15px; }

  .hero__section > .inner__hero .top__head {
    top: 25px; }

  .hero__section > .inner__hero .top__head .icon .spacer:nth-child(2) {
    margin-left: 15px; }

  .hero__section > .inner__hero .top__head .icon .spacer:nth-child(1) {
    margin-right: 15px; }

  .faq__wrapper .faq__inner > .elem > .list {
    margin-top: 24px; }

  .faq__wrapper .faq__inner > .elem {
    padding-top: 24px;
    margin-bottom: 24px; }

  .faq__wrapper .faq__inner > .elem > .list > .faq__element .content > p {
    margin-bottom: 15px; }

  .faq__wrapper .faq__inner > .elem > .list > .faq__element .content > ul {
    margin-bottom: 15px; }

  .faq__wrapper .title > h6 {
    font-size: 48px;
    line-height: 48px; }

  .faq__wrapper .faq__inner > .elem > p {
    font-size: 20px;
    line-height: 24px; }

  .faq__wrapper .faq__inner > .elem > .list > .faq__element .head > p {
    font-size: 20px;
    line-height: 24px; }

  .faq__wrapper .faq__inner > .elem > .list > .faq__element .content > p {
    font-size: 18px; }

  .faq__wrapper .faq__inner > .elem > .list > .faq__element .content ul > li {
    font-size: 18px; }

  .hero__section > .inner__hero .container {
    padding-left: 20px;
    padding-right: 20px; }

  .process__wrapper .outer__process > .top {
    margin-bottom: 40px; }

  .process__wrapper .outer__process .step.last .inn .video__ {
    padding-bottom: 0px;
    height: auto;
    justify-content: flex-start; }

  .process__more--wrapper {
    margin-top: 40px; }

  .process__more--wrapper {
    width: calc(100% - 20px); }

  .process__wrapper .outer__process .step.last .inn .video__ > img {
    max-width: 400px;
    height: auto;
    width: 100%; } }
.contact__us .outer__contact .btn > a {
  word-break: break-all; }

@media (max-width: 767px) {
  .experiences__wrapper .experiences .outer__experiences > .desc > h2 {
    font-size: 36px;
    line-height: 40px;
    margin-bottom: 15px; }

  .experiences__wrapper .experiences .outer__experiences .media {
    margin: 20px 0px; }

  .experiences__wrapper .experiences .outer__experiences .media img {
    min-width: 0px;
    max-width: 450px;
    width: 100%; }

  .experiences__wrapper .experiences .outer__experiences .slider__wrapper .slide__inn > h6 {
    padding: 15px 0px; }

  .experiences__wrapper .experiences .outer__experiences .slider__wrapper .slide__inn > h6 {
    font-size: 28px;
    line-height: 34px; }

  .engage__wrapper .outer__engage .grid {
    grid-template-columns: 1fr;
    grid-row-gap: 24px; }

  .engage__wrapper .outer__engage .grid > .el > span img {
    max-height: 70px; }

  .engage__wrapper .outer__engage .grid > .el > span {
    min-height: 70px;
    max-height: 70px; }

  .learn__program .outer__learn .media img {
    height: 220px; }

  .overview__program .outer__overview > h2 {
    font-size: 32px;
    line-height: 40px;
    margin-bottom: 20px; }

  .overview__program {
    padding: 40px 0px; }

  .look__forward > img {
    min-width: 640px; }

  .look__forward .outer__forward > .bottom > p {
    font-size: 12px; }

  .look__forward .outer__forward > .bottom > a {
    font-size: 12px; }

  .look__forward {
    padding-top: 75px; }

  .look__forward .outer__forward .top__part > h6 {
    font-size: 30px;
    line-height: 36px; }

  .hero__section > .inner__hero .float__logos {
    top: 12px; }
    .hero__section > .inner__hero .float__logos > img {
      max-height: 25px;
      margin-right: 25px; }
      .hero__section > .inner__hero .float__logos > img:last-child {
        margin-bottom: 0px; }

  .who__can--apply .outer__apply .box {
    padding: 15px; }

  .who__can--apply .outer__apply .box .desc > .el > p {
    font-size: 19px; }

  .learn__info .outer__learn .box {
    padding: 20px; }

  .learn__info .outer__learn .box > .el > h6 {
    font-size: 21px;
    line-height: 28px;
    margin-bottom: 15px; }

  .learn__info .outer__learn .box > .el > .img {
    margin-bottom: 15px; }

  .learn__info .outer__learn .box > .el > p {
    font-size: 17px; }

  .learn__info .outer__learn .box {
    grid-template-columns: 1fr; }

  .contact__us .outer__contact .top > span {
    margin-bottom: 25px; }

  .contact__us .outer__contact .top > p {
    margin-bottom: 25px; }

  .contact__us .outer__contact .top > p br {
    display: none; }

  .contact__us .outer__contact .top > p {
    font-size: 23px;
    line-height: 30px; }

  .contact__us .outer__contact {
    min-height: 0px;
    padding: 25px; }

  .contact__us .outer__contact .btn > a {
    font-size: 22px;
    line-height: 30px; }

  .key__principles .outer__principles .grid .elem {
    padding-top: 20px; }

  .key__principles .outer__principles .grid {
    grid-template-columns: 1fr; }

  .partners {
    padding-top: 30px;
    padding-bottom: 40px; }

  .partners > img {
    max-width: 440px; }

  .objectives .outer__objectives .grid > .el {
    padding: 20px 0px; }

  .objectives .outer__objectives {
    padding: 25px 20px; }

  .objectives .outer__objectives > .top {
    margin-bottom: 25px; }

  .program__vision .outer__vision > h2 {
    white-space: initial; }

  .hero__section {
    padding: 10px; }

  .hero__section .float__btn {
    top: 20px;
    right: 20px; }

  .hero__section > .inner__hero .top__head {
    top: 20px; }

  .hero__section .float__btn {
    top: 45px; }

  .hero__section > .inner__hero .launch__date {
    right: 14px;
    bottom: 14px; }

  .hero__section > .inner__hero .launch__date > p {
    font-size: 14px;
    line-height: 18px;
    min-height: 36px; }

  .hero__section .float__btn > a {
    min-height: 34px;
    padding-left: 15px;
    padding-right: 15px; }

  .hero__section > .inner__hero {
    padding: 200px 0px; }

  .key__principles .outer__principles .grid {
    grid-row-gap: 25px; }

  .container {
    padding-left: 15px;
    padding-right: 15px; } }
@media (max-width: 640px) {
  .outer__hero > h6 {
    margin-bottom: 0px;
    font-size: 21px;
    line-height: 30px; }

  .learn__program .outer__learn .media img {
    height: 190px; }

  .process__wrapper .outer__process .step.last .inn .left > h6 {
    font-size: 30px;
    line-height: 38px; }

  .hero__section > .inner__hero .outer__hero > h1 {
    font-size: 38px;
    line-height: 44px; }

  .hero__section > .inner__hero .bottom {
    flex-direction: column; }

  .hero__section > .inner__hero .bottom .right {
    width: 100%;
    justify-content: space-between; }
    .hero__section > .inner__hero .bottom .right img {
      width: 50%;
      max-width: 100%; }

  .hero__section > .inner__hero .bottom > a {
    width: 100%;
    margin-bottom: 15px; } }
@media (max-width: 480px) {
  .ready__ .outer__ready > h6 {
    font-size: 42px;
    line-height: 48px; }

  .implement__wrapper .outer__implement > h6 {
    font-size: 34px;
    line-height: 34px; }

  .implement__wrapper {
    padding: 30px 0px; }

  .implement__wrapper .outer__implement .boxes .box .text > span {
    font-size: 18px; }

  .implement__wrapper .outer__implement .boxes .box .text > p {
    font-size: 24px;
    line-height: 32px;
    margin-bottom: 10px; }

  .engage__wrapper .outer__engage > h6 {
    font-size: 34px;
    line-height: 34px; }

  .engage__wrapper .outer__engage .grid > .el {
    padding: 20px; }

  .learn__program .outer__learn > h6 {
    font-size: 34px;
    line-height: 34px; }

  .overview__program .outer__overview > h2 {
    font-size: 27px;
    line-height: 34px; }

  .hero__section > .inner__hero {
    padding: 120px 0px; }

  .program__benefits .outer__program > h6 {
    font-size: 34px;
    line-height: 34px; }

  .who__can--apply .outer__apply > h6 {
    font-size: 34px;
    line-height: 34px;
    margin-bottom: 15px; }

  .learn__info .outer__learn .box > .el {
    padding: 16px 20px; }

  .learn__info .outer__learn .desc .top__part > h6 {
    font-size: 34px;
    line-height: 34px; }

  .load__animation .inn .bottom > p {
    margin-bottom: 10px;
    font-size: 16px; }

  .load__animation .inn .bottom {
    bottom: -340px; }

  .load__animation .inn .bottom video {
    width: 270px; }

  .load__animation .inn .txtone > h2 {
    font-size: 24px; }

  .load__animation .inn .txttwo > h2 {
    font-size: 24px; }

  .load__animation.loaded .inn .txtone {
    height: 32px; }

  .load__animation .inn .t__box {
    min-height: 32px; }

  .candidate__info .outer__candidate .list > .el > p {
    font-size: 17px; }

  .candidate__info .outer__candidate .list > .el {
    padding: 15px 0px; }

  .candidate__info .outer__candidate > h6 {
    font-size: 34px;
    line-height: 34px; }

  .contact__us {
    padding-bottom: 15px; }

  .contact__us .outer__contact .top > p {
    font-size: 19px;
    line-height: 26px; }

  .contact__us .outer__contact {
    padding: 15px; }

  .contact__us .outer__contact .btn > a {
    font-size: 18px;
    line-height: 24px; }

  .partners .outer__partners {
    padding-top: 40px;
    padding-bottom: 55px; }

  .process__wrapper .outer__process > .top > h6 {
    font-size: 34px;
    line-height: 34px; }

  .process__wrapper .outer__process > .top > p {
    font-size: 17px;
    line-height: 25px; }

  .key__principles .outer__principles > h6 {
    font-size: 34px;
    line-height: 34px;
    margin-bottom: 10px; }

  .partners .outer__partners .inn__box > h6 {
    font-size: 34px;
    line-height: 34px;
    margin-bottom: 10px; }

  .partners .outer__partners .inn__box > p {
    font-size: 18px;
    line-height: 26px; }

  .program__vision .outer__vision > p {
    font-size: 19px;
    line-height: 25px; }

  .objectives .outer__objectives > .top > h3 {
    font-size: 34px;
    line-height: 34px; }

  .program__vision .outer__vision > h2 {
    font-size: 34px;
    line-height: 34px; }

  .hero__section > .inner__hero .outer__hero > h1 {
    font-size: 28px;
    line-height: 36px; }

  .hero__section > .inner__hero .outer__hero > h1 .first__row {
    padding-left: 20px; }

  .hero__section > .inner__hero .outer__hero > h1 .second__row {
    padding-left: 10px;
    top: -4px; }

  .hero__section > .inner__hero .top__head .logo {
    margin: 0px 10px; }
    .hero__section > .inner__hero .top__head .logo > a img {
      max-width: 70px; }

  .hero__section > .inner__hero .top__head .icon > a {
    font-size: 14px;
    line-height: 20px; }

  .hero__section > .inner__hero .top__head .icon .spacer:nth-child(1), .hero__section > .inner__hero .top__head .icon .spacer:nth-child(2) {
    display: none; }

  .hero__section > .inner__hero .top__head .icon {
    min-width: 75px;
    justify-content: center;
    max-width: 100%; }

  .faq__wrapper .title > h6 {
    font-size: 38px;
    line-height: 38px; }

  .faq__wrapper {
    padding-top: 45px;
    padding-bottom: 25px; } }

/*# sourceMappingURL=style.css.map */
