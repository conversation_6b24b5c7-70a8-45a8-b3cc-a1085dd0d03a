import { FC, useCallback } from 'react';
import styles from './style.module.scss';
import { useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';

interface IAssistant {
  onActivateChat?: (isActive: boolean) => void;
}

const Trigger: FC<IAssistant> = ({ onActivateChat }) => {
  const t = useTranslations();

  const onClickAction = useCallback(() => {
    if (onActivateChat) {
      onActivateChat(true);
    }
  }, [onActivateChat]);

  return (
    <button
      className={styles.assistantTrigger}
      onClick={() => onClickAction()}
      title={t(dictionary.aiAssistant)}
    ></button>
  );
};

export default Trigger;
