@use 'mixins' as *;

.myStreaksBox {
  // position: relative;
  width: 100%;
  min-height: size(312);
  overflow: hidden;
  @include borderRadius(var(--radius));
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));
  background: var(--white);
}

.body {
  @include forMarginPadding(padding, size(20), size(20), size(20), size(20));

  h4 {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }
}

.streakProgress {
  width: size(160);
  height: size(160);
  @include forMarginPadding(margin, size(20), auto, size(0), auto);

  svg {
    width: size(160);
    height: size(160);
  }
}

.footer {
  @include forMarginPadding(padding, size(15), size(15), size(15), size(15));
  border-top: solid 1px var(--grey-400);

  nav {
    ul {
      @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
      @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      align-items: center;

      li {
        list-style: none;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        &:first-child {
          @include forMarginPadding(margin, size(0), size(1), size(0), size(0));
        }

        &:last-child {
          @include forMarginPadding(margin, size(0), size(0), size(0), size(1));
        }

        [data-percent] {
          width: size(35);
          height: size(35);

          svg {
            width: size(35);
            height: size(35);
          }
        }

        &.active {
          .dot {
            display: block;
          }
        }
      }
    }
  }
}

.dot {
  position: absolute;
  @include leftToRight(0);
  @include rightToLeft(0);
  @include forMarginPadding(margin, auto, auto, auto, auto);
  bottom: size(-6);
  width: size(4);
  height: size(4);
  background: var(--grey-900);
  @include borderRadius(50%);
  display: none;
}

.weekDay {
  padding: 0 !important;
}

@include for-all-phone() {
  .myStreaksBox {
    top: size(0);
    position: initial;
    min-height: size(106);
    @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
  }

  .body {
    @include forMarginPadding(padding, size(16), size(20), size(8), size(20));
  }

  .footer {
    border: none;
    @include forMarginPadding(padding, size(8), size(20), size(16), size(20));
  }
}
