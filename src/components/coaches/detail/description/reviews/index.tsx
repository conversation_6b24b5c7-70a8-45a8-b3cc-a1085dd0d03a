import { FC, useMemo } from 'react';
import styles from './style.module.scss';
import Typography from '@/ui/typography';
import Image from 'next/image';
import star from '@public/images/yellow-star.svg';
import { User } from '@/ui/icons';

interface ICoachReview {
  userPhoto?: string;
  firstName: string;
  rating: number;
  date: string;
  comment: string;
}

const CoachReview: FC<ICoachReview> = ({
  firstName,
  rating,
  date,
  comment,
}) => {
  const starRating = useMemo(() => {
    const stars: JSX.Element[] = [];

    for (let i = 0; i < rating; i++) {
      stars.push(
        <i>
          <Image
            src={star}
            className="img-fluid"
            alt=""
            width={16}
            height={16}
          />
        </i>,
      );
    }
    return stars;
  }, [rating]);

  return (
    <div className={styles.coachReview}>
      <div className={styles.userImage}>
        <i>
          <User />
        </i>
      </div>
      <div>
        <div className={styles.header}>
          <div className={styles.userName}>{firstName}</div>
          <div className={styles.ratingAndDate}>
            <div className={styles.rating}>{starRating}</div>
            <Typography as="span" className={styles.date}>
              {date}
            </Typography>
          </div>
        </div>
        <div className={styles.comment}>
          <Typography>{comment}</Typography>
        </div>
      </div>
    </div>
  );
};

export default CoachReview;
