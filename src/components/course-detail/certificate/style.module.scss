@use 'mixins' as *;

.certificates {
  @include forMarginPadding(padding, size(30), size(0), size(30), size(0));
}

.list {
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: size(16);

  li {
    list-style: none;
    display: flex;
    align-items: center;
  }
}

.card {
  @include borderRadius(var(--radius));
  overflow: hidden;
  background: var(--bg-shade-12);
  width: size(382);
}

.certLink {
  display: flex;
  @include forMarginPadding(padding, size(24), size(24), size(24), size(24));

  &:hover {
    text-decoration: underline;
  }
}

.photo {
  display: block;
  @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
  width: size(156);
  height: size(120);
  overflow: hidden;
  border: solid 1px var(--grey-700);
}

.text {
  font-size: size(14);
}

@include for-all-phone() {
  .list {
    display: block;

    li {
      + li {
        @include forMarginPadding(margin, size(16), size(0), size(0), size(0));
      }
    }
  }
}
