import { FC } from 'react';
import Image, { StaticImageData } from 'next/image';
import Typography from '@/ui/typography';
import profilePic from '@public/images/mahsa.png';
import styles from '../style.module.scss';

interface IItem {
  image?: string | StaticImageData;
  title?: string;
  designation?: string;
}

const Item: FC<IItem> = (props) => {
  const { image = profilePic, title, designation } = props;
  return (
    <div className={styles.card}>
      {image && (
        <div className={styles.photo}>
          <Image
            width={56}
            height={56}
            src={image}
            alt=""
            className="img-fluid"
          />
        </div>
      )}
      {(title || designation) && (
        <div className={styles.data}>
          {title && (
            <Typography as="span" className={styles.title}>
              {title}
            </Typography>
          )}
          {designation && (
            <Typography as="span" className={styles.designation}>
              {designation}
            </Typography>
          )}
        </div>
      )}
    </div>
  );
};

export default Item;
