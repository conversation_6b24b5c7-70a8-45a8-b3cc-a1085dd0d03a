import { NextRequest, NextResponse } from 'next/server';
import { TotaraFetchInterestsService } from '@/services/totara/interests';
import { AxiosError } from 'axios';
import { ErrorResponse } from '@/utils/fetcher';

/**
 * Route to get the interests
 */
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const id = request.nextUrl.searchParams.get('id') || '';
    // Updating user profile
    const response = await TotaraFetchInterestsService(id);
    // Return the response
    return NextResponse.json({ list: response?.data });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error as AxiosError);
  }
}
