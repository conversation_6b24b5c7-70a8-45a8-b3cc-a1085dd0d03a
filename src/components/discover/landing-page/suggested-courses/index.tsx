import React, { useEffect, useMemo } from 'react';
import CONFIG from '@/config';
import CardsSection from '@/components/common/cards-section';

import Course from '@/components/course';
import EmptyMessage from '@/ui/empty-message';
import useSuggestedCoursesStore from '@/stores/learning/courses/suggested';
import { useTranslations } from 'next-intl';
import dictionaries from '@/dictionaries';
import dictionary from '@/dictionaries';

const SuggestedCourses = () => {
  // Getting loading state
  const isFetching = useSuggestedCoursesStore((x) => x.isFetching);

  // Getting fetch action
  const onFetch = useSuggestedCoursesStore((x) => x.fetch);

  // Getting short courses
  const list = useSuggestedCoursesStore((x) => x.list);
  const t = useTranslations();
  // Creating dyanmic array of cards
  const items = useMemo(() => {
    if (list && list.length > 0) {
      return list.map((item) => <Course key={item.id} course={item} />);
    }
    return [];
  }, [list]);

  // React hook to fetch courses
  useEffect(() => {
    // If not fetched already
    if (isFetching === null) {
      // fetching courses
      onFetch();
    }
  }, [onFetch, isFetching]);

  // Return JSX
  return (
    <CardsSection
      title={t(dictionaries.CoursesYouMightBeInterestedIn)}
      listingPageUrl={CONFIG.routes.discover.suggested}
      items={items}
      limit={4}
      loading={isFetching}
      className="learningSlider2"
      slidesConfig={{
        desktop: {
          view: 4,
          gap: 16,
        },
        mobile: {
          view: 1.3,
          gap: 10,
        },
      }}
      noDataMessage={
        <EmptyMessage
          icon="search"
          title={t(dictionary.noSearchResultsFound)}
          description={t(dictionary.pleaseCheckThatAllWordsAreSpelledCorrectly)}
        />
      }
    />
  );
};

export default SuggestedCourses;
