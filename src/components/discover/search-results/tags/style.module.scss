@use 'mixins' as *;

.filterTags {
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));

  nav {
    position: relative;
    .scrollArrow {
      position: absolute;

      top: size(5);
      height: size(34);
      width: size(34);

      border-radius: 50%;
      background: var(--white);
      border: none;
      outline: none;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      background: var(--white);
      border: 1px solid var(--grey-500);
      z-index: 1;
      cursor: pointer;

      svg {
        fill: var(--grey-900);
        height: size(14);
        width: size(14);
      }
    }

    .scrollArrowRight {
      right: size(0);
    }

    .scrollArrowLeft {
      left: size(0);
    }

    ul {
      @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
      @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
      display: flex;
      overflow-x: auto;
      -ms-overflow-style: none;
      scrollbar-width: none;
      &::-webkit-scrollbar {
        display: none;
      }
      li {
        list-style: none;
        @include forMarginPadding(margin, size(0), size(10), size(0), size(0));

        &:last-child {
          @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
        }

        button {
          white-space: nowrap;
          background: var(--grey-900);
          color: var(--white);
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          @include borderRadius(40px);
          font-size: size(16);
          position: relative;
          @include forMarginPadding(
            padding,
            size(12),
            size(16),
            size(12),
            size(16)
          );
          line-height: 1;
          font-weight: 400;
          border: none;
          cursor: pointer;

          i {
            @include borderRadius(50%);
            width: size(20);
            height: size(20);
            background: var(--white);
            @include forMarginPadding(
              padding,
              size(2),
              size(2),
              size(2),
              size(2)
            );
            @include forMarginPadding(
              margin,
              size(0),
              size(0),
              size(0),
              size(8)
            );
            display: flex;
            justify-content: center;
            align-items: center;

            svg {
              fill: var(--grey-900);
              fill-rule: evenodd;
              width: 60%;
              vertical-align: top;
            }
          }
        }
      }
    }
  }
}

@include for-all-phone() {
  .filterTags {
    nav {
      ul {
        overflow-x: scroll;
        display: flex;
        white-space: nowrap;
        flex-wrap: nowrap;
      }
    }
  }
}
