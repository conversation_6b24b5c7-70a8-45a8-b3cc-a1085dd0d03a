import { ICommunityLibrary } from '@/types/domain/my-network/communityDetails';
import Paper from '@/ui/paper';
import LibraryItem from './library-item';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';
import { useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';
interface ILibraryProps {
  records: ICommunityLibrary[] | null;
  isLoading: boolean;
}

const Library: React.FC<ILibraryProps> = ({ records, isLoading }) => {
  const t = useTranslations();
  return (
    <WithLoader loader={<Loading />} loading={isLoading}>
      <Paper title={t(dictionary.Library)}>
        {records &&
          records?.map((record) => {
            return <LibraryItem record={record} key={record.url} />;
          })}
      </Paper>
    </WithLoader>
  );
};

export default Library;
