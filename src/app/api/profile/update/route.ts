import { NextRequest, NextResponse } from 'next/server';
import { ExternalUpdateProfileService } from '@/services/external/profile';
import IUser from '@/types/domain/external/user';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';

// To handle a POST request to /api
export async function POST(request: NextRequest) {
  try {
    // Getting request body
    const data: IUser = await request.json();
    // Updating user profile
    const response = await ExternalUpdateProfileService(data);
    // Return the response
    return NextResponse.json({ user: response?.data });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error as AxiosError);
  }
}
