@use 'mixins' as *;

.banner {
  position: relative;
  height: size(434);
  @include borderRadius(var(--radius));
  overflow: hidden;
  @include forMarginPadding(margin, size(0), size(0), size(24), size(0));
}

.btnWrap {
  position: absolute;
  @include rightToLeft(size(24));
  bottom: size(24);

  button {
    border: solid 1px var(--white);
    font-size: size(21);
    min-width: size(120);
    min-height: size(49);
    @include forMarginPadding(padding, size(12), size(30), size(12), size(30));
  }
}

@include hover() {
  .btnWrap {
    button {
      &:hover {
        background-color: var(--white);
        color: var(--black);
      }
    }
  }
}

@include for-all-phone() {
  .banner {
    height: initial;
  }

  .btnWrap {
    position: absolute;
    @include rightToLeft(size(16));
    bottom: size(16);

    button {
      font-size: size(18);
    }
  }
}
