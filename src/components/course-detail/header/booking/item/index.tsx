'use client';

import { FC, useCallback, useMemo } from 'react';
import Typography from '@/ui/typography';
import Pin from '@/ui/icons/pin';
import cn from 'classnames';
import styles from './style.module.scss';
import { formatLocalTime } from '@/utils/time';
import { useLocale } from 'next-intl';
import { AvailableSlots } from '@/types/domain/learning/course';
import usePhysicalCourseStore from '@/stores/learning/courses/physical';

interface IItem {
  slot: AvailableSlots;
  title?: string;
}

const Item: FC<IItem> = (props) => {
  // Deconstructing props
  const { slot, title } = props;

  const selected = usePhysicalCourseStore((x) => x.selected);
  const setSelected = usePhysicalCourseStore((x) => x.setSelected);

  const locale = useLocale();

  // Getting active state
  const active = useMemo(() => {
    return selected?.id === slot?.id;
  }, [selected?.id, slot?.id]);

  // Creating classnames
  const classNames = cn({
    [styles.item]: true,
    [styles.active]: active,
    [styles.rejected]: slot.is_booked === 'REJECTED',
  });

  // Memoizing the start
  const start = useMemo(() => {
    // If start is available
    if (slot?.startdatetime) {
      return formatLocalTime(slot?.startdatetime);
    }
    // Returning null
    return null;
  }, [slot?.startdatetime]);

  // Memoizing the end
  const end = useMemo(() => {
    // If start is available
    if (slot?.enddatetime) {
      return formatLocalTime(slot?.enddatetime);
    }
    // Returning null
    return null;
  }, [slot?.enddatetime]);

  /**
   * Method to handle click
   */
  const handleCLick = useCallback(() => {
    if (slot) {
      if (slot.is_booked === 'REJECTED') return;
      setSelected(slot);
    }
  }, [slot, setSelected]);

  // Return JSX
  return (
    <div className={classNames} onClick={handleCLick}>
      {(title || start || end) && (
        <div className={styles.head}>
          {title && <Typography as="span">{title}</Typography>}
          {(start || end) && (
            <Typography as="span" className={styles.time}>
              {start?.locale(locale === 'ar' ? 'ar' : 'en').format('h:mma')} -{' '}
              {end?.locale(locale === 'ar' ? 'ar' : 'en').format('h:mma')}
            </Typography>
          )}
        </div>
      )}
      {!Array.isArray(slot?.location) && slot?.location?.room && (
        <div className={styles.location}>
          <i>
            <Pin />
          </i>
          <Typography as="span">{slot?.location.room}</Typography>
        </div>
      )}
    </div>
  );
};

export default Item;
