import { FC, useMemo } from 'react';
import ICourse from '@/types/domain/learning/course';
import Typography from '@/ui/typography';
import Image from '@/ui/image';
import star from '@public/images/star.svg';
import styles from './style.module.scss';

interface IRating {
  course: ICourse;
}

/**
 * Normalizes the rating value from a course object, handling inconsistencies in API responses.
 * The API may return the rating under either 'raiting' or 'ratings' keys,
 * and the value can be either a number or a string.
 *
 * @param {ICourse | null | undefined} course - The course object containing the rating.
 * @returns {number | null} - The normalized numeric rating, or null if no valid rating is available.
 *
 */

function normalizeRating(course: ICourse | null | undefined): number | null {
  if (!course) {
    return null;
  }

  const rawRating =
    course.rating !== undefined ? course.rating : course.ratings;

  if (rawRating === undefined || rawRating === null) {
    return null;
  }

  const parsedRating = parseFloat(rawRating.toString());

  return isNaN(parsedRating) ? null : parsedRating;
}

const Rating: FC<IRating> = (props) => {
  // Deconstructing props
  const { course } = props;

  // Getting course rating
  const rating = useMemo(() => {
    return normalizeRating(course);
  }, [course]);

  // Return JSX
  return (
    !!rating && (
      <div className={styles.rating}>
        <span className={styles.starRating}>
          <Image src={star} alt="" className="img-fluid" />
        </span>
        <Typography as="span" className={styles.ratingText}>
          {rating}
        </Typography>
      </div>
    )
  );
};

export default Rating;
