import { NextResponse } from 'next/server';
import { AxiosError } from 'axios';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchOnboardingInfoService } from '@/services/totara/onboarding';

/**
 * Route to get the interests
 */
export async function GET() {
  try {
    // Updating user profile
    const response = await TotaraFetchOnboardingInfoService();
    // Return the response
    return NextResponse.json({
      levels: response?.data?.grade || [],
      departments: response?.data?.discipline || [],
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error as AxiosError);
  }
}
