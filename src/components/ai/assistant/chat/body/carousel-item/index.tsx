import { FC } from 'react';
import styles from './style.module.scss';
import Image from 'next/image';
import ISuggestionCourse from '@/types/domain/ai/suggestionCourse';
import { useLocale } from 'next-intl';
import { Clock, Pillars } from '@/ui/icons';

interface ICarouselItem {
  item?: ISuggestionCourse;
}

const CarouselItem: FC<ICarouselItem> = ({ item }) => {
  const skills = item?.Skills?.split(',');
  const locale = useLocale();
  const courseId = item?.CourseURL.split('id=')[1];
  const courseUrl = `/${locale}/course/${courseId}`;

  return (
    <>
      <div className={styles.carouselItemImage}>
        <a href={courseUrl}>
          <Image
            alt={item?.Name || 'image'}
            className={styles.courseImage}
            src={item?.ImageURL as string}
            height={150}
            width={270}
          />
        </a>
      </div>
      <div className={styles.carouselItemInfo}>
        <div className={styles.infoType}>{item?.Type}</div>
        <a href={courseUrl} className={styles.nameLink}>
          <div className={styles.infoName}>{item?.Name}</div>
        </a>
        <div className={styles.infoMeta}>
          <div className={styles.infoDuration}>
            <Clock />
            {item?.Duration}
          </div>
          <div className={styles.infoProficiency}>
            <Pillars />
            {item?.Proficiency}
          </div>
        </div>
        <div className={styles.infoSummary}>{item?.Summary}</div>
        <div className={styles.infoSkills}>
          {skills?.map((skill: string, index) => (
            <div key={skill + index} className={styles.infoSkill}>
              {skill}
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default CarouselItem;
