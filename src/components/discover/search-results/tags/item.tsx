import { FC } from 'react';
import { Close } from '@/ui/icons';
import Typography from '@/ui/typography';

interface IItem {
  title: string;
  onClick: () => void;
}

/**
 * React component to handle
 * each tag
 */
const Item: FC<IItem> = ({ title, onClick }) => {
  // Return JSX
  return (
    <li>
      <button type="button">
        <Typography as="span">{title}</Typography>
        <i onClick={onClick}>
          <Close />
        </i>
      </button>
    </li>
  );
};

// Export the component
export default Item;
