import Paper from '@/ui/paper';
import Typography from '@/ui/typography';
import { EditIcon, ImageIcon, PopperIcon, QuestionIcon } from '@/ui/icons';

// Styles
import styles from './style.module.scss';
import { getFullNameInitials } from '@/utils/helpers';
import useAuthStore from '@/stores/auth';
import { FC, useMemo } from 'react';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';
import { PostTypeEnum } from '../discussion';
import dictionary from '@/dictionaries';

interface IShareYourThoughtsProps {
  postType?: PostTypeEnum;
  handleOpenDialog: () => void;
  handleSetPostType: React.Dispatch<React.SetStateAction<PostTypeEnum>>;
}

const ShareYourThoughts: FC<IShareYourThoughtsProps> = ({
  handleOpenDialog,
  handleSetPostType,
}) => {
  const { user, isFetching } = useAuthStore((store) => store);

  const fullNameInitials = useMemo(() => {
    return user?.fullname && getFullNameInitials(user?.fullname as string);
  }, [user]);

  const handleChangePostTypeAndOpenPopup = (
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    handleSetPostType(event.currentTarget.name as PostTypeEnum);
    handleOpenDialog();
  };

  return (
    <WithLoader loader={<Loading />} loading={isFetching}>
      <Paper>
        <div className={styles.head} onClick={handleOpenDialog}>
          <div className={styles.avatar}>
            <Typography>{fullNameInitials}</Typography>
          </div>
          <Typography
            as="p"
            className={styles.postInput}
            dictionary={dictionary.startAdiscussion}
          />
          <div className={styles.icon}>
            <i>
              <ImageIcon />
            </i>
          </div>
        </div>
        <div className={styles.footer}>
          <button
            type="button"
            className={styles.btn}
            name={PostTypeEnum.discussion}
            onClick={handleChangePostTypeAndOpenPopup}
          >
            <i>
              <EditIcon />
            </i>
            <Typography as="span" dictionary={dictionary.discussion} />
          </button>
          <button
            type="button"
            className={styles.btn + ' ' + styles.question}
            name={PostTypeEnum.question}
            onClick={handleChangePostTypeAndOpenPopup}
          >
            <i>
              <QuestionIcon />
            </i>
            <Typography as="span" dictionary={dictionary.question} />
          </button>
          <button
            type="button"
            className={styles.btn + ' ' + styles.popper}
            name={PostTypeEnum.celebration}
            onClick={handleChangePostTypeAndOpenPopup}
          >
            <i>
              <PopperIcon />
            </i>
            <Typography as="span" dictionary={dictionary.celebrate} />
          </button>
        </div>
      </Paper>
    </WithLoader>
  );
};

export default ShareYourThoughts;
