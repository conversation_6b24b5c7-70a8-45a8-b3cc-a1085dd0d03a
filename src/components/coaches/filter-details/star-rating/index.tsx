import ChipButton from '@/ui/form/chip-button';
import Typography from '@/ui/typography';
import { FC, useCallback } from 'react';

// Styles
import styles from './style.module.scss';
import star from '@public/images/yellow-star.svg';
import Image from 'next/image';

interface IStarRatingFilter {
  onSelectChange: (
    id: string,
    name: string | undefined,
    fieldName?: string,
  ) => void;
  fieldName: string;
  selected: string;
}

const StarRatingFilter: FC<IStarRatingFilter> = ({
  onSelectChange,
  fieldName,
  selected,
}) => {
  // Method to handle click
  const onClick = useCallback(
    (x?: string) => {
      if (x) {
        onSelectChange(x, x, fieldName);
      }
    },
    [onSelectChange, fieldName],
  );

  const list = ['1-2', '2-3', '3-4', '4-5'];

  // Return JSX
  return (
    <nav className={styles.filter}>
      <Typography as="h4">Rating</Typography>
      <ul>
        {list?.map((x, i) => (
          <li key={'type-' + i}>
            <ChipButton
              variant="rounded"
              active={selected === x}
              onClick={() => onClick(x)}
            >
              <span className={styles.ratingFilter}>
                <i>
                  <Image
                    src={star}
                    className="img-fluid"
                    alt=""
                    width={16}
                    height={16}
                  />
                </i>
                {x}
                {' star'}
              </span>
            </ChipButton>
          </li>
        ))}
      </ul>
    </nav>
  );
};

export default StarRatingFilter;
