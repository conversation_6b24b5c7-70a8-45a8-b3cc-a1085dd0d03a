import { NextRequest, NextResponse } from 'next/server';
import { ExternalGetProfileService } from '@/services/external/profile/get-profile';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';

// To handle a GET request to /api
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const id = request.nextUrl.searchParams.get('talentId') || '';
    // Fetching user profile
    const response = await ExternalGetProfileService(id);
    // Return the response
    return NextResponse.json({
      user: response?.data,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error as AxiosError);
  }
}
