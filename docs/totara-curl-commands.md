# Totara LMS Authentication & Course Retrieval - Curl Commands

## Complete curl command sequence for ILearn platform integration

**Base URL:** `https://adsgstaging.elearning.ae`
**API Endpoint:** `https://adsgstaging.elearning.ae/webservice/rest/server.php`
**Last Updated:** January 30, 2025

---

## 🔐 **Step 1: Login Setup**

### **Initialize Login Session**

```bash
curl -X POST "https://adsgstaging.elearning.ae/totara/mobile/login_setup.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "User-Agent: TotaraClient/1.0" \
  -d "service=totara_mobile_app"
```

**Expected Response:**

```json
{
  "token": "setup_token_here",
  "privatetoken": "private_token_here"
}
```

---

## 🔑 **Step 2: Client Login**

### **Authenticate with Credentials**

```bash
curl -X POST "https://adsgstaging.elearning.ae/local/wstotara/client/login.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "User-Agent: TotaraClient/1.0" \
  -d "username=YOUR_USERNAME" \
  -d "password=YOUR_PASSWORD" \
  -d "service=totara_mobile_app" \
  -d "token=SETUP_TOKEN_FROM_STEP1"
```

**Expected Response:**

```json
{
  "token": "client_login_token",
  "privatetoken": "private_token_updated"
}
```

---

## 🔒 **Step 3: Get Login Secrets**

### **Fetch Authentication Secrets**

```bash
curl -X POST "https://adsgstaging.elearning.ae/totara/mobile/login.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "User-Agent: TotaraClient/1.0" \
  -d "username=YOUR_USERNAME" \
  -d "password=YOUR_PASSWORD" \
  -d "service=totara_mobile_app" \
  -d "token=CLIENT_LOGIN_TOKEN_FROM_STEP2"
```

**Expected Response:**

```json
{
  "token": "final_ws_token",
  "privatetoken": "final_private_token",
  "siteurl": "https://adsgstaging.elearning.ae"
}
```

---

## 📱 **Step 4: Register Device (Optional)**

### **Register Device for Mobile Access**

```bash
curl -X POST "https://adsgstaging.elearning.ae/totara/mobile/device_register.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "User-Agent: TotaraClient/1.0" \
  -d "token=FINAL_WS_TOKEN_FROM_STEP3" \
  -d "appid=com.dge.academy" \
  -d "name=Mobile Device" \
  -d "model=iPhone" \
  -d "platform=iOS" \
  -d "version=1.0" \
  -d "pushid=device_push_token"
```

---

## 📚 **Step 5: Get All Available Courses**

### **5.1 Get Recommended Courses**

```bash
curl -X POST "https://adsgstaging.elearning.ae/webservice/rest/server.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wstoken=FINAL_WS_TOKEN" \
  -d "moodlewsrestformat=json" \
  -d "wsfunction=wstotara_get_onboardingcourses" \
  -d "language=en"
```

### **5.2 Search All Courses (No Filters)**

```bash
curl -X POST "https://adsgstaging.elearning.ae/webservice/rest/server.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wstoken=FINAL_WS_TOKEN" \
  -d "moodlewsrestformat=json" \
  -d "wsfunction=wstotara_search_learningdata" \
  -d "language=en" \
  -d "page=1" \
  -d "perpage=100" \
  -d "keyword="
```

### **5.3 Get Course Categories**

```bash
curl -X POST "https://adsgstaging.elearning.ae/webservice/rest/server.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wstoken=FINAL_WS_TOKEN" \
  -d "moodlewsrestformat=json" \
  -d "wsfunction=wstotara_get_coursecategories" \
  -d "language=en"
```

### **5.4 Get Top Picks**

```bash
curl -X POST "https://adsgstaging.elearning.ae/webservice/rest/server.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wstoken=FINAL_WS_TOKEN" \
  -d "moodlewsrestformat=json" \
  -d "wsfunction=wstotara_get_top_picks" \
  -d "language=en"
```

### **5.5 Get Recent/New Courses**

```bash
curl -X POST "https://adsgstaging.elearning.ae/webservice/rest/server.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wstoken=FINAL_WS_TOKEN" \
  -d "moodlewsrestformat=json" \
  -d "wsfunction=wstotara_get_recent_courses" \
  -d "language=en"
```

### **5.6 Get Trending Courses**

```bash
curl -X POST "https://adsgstaging.elearning.ae/webservice/rest/server.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wstoken=FINAL_WS_TOKEN" \
  -d "moodlewsrestformat=json" \
  -d "wsfunction=wstotara_get_trending_courses" \
  -d "language=en"
```

### **5.7 Get Short Duration Courses**

```bash
curl -X POST "https://adsgstaging.elearning.ae/webservice/rest/server.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wstoken=FINAL_WS_TOKEN" \
  -d "moodlewsrestformat=json" \
  -d "wsfunction=wstotara_get_duration_based_courses" \
  -d "language=en" \
  -d "courseduration=short"
```

---

## 🔍 **Advanced Course Search**

### **Search with Filters**

```bash
curl -X POST "https://adsgstaging.elearning.ae/webservice/rest/server.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wstoken=FINAL_WS_TOKEN" \
  -d "moodlewsrestformat=json" \
  -d "wsfunction=wstotara_search_learningdata" \
  -d "language=en" \
  -d "page=1" \
  -d "perpage=50" \
  -d "keyword=javascript" \
  -d "category=programming" \
  -d "learningtype=online" \
  -d "durationto=10"
```

### **Get Course Filters (Available Options)**

```bash
curl -X POST "https://adsgstaging.elearning.ae/webservice/rest/server.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wstoken=FINAL_WS_TOKEN" \
  -d "moodlewsrestformat=json" \
  -d "wsfunction=wstotara_get_course_filters" \
  -d "language=en"
```

---

## 📖 **Get Course Details**

### **Get Specific Course Details**

```bash
curl -X POST "https://adsgstaging.elearning.ae/webservice/rest/server.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wstoken=FINAL_WS_TOKEN" \
  -d "moodlewsrestformat=json" \
  -d "wsfunction=wstotara_course_details" \
  -d "language=en" \
  -d "courseid=COURSE_ID_HERE"
```

---

## 🎯 **Complete Authentication Script**

### **Bash Script for Full Authentication Flow**

```bash
#!/bin/bash

# Configuration
BASE_URL="https://adsgstaging.elearning.ae"
USERNAME="your_username"
PASSWORD="your_password"

echo "🔐 Step 1: Login Setup..."
SETUP_RESPONSE=$(curl -s -X POST "$BASE_URL/totara/mobile/login_setup.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "service=totara_mobile_app")

SETUP_TOKEN=$(echo $SETUP_RESPONSE | jq -r '.token')
echo "Setup Token: $SETUP_TOKEN"

echo "🔑 Step 2: Client Login..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/local/wstotara/client/login.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=$USERNAME" \
  -d "password=$PASSWORD" \
  -d "service=totara_mobile_app" \
  -d "token=$SETUP_TOKEN")

CLIENT_TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.token')
echo "Client Token: $CLIENT_TOKEN"

echo "🔒 Step 3: Get Secrets..."
SECRETS_RESPONSE=$(curl -s -X POST "$BASE_URL/totara/mobile/login.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=$USERNAME" \
  -d "password=$PASSWORD" \
  -d "service=totara_mobile_app" \
  -d "token=$CLIENT_TOKEN")

WS_TOKEN=$(echo $SECRETS_RESPONSE | jq -r '.token')
echo "Final WS Token: $WS_TOKEN"

echo "📚 Step 4: Get All Courses..."
curl -X POST "$BASE_URL/webservice/rest/server.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wstoken=$WS_TOKEN" \
  -d "moodlewsrestformat=json" \
  -d "wsfunction=wstotara_search_learningdata" \
  -d "language=en" \
  -d "page=1" \
  -d "perpage=100" \
  -d "keyword=" | jq '.'
```

---

## 🔧 **Environment Variables Setup**

### **Set Environment Variables**

```bash
export TOTARA_BASE_URL="https://adsgstaging.elearning.ae"
export TOTARA_USERNAME="your_username"
export TOTARA_PASSWORD="your_password"
export TOTARA_WS_TOKEN="your_obtained_token"
```

### **Use Environment Variables in Curl**

```bash
curl -X POST "$TOTARA_BASE_URL/webservice/rest/server.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wstoken=$TOTARA_WS_TOKEN" \
  -d "moodlewsrestformat=json" \
  -d "wsfunction=wstotara_search_learningdata" \
  -d "language=en" \
  -d "page=1" \
  -d "perpage=100"
```

---

## 📊 **Response Examples**

### **Course Search Response**

```json
{
  "data": {
    "courses": [
      {
        "id": "123",
        "fullname": "Introduction to JavaScript",
        "shortname": "JS101",
        "summary": "Learn JavaScript fundamentals",
        "categoryname": "Programming",
        "duration": "40 hours",
        "enrollmentcount": 150,
        "rating": 4.5
      }
    ],
    "totalcount": 500,
    "page": 1,
    "perpage": 100
  },
  "warnings": []
}
```

---

## ⚠️ **Important Notes**

1. **Token Expiry**: Tokens expire after inactivity - re-authenticate if needed
2. **Rate Limiting**: Don't make too many concurrent requests
3. **Error Handling**: Check response for errors before proceeding
4. **Security**: Never commit credentials to version control
5. **Pagination**: Use `page` and `perpage` parameters for large datasets
6. **Language**: Set `language=en` or `language=ar` for localized content

---

## 🚨 **Troubleshooting**

### **Common Issues**

- **Invalid Token**: Re-run authentication flow
- **403 Forbidden**: Check user permissions
- **Empty Response**: Verify function name and parameters
- **Timeout**: Increase curl timeout with `--max-time 30`

### **Debug Mode**

Add `-v` flag to curl commands for verbose output:

```bash
curl -v -X POST "https://adsgstaging.elearning.ae/webservice/rest/server.php" \
  -d "wstoken=$WS_TOKEN" \
  -d "moodlewsrestformat=json" \
  -d "wsfunction=wstotara_search_learningdata"
```

This guide provides complete curl commands for authenticating with Totara LMS and retrieving all available courses.
