@use 'mixins' as *;
.listWrapper {
  @include forMarginPadding(margin, size(24), size(0), size(12), size(0));
  @include forMarginPadding(padding, size(24), size(0), size(0), size(0));

  border-top: solid 1px var(--grey-400);
}

.containerInput {
  display: flex;
  align-items: center;
  position: relative;
  @include borderRadius(var(--radius));
  @include forMarginPadding(padding, size(15), size(26), size(15), size(26));
  overflow: hidden;
  background: var(--grey-3);
  cursor: pointer;
  border: solid 1px var(--grey-500);

  input {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }
}

.postInput {
  font-size: size(20);
  margin-bottom: size(20);
  border: none;
  background: transparent;
  outline: none;
  font-size: inherit;
  color: inherit;
  width: 100%;
}

.icon {
  @include forMarginPadding(margin, size(0), size(0), size(0), auto);
  label {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--grey-700);
    font-size: size(14);
  }
  i {
    display: flex;
  }
  svg {
    width: size(36);
    height: size(36);
    fill: var(--grey-900);
  }
}

.iconAirplane {
  @include forMarginPadding(margin, size(0), size(0), size(0), size(12));
  button {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--grey-700);
    font-size: size(14);
    border: none;
    background-color: transparent;
    &:disabled {
      cursor: auto;
      svg {
        fill: var(--grey-700);
      }
    }
    i {
      display: flex;
      @include rtl {
        transform: rotate(180deg);
      }
    }
    svg {
      fill: var(--text-link-blue);
    }
  }
}
.inputImageContainer {
  @include forMarginPadding(margin, size(16), size(8), size(8), size(8));

  display: flex;
}

.inputImage {
  @include forMarginPadding(margin, size(0), size(8), size(0), size(0));

  width: size(150);
  height: size(150);
  overflow: hidden;

  img {
    object-fit: cover;
    object-position: center;
    height: 100%;
    width: auto;
  }
}

.close {
  // position: absolute;
  // @include rightToLeft(size(24));
  // top: size(30);
  width: size(36);
  height: size(36);
  background: none;
  border: solid 1px var(--grey-700);
  display: flex;
  justify-content: center;
  align-items: center;
  @include borderRadius(50%);
  cursor: pointer;
  @include transitions(0.5s);
  fill: var(--grey-700);

  i {
    width: size(14);
    height: size(14);
    vertical-align: top;

    svg {
      vertical-align: top;
    }
  }
}

.avatar {
  width: size(40);
  height: size(40);
  @include borderRadius(50%);
  @include forMarginPadding(margin, size(0), size(12), size(0), size(0));
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  @include borderRadius(50%);
  background: rgba(160, 146, 200, 0.15);

  p {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    font-weight: 500;
    font-size: size(16);
  }
}
