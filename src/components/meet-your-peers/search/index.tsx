'use client';

import { useState } from 'react';
import InputField from '@/ui/form/input-field';
import { Close, SearchIcon } from '@/ui/icons';
import styles from './style.module.scss';
import { useTranslations } from 'next-intl';
import dictionaries from '@/dictionaries';

/**
 * Component to handle search
 */
const Search = () => {
  const t = useTranslations();

  const [search, setSearch] = useState('');
  /**
   * Method to handle clear input value
   */
  const handleClick = () => {
    setSearch('');
    console.log('Search...');
  };

  /**
   * Method to handle input change
   */
  const handleChange = () => {
    console.log('Search...');
  };

  // Return JSX
  return (
    <div className={'position-relative ' + styles.searchWrapper}>
      <InputField
        placeholder={t(dictionaries.searchDirectory)}
        ariaLabel="search"
        endIcon=""
        className={styles.input}
        value={search || ''}
        onChange={handleChange}
      />
      <button type="button" className={styles.searchBtn} onClick={handleClick}>
        <i>{search && search?.length > 0 ? <Close /> : <SearchIcon />}</i>
      </button>
    </div>
  );
};

export default Search;
