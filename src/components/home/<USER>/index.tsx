import { useEffect, useMemo } from 'react';
import CONFIG from '@/config';
import CardsSection from '@/components/common/cards-section';
import useRecommendedCoursesStore from '@/stores/learning/courses/recommended';
import Course from '@/components/course';
import EmptyMessage from '@/ui/empty-message';
import styles from './style.module.scss';
import dictionary from '@/dictionaries';

const RecommendedCourses = () => {
  // Getting loading state and data from the store
  const { isFetching, fetch: onFetch, list } = useRecommendedCoursesStore();

  // Creating listing page url
  const page = useMemo(() => {
    // If list has more than 3 items
    if (list?.length > 3) {
      return CONFIG.routes.myCourseList.replace('{id}', 'recommended');
    }
    // Return the listing page URL
    return null;
  }, [list]);

  // Creating dyanmic array of cards
  const items = useMemo(() => {
    return list?.map((item, index) => (
      <Course key={index} course={item} variant="beta" />
    ));
  }, [list]);

  // Effect to fetch the recommended courses
  useEffect(() => {
    // If not fetching and list is empty
    if (isFetching === null) {
      // Fetch the recommended courses
      onFetch();
    }
  }, [onFetch, isFetching]);

  // Return JSX
  return (
    <CardsSection
      loading={isFetching}
      title={dictionary.recommendedForYou}
      listingPageUrl={page}
      items={items}
      limit={3}
      className="learningSlider"
      wrapperClass={styles.recommendedWrapper}
      slidesConfig={{
        desktop: {
          view: 3,
          gap: 16,
        },
        tablet: {
          view: 2.2,
          gap: 16,
        },
        mobile: {
          view: 1.2,
          gap: 10,
        },
      }}
      noDataMessage={
        <EmptyMessage
          icon="notification"
          title={dictionary.noRecommendedLearningAvailable}
          description={dictionary.thereAreNoNewRecommendationsAtThisTime}
        />
      }
    />
  );
};

export default RecommendedCourses;
