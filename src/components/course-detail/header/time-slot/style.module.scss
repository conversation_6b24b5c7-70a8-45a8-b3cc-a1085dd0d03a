@use 'mixins' as *;

.timeSlotWrapper {
  background-color: var(--bg-shade-4);
  border-radius: size(12);
  padding: size(10);
  margin-bottom: size(16);
  max-width: size(336);
  width: 100%;
  position: relative;
}

.slotHeader {
  align-items: center;
  display: flex;
  gap: size(8);

  &:first-child {
    margin-bottom: size(8);
  }

  p {
    margin: 0;
  }
}

.iconWrapper {
  align-items: center;
  background-color: var(--warning-900);
  border-radius: 50%;
  display: flex;
  height: size(32);
  justify-content: center;
  padding: size(5);
  width: size(32);

  svg {
    fill: var(--white);
    height: size(17);
    width: size(17);
  }
}

.dateIconWrapper {
  align-items: center;
  display: flex;
  height: size(32);
  justify-content: center;
  padding: size(5);
  width: size(32);

  svg {
    fill: var(--grey-900);
    height: size(17);
    width: size(17);
  }
}

.dateText {
  font-size: size(14);
}

.menuWrapper {
  // @include forMarginPadding(padding, size(9), size(9), size(9), size(9));
  display: flex;
  justify-content: center;
  align-items: center;
  width: size(32);
  height: size(32);
  gap: size(16);
  position: absolute;
  top: size(10);
  right: size(10);
  border: 1px solid var(--grey-700);
  @include borderRadius(50%);

  @include rtl {
    right: auto;
    left: size(10);
  }

  div[class*='menu'] {
    // @include forMarginPadding(padding, size(4), size(16), size(4), size(16));
    background-color: var(--tag);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }

  ul {
    list-style: none;
  }

  li {
    &:not(:last-child) {
      border-bottom: 1px solid var(--grey-500);
    }
  }

  .menuItem {
    display: flex;
    align-items: center;
    border: none;
    width: 100%;
    height: 100%;
    background: none;
    font-size: size(16);
    @include forMarginPadding(padding, size(16), size(16), size(16), size(16));
    @include transitions(0.2s);
    cursor: pointer;

    & i {
      display: flex;
      justify-content: center;
      align-items: center;
      @include forMarginPadding(margin, size(0), size(16), size(0), size(0));
    }

    & svg {
      fill: var(--grey-900);
      width: size(20);
      height: size(20);
    }
  }
}

@include hover() {
  @include for-dark-theme {
    .menuWrapper {
      .menuItem:hover {
        background-color: var(--grey-500);
      }
    }
  }
  .menuWrapper {
    .menuItem:hover {
      background-color: var(--grey-300);
    }
  }
}

@include for-dark-theme() {
  .timeSlotWrapper {
    background-color: var(--bg-card-1);
  }
}
