const basePaths = {
  auth: '/auth/token/',
  crm: '/crm/',
  faq: '/adsg-faq-headless/v1.0/',
};

const supportCenter = {
  // Legacy Liferay FAQ (no longer used):
  faq: `${basePaths.faq}faq`,

  // Auth & CRM endpoints
  authenticate: basePaths.auth,
  references: `${basePaths.crm}references/`,
  createCase: `${basePaths.crm}cases/`,
  addAttachment: `${basePaths.crm}attachments/`,
  addComment: `${basePaths.crm}comments/`,

  // CRM FAQ endpoints
  faqCategories: `${basePaths.crm}faq/categories/`,
  faqItems: `${basePaths.crm}faq/items/`,
  getFaqItem: (id: number | string) => `${basePaths.crm}faq/items/${id}/`,

  getCase: (caseNumber: string | number) =>
    `${basePaths.crm}cases/${caseNumber}/`,
  getCases: (emiratesId: string) =>
    `${basePaths.crm}cases/?emirates_id=${emiratesId}`,
  getCaseAttachments: (caseNumber: string | number) =>
    `${basePaths.crm}cases/${caseNumber}/attachments/`,
};

export default supportCenter;
