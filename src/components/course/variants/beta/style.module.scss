@use 'mixins' as *;

.card {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  @include borderRadius(var(--radius));
  color: var(--white);
  fill: var(--white);

  &.medium {
    .top {
      p {
        font-size: size(14);
      }
    }

    .bottom {
      p {
        font-size: size(16);
      }

      h4 {
        font-size: size(22);
      }
    }

    .content {
      @include forMarginPadding(
        padding,
        size(16),
        size(16),
        size(16),
        size(16)
      );
    }
  }

  &:hover {
    > div[class*='bg'] {
      transform: scale(1.1, 1.1);
      transition: 0.4s transform cubic-bezier(0.77, 0.23, 0.23, 0.81);

      &:before {
        opacity: 0.7;
        transition: 0.4s all cubic-bezier(0.77, 0.23, 0.23, 0.81);
      }
    }

    h4[class*='title'] {
      text-decoration: underline;
    }
  }
}

.bg {
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  position: relative;
  transform: scale(1, 1);
  transition: 0.4s transform cubic-bezier(0.77, 0.23, 0.23, 0.81);

  &:before {
    position: absolute;
    @include leftToRight(0);
    top: 0;
    width: 100%;
    height: 100%;
    background: #000;
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0.47) 0%,
      rgba(0, 0, 0, 0.12) 13%,
      rgba(0, 0, 0, 0.13) 54.59%,
      rgba(0, 0, 0, 0.71) 83.09%
    );
    opacity: 0.9;
    content: '';
    transition: 0.4s all cubic-bezier(0.77, 0.23, 0.23, 0.81);
  }
}

.link {
  width: 100%;
  height: 100%;
  position: absolute;
  @include leftToRight(0);
  top: 0;
  z-index: 1;
}

.content {
  position: absolute;
  @include leftToRight(0);
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  @include forMarginPadding(padding, size(24), size(24), size(24), size(24));

  p {
    @include forMarginPadding(margin, size(0), size(0), size(10), size(0));
  }
}

button.favorite {
  fill: #ffffff;
  height: size(26);
  width: size(26);

  i {
    width: size(24);
  }
}

.provider {
  color: var(--grey-400);
}

nav.infoList {
  ul {
    li {
      i {
        svg {
          fill: var(--grey-400);
        }
      }

      span {
        color: var(--grey-400);
      }
    }
  }
}

.top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: size(4);

  p {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    font-weight: 400;
    flex: 1;
    color: white;
  }

  button {
    @include forMarginPadding(margin, size(0), size(0), size(0), auto);
  }

  svg[class*='spinner'] {
    stroke: var(--white);
  }

  div[class*='menuWrap'] {
    align-items: flex-end;
    display: flex;
    height: size(26);
    width: size(26);

    div[class*='container'] > button {
      height: size(20);
      width: size(20);

      svg {
        fill: #ffffff;
      }
    }
  }
}

@include for-dark-theme() {
  .top {
    svg[class*='spinner'] {
      stroke: var(--grey-900);
    }

    div[class*='menuWrap'] {
      div[class*='container'] > button {
        svg {
          fill: #ffffff;
        }
      }
    }
  }
}

.bottom {
  @include forMarginPadding(margin, auto, 0, 0, 0);
}

p.continueLabel {
  font-size: size(16);
  text-decoration: underline;
  text-transform: lowercase;
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));

  &::first-letter {
    text-transform: uppercase;
  }
}

@include for-all-phone() {
  .content {
    @include forMarginPadding(padding, size(16), size(16), size(16), size(16));
  }

  .top {
    p {
      font-size: size(12);
    }
  }
}

@include for-dark-theme() {
  .card {
    color: #ffffff;
    fill: #ffffff;
  }

  .provider {
    color: var(--grey-800);
  }

  .favorite {
    fill: #ffffff;
  }
}
