'use client';

import { FC, useMemo } from 'react';
import ArticleCard from '@/ui/cards/article';
import CardsSection from '../cards-section';

// Styles
import logo from '@public/images/icon-1.png';
import EmptyMessage from '@/ui/empty-message';

interface IMyScholarships {
  isAppliedVariant?: boolean;
}

const Scholarships: FC<IMyScholarships> = (props) => {
  const { isAppliedVariant = false } = props;

  // Creating items dynamically
  const items = useMemo(() => {
    return Array(5)
      .fill(logo.src)
      .map((item, index) => (
        <ArticleCard
          isApplied={isAppliedVariant}
          key={index}
          image={item}
          title="Master of Applied Data Science"
          college="New York University"
          duration="3 months"
          location="International"
          deadline="14 Oct 2024"
        />
      ));
  }, [isAppliedVariant]);

  return (
    <CardsSection
      title="Scholarships"
      description="Use the opportunity offered to you, to pursue first class education offered by internationally recognised institutions."
      listingPageUrl="#"
      items={items}
      limit={3}
      className="scholarshipSlider"
      slidesConfig={{
        desktop: { view: 3, gap: 16 },
        mobile: { view: 1.4, gap: 10 },
      }}
      noDataMessage={
        <EmptyMessage
          icon="search"
          title="No Scholarships Found"
          description="There are no new recommendations at this time. Please check back later."
        />
      }
    />
  );
};

export default Scholarships;
