@use 'mixins' as *;

.coachReview {
  display: flex;
  gap: size(16);

  border-bottom: size(1) solid var(--cool-grey-800);
  @include forMarginPadding(margin, size(0), size(0), size(32), size(0));

  &:last-child {
    border-bottom: none;
  }

  .userImage {
    i {
      display: flex;
      justify-content: center;
      align-items: center;
      @include borderRadius(50%);
      background-color: var(--grey-400);
      width: size(48);
      height: size(48);

      svg {
        fill: var(--grey-900);
        width: size(24);
        height: size(24);
      }
    }
  }

  .header {
    @include forMarginPadding(padding, size(0), size(0), size(16), size(0));

    .userName {
      font-size: size(18);
      color: var(--grey-900);
      @include forMarginPadding(padding, size(0), size(0), size(10), size(0));
    }

    .ratingAndDate {
      display: flex;
      align-items: flex-end;
      gap: size(14);

      .rating {
        display: flex;
        align-items: center;
        gap: size(2.88);
        line-height: 1;
        @include rightToLeft(size(16));

        i {
          width: size(24);
          height: size(24);
          position: relative;
          @include forMarginPadding(margin, size(0), size(5), size(0), size(0));
        }
      }

      .date {
        font-size: size(14);
        color: var(--grey-700);
      }
    }
  }

  .comment {
    color: var(--grey-800);
  }
}
