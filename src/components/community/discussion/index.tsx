'use client';

import React, { useState } from 'react';
import ShareYourThoughts from '../share-your-thoughts';
import ShareYourThoughtsPopup from '../share-your-thoughts/popup';
import Posts from '../posts';
import ICommunityDetails, {
  ICommunityDiscussion,
} from '@/types/domain/my-network/communityDetails';
import EmptyMessage from '@/ui/empty-message';
import dictionary from '@/dictionaries';

interface IDiscussionProps {
  discussions: ICommunityDiscussion[] | undefined;
  communityDetails: ICommunityDetails | null;
}

export enum PostTypeEnum {
  discussion = 'DISCUSSION',
  question = 'QUESTION',
  celebration = 'CELEBRATION',
}

// Community discussion component
const Discussion: React.FC<IDiscussionProps> = ({
  discussions,
  communityDetails,
}) => {
  const [showShareYourThoughtsPopup, setShowShareYourThoughtsPopup] =
    useState<boolean>(false);
  const [postType, setPostType] = useState<PostTypeEnum>(
    PostTypeEnum.discussion,
  );

  const openDialog = () => {
    setShowShareYourThoughtsPopup(true);
  };

  const closeDialog = () => {
    setShowShareYourThoughtsPopup(false);
  };

  return (
    <>
      {communityDetails?.is_member && (
        <ShareYourThoughts
          handleOpenDialog={openDialog}
          postType={postType}
          handleSetPostType={setPostType}
        />
      )}
      {showShareYourThoughtsPopup && (
        <ShareYourThoughtsPopup
          title={communityDetails?.title}
          imageUrl={communityDetails?.imageurl}
          postType={postType}
          handleCloseDialog={closeDialog}
        />
      )}
      {discussions &&
      discussions.length > 0 &&
      (communityDetails?.is_member || communityDetails?.private === 0) ? (
        <Posts
          discussions={discussions}
          isMember={communityDetails?.is_member}
        />
      ) : (
        <EmptyMessage
          icon="discussions"
          title={dictionary.noDiscussionStarted}
          description={dictionary.beFirstToStartDiscussion}
        />
      )}
    </>
  );
};

export default Discussion;
