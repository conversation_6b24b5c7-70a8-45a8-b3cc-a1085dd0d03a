import { FC } from 'react';
import styles from './style.module.scss';
import starOutline from '@public/images/star-outline.svg';
import star from '@public/images/star.svg';
import Image from 'next/image';

interface IRatingProps {
  rating: number;
}

const Rating: FC<IRatingProps> = ({ rating }) => {
  return (
    <div className={styles.container}>
      {Array.from(Array(5).keys()).map((_item, index) => (
        <Image
          key={index}
          src={Math.round(rating) >= index + 1 ? star : starOutline}
          width={16}
          height={16}
          alt="Star icon"
        />
      ))}
    </div>
  );
};

export default Rating;
