import Typography from '@/ui/typography';
import InputField from '@/ui/form/input-field';
import Button from '@/ui/form/button';
import usePersonalListsStore from '@/stores/learning/personal-lists';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';
import styles from './style.module.scss';

const AddNameForm = () => {
  const {
    validateAndAddListName,
    listName,
    listNameError,
    setAddListFormState,
  } = usePersonalListsStore();

  const t = useTranslations();

  return (
    <>
      <Typography
        className={styles.dialogDrawerTitle}
        dictionary={dictionary.createNewList}
      />
      <InputField
        type="text"
        placeholder={t(dictionary.enterANewListName)}
        error={listNameError && t(listNameError)}
        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
          validateAndAddListName(e.target.value);
        }}
      />
      <Button
        className={styles.addNameButton}
        disabled={!listName || !!listNameError}
        onClick={() => {
          setAddListFormState('add-courses');
        }}
      >
        {t(dictionary.next)}
      </Button>
    </>
  );
};

export default AddNameForm;
