@use 'mixins' as *;

.headerFixed {
  background: var(--white);
  @include forMarginPadding(padding, size(30), size(0), size(0), size(0));

  nav[class*='head'] {
    border-bottom: solid 2px var(--grey-500);

    button {
      &:before {
        bottom: -2px;
        height: 2px;
      }
    }
  }
}

.descriptionBody {
  @include forMarginPadding(padding, size(30), size(0), size(0), size(0));
  overflow: hidden;

  h4 {
    font-size: size(20);
  }
}

.courseDetailWrapper {
  min-height: size(700);
}

@include for-all-phone() {
  .headerFixed {
    top: 0;
    z-index: 9;

    nav[class*='head'] {
      button {
        &:before {
          bottom: 0px;
          height: 1px;
        }
      }
    }
  }
}
