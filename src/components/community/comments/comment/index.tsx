import Typography from '@/ui/typography';
import styles from './style.module.scss';
import {
  EApiAreaTypes,
  EApiComponentTypes,
  IComment,
} from '@/types/domain/my-network/communityPost';
import { CommentIcon, LikeIcon } from '@/ui/icons';
import dictionary from '@/dictionaries';
import { getFullNameInitials } from '@/utils/helpers';
import Image from 'next/image';
import useAuthStore from '@/stores/auth';
import CommentKebab from './commentKebab';
import useCommunityDetailsCommentsStore from '@/stores/my-network/community-details/comments';
import ReplyItem from './reply';
import { useState } from 'react';
import ListInput from '../list/list-input';
import LikeFillIcon from '@/ui/icons/like-fill';
import React from 'react';

interface ICommentItem {
  comment: IComment;
  postId: number;
}

const CommentItem: React.FC<ICommentItem> = ({ comment, postId }) => {
  const [showInput, setShowInput] = useState<boolean>(false);
  const [userTag, setUserTag] = useState<string>('');
  const [isPreview, setIsPreview] = useState(false);

  const { totaraProfile } = useAuthStore();
  const { deleteComment, createReply, toggleLikeStatus } =
    useCommunityDetailsCommentsStore();

  const fullNameInitials = getFullNameInitials(comment.userfullname);

  const onClickDelete = () => {
    deleteComment(comment.id, postId);
  };

  const onClickReply = (fullname: string) => {
    setUserTag(fullname);
    setShowInput(true);
  };

  const handleSendReply = (
    commentText: string,
    formData: FormData | undefined,
  ) => {
    createReply(postId, comment.id, commentText, formData);
  };

  const onClickLike = () => {
    toggleLikeStatus(
      postId,
      comment.is_liked,
      EApiAreaTypes.COMMENT,
      EApiComponentTypes.TOTARA_COMMENT,
      comment.id,
    );
  };

  const handlePreview = (isPreview: boolean) => {
    setIsPreview(!isPreview);
  };

  return (
    <div className={styles.commentWrapper}>
      <div className={styles.avatar}>
        <Typography>{fullNameInitials}</Typography>
      </div>

      <div className={styles.commentSection}>
        <div className={styles.contentSection}>
          <div>
            <div className={styles.containerComment}>
              {comment.userid === totaraProfile?.id && (
                <CommentKebab onClickDelete={onClickDelete} />
              )}

              <div className={styles.headerComment}>
                <Typography as="span" className={styles.title}>
                  {comment.userfullname}
                </Typography>
                <span aria-hidden="true" className="dot-breaker"></span>
                <Typography as="span" className={styles.time}>
                  {comment.timecreated}
                </Typography>
              </div>
              <div className={styles.message}>
                <Typography as="p"> {comment.contenttext}</Typography>
              </div>
              {comment.images.length > 0 && (
                <div className={styles.postImage}>
                  {comment.images.map(
                    (image) =>
                      image?.url && (
                        <React.Fragment key={image.url}>
                          <Image
                            src={image.url}
                            alt=""
                            width={200}
                            height={200}
                            onClick={() => handlePreview(isPreview)}
                          />
                          {isPreview && (
                            <div
                              className={styles.lightBoxOverlay}
                              onClick={() => handlePreview(isPreview)}
                            >
                              <div
                                className={styles.lightBoxContent}
                                onClick={(e) => e.stopPropagation()}
                              >
                                <img
                                  src={image.url}
                                  alt={image.url}
                                  className={styles.lightBoxImage}
                                />
                              </div>
                            </div>
                          )}
                        </React.Fragment>
                      ),
                  )}
                </div>
              )}
            </div>

            <div className={styles.containerLike}>
              <button
                type="button"
                className={styles.ftButton}
                onClick={onClickLike}
              >
                {comment.is_liked ? (
                  <i className={styles.iconLike}>
                    <LikeFillIcon />
                  </i>
                ) : (
                  <i>
                    <LikeIcon />
                  </i>
                )}
                <Typography as="span">
                  <Typography as="span" dictionary={dictionary.like} /> (
                  {comment.likes})
                </Typography>
              </button>
              <button
                type="button"
                className={styles.ftButton}
                onClick={() => onClickReply(comment.userfullname)}
              >
                <i>
                  <CommentIcon />
                </i>
                <Typography as="span">
                  <Typography as="span" dictionary={dictionary.reply} /> (
                  {comment.replies.length})
                </Typography>
              </button>
            </div>
            <div className={styles.replyWrapper}>
              {comment.replies.map((reply) => (
                <ReplyItem
                  reply={reply}
                  key={reply.id}
                  postId={postId}
                  setUserTag={onClickReply}
                  commentId={comment.id}
                />
              ))}
            </div>
          </div>
        </div>
        {showInput && (
          <ListInput onClickSend={handleSendReply} text={`@${userTag}`} />
        )}
      </div>
    </div>
  );
};
export default CommentItem;
