import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraRemovePersonalListService } from '@/services/totara/personal-lists';

export async function GET(request: NextRequest) {
  const id = request.nextUrl.searchParams.get('id') || '';

  try {
    const response = await TotaraRemovePersonalListService(id);

    return NextResponse.json({
      removedList: response?.data || null,
    });
  } catch (error) {
    return ErrorResponse(error);
  }
}
