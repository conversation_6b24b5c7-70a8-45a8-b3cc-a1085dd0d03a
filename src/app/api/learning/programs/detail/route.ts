import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchProgramDetailService } from '@/services/totara/programs';

// Route to get the program details
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const programId = request.nextUrl.searchParams.get('programId');

    if (!programId) {
      return NextResponse.json({
        error: 'Please provide programId!',
      });
    }
    // Fetch the data
    const response = await TotaraFetchProgramDetailService(programId);

    // Return the response
    return NextResponse.json({
      program: response?.data || null,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
