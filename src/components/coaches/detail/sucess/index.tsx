import { FC, useMemo } from 'react';
import Typography from '@/ui/typography';
import Button from '@/ui/form/button';
import { CalendarOutline, Time, Pin, Close } from '@/ui/icons';
import styles from './style.module.scss';
import useCoachBookingStore from '@/stores/learning/coaches/booking';
import moment from 'moment';
import { formatLocalTime } from '@/utils/time';
import GreenTick from '@/ui/icons/green-tick';
import dictionary from '@/dictionaries';
import { useLocale } from 'next-intl';
import Image from 'next/image';
import teamsIcon from '@public/images/teams.png';

interface IBookingSuccess {
  onClose: () => void;
  viewAllBookings: () => void;
  coachName: string | undefined;
  coachImage: string | undefined;
  designation: string | undefined;
}

const BookingSuccess: FC<IBookingSuccess> = ({
  onClose,
  viewAllBookings,
  coachName,
  coachImage,
  designation,
}) => {
  // Getting selected slot
  const selected = useCoachBookingStore((x) => x.selected);
  // const t = useTranslations();
  const locale = useLocale();
  // Formating the date
  const date = useMemo(() => {
    return moment(selected.slot_date, 'ddd MMM DD YYYY', 'en')
      .locale(locale === 'ar' ? 'ar' : 'en')
      .format('ddd, DD MMM YYYY');
  }, [selected?.slot_date, locale]);

  // Formating the start time
  const start = useMemo(() => {
    // If start is available
    if (selected?.time_from) {
      return formatLocalTime(selected?.time_from);
    }
    // Returning null
    return null;
  }, [selected?.time_from]);

  // Formating the end time
  const end = useMemo(() => {
    // If start is available
    if (selected?.time_to) {
      return formatLocalTime(selected?.time_to);
    }
    // Returning null
  }, [selected?.time_to]);

  // Return JSX
  return (
    <div className={styles.successView}>
      <div className={styles.coachModalHeader}>
        <button
          type="button"
          aria-label="Close dialog"
          className={styles.backBtn}
          onClick={onClose}
        >
          <i>
            <Close />
          </i>
        </button>
      </div>

      <div className={styles.header}>
        <div className={styles.tick}>
          <i className={styles.icon + ' fa-icon'}>
            <GreenTick />
          </i>
        </div>
        <Typography as="h3" className={styles.headerTitle}>
          Session Booked
        </Typography>
        <Typography as="h4" className={styles.description}>
          Your session has been booked successfully.
        </Typography>
      </div>
      <div className={styles.details}>
        <nav>
          <Typography className={styles.detailsTitle}>
            MENTORSHIP SESSION WITH
          </Typography>
          <ul>
            <li>
              <div className={styles.nameAndDesignation}>
                <img
                  src={coachImage as string}
                  alt="coach photo"
                  className={`img-fluid ${styles.photo}`}
                />
                <Typography as="p" className={styles.coachName}>
                  {coachName}
                </Typography>
                <Typography as="p" className={styles.designation}>
                  {designation}
                </Typography>
              </div>
            </li>
            <li>
              <Typography className={styles.bookingDetailsTitle}>
                BOOKING DETAILS
              </Typography>
            </li>
            <li>
              <div className={styles.timeAndDate}>
                <div className={styles.flexContainer}>
                  <i className="fa-icon">
                    <CalendarOutline />
                  </i>
                  <Typography as="span" className={styles.detailsText}>
                    {date}
                  </Typography>
                </div>
                <div className={styles.flexContainer}>
                  <i className="fa-icon">
                    <Time />
                  </i>
                  <Typography as="span" className={styles.detailsText}>
                    {start?.locale('en').format('h:mma')} -{' '}
                    {end?.locale('en').format('h:mma')}
                  </Typography>
                </div>
              </div>
            </li>
            {selected.meeting_link && (
              <li>
                <i>
                  <Image src={teamsIcon} alt="" className="img-fluid" />
                </i>
                <Typography
                  as="span"
                  dictionary={dictionary.viaMicrosoftTeams}
                />
              </li>
            )}
            {selected.location && (
              <li>
                <i className="fa-icon">
                  <Pin />
                </i>
                <Typography as="span" className={styles.detailsText}>
                  {selected.location}
                </Typography>
              </li>
            )}
          </ul>
        </nav>
      </div>
      <Button type="button" color="black" onClick={viewAllBookings}>
        View all bookings
      </Button>
    </div>
  );
};

export default BookingSuccess;
