import { useCallback, useEffect, useState } from 'react';
import ChipButton from '@/ui/form/chip-button';
import Typography from '@/ui/typography';
import useCoursesFiltersStore from '@/stores/learning/courses/filters/advance';
import useSearchCoursesStore from '@/stores/learning/courses/search';
import { convertDurationFilter } from '@/utils/helpers';
import Loading from '@/components/common/loading';

// Styles
import styles from './style.module.scss';
import dictionary from '@/dictionaries';
import { RightArrow } from '@/ui/icons';

const Durations = () => {
  const [isAccordionOpen, setIsAccordionOpen] = useState(true);
  const isFetching = useCoursesFiltersStore((x) => x.isFetching);
  // Getting the types
  const list = useCoursesFiltersStore((x) => x?.list?.durations);
  // Getting selected filter
  const selected = useSearchCoursesStore((x) => x.filters?.duration);

  // Getting method to set filter
  const setFilter = useSearchCoursesStore((x) => x.setFilter);

  useEffect(() => {
    if (selected) {
      setIsAccordionOpen(true);
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const handleAccordionToggle = () => {
    setIsAccordionOpen((prev) => !prev);
  };

  // Method to handle click
  const onClick = useCallback(
    (x?: string) => {
      if (x) {
        setFilter({
          key: 'duration',
          value:
            selected === convertDurationFilter(x)
              ? undefined
              : convertDurationFilter(x),
        });
      }
    },
    [setFilter, selected],
  );

  // Return JSX
  return (
    <nav className={styles.filter}>
      <div className={styles.filterHeader} onClick={handleAccordionToggle}>
        <Typography as="h4" dictionary={dictionary.Duration} />
        <div
          className={`${styles.accordionAction} ${isAccordionOpen ? styles.openState : styles.closeState}`}
        >
          <RightArrow />
        </div>
      </div>

      {isAccordionOpen && (
        <>
          {isFetching ? (
            <div className={styles.loadingContainer}>
              <Loading />
            </div>
          ) : (
            <ul className={styles.listContainer}>
              {list?.map((x, i) => (
                <li key={'type-' + i}>
                  <ChipButton
                    variant="rounded"
                    active={selected === convertDurationFilter(x)}
                    onClick={() => onClick(x)}
                  >
                    {x}
                  </ChipButton>
                </li>
              ))}
            </ul>
          )}
        </>
      )}
    </nav>
  );
};

// Export the component
export default Durations;
