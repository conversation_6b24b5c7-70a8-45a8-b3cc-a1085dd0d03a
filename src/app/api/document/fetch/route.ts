import { ExternalDocumentFetchService } from '@/services/external/document/document.service';
import { NextRequest, NextResponse } from 'next/server';

/**
 * Endpoint to fetch document
 */
export async function GET(request: NextRequest) {
  // Getting response properties
  const id = request.nextUrl.searchParams.get('id');

  // Try fetching document
  try {
    // Getting user form liferay
    const response = await ExternalDocumentFetchService(id || '');
    // If content exist
    const content = response?.data?.contentUrl;
    const fileName = response?.data?.fileName;
    // Generate URL to fetch document
    const source = `/api/document/file?url=${content}&filename=${fileName}`;
    // If response exist
    if (response?.data?.contentUrl) {
      // Return the response
      return NextResponse.json({
        src: source,
      });
    }
  } catch (e) {
    console.log(e);
  }

  // Do whatever you want
  return NextResponse.json({ src: '' });
}
