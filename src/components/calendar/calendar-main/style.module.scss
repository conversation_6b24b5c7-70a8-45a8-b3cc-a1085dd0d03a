@use 'mixins' as *;

.calendarContentArea {
  padding-top: size(21);

  &.emptyState {
    height: size(620);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}

.calendarMainContainer {
  [class*='dialogDrawerActions'] {
    padding-right: 0;
  }
  dialog > div {
    display: flex;
    flex-direction: column;
    padding: size(22) size(24) size(24) !important;
  }
}
