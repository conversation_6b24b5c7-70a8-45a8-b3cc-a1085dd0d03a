import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchLearningPathwaysService } from '@/services/totara/learning-pathways';

/**
 * Route to get the announcements
 */
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const type = request.nextUrl.searchParams.get('type') || 'personal';
    const page = request.nextUrl.searchParams.get('page') || '1';
    const perPage = request.nextUrl.searchParams.get('perPage') || '10';
    // Updating user profile
    const response = await TotaraFetchLearningPathwaysService({
      type: type,
      page: page,
      perPage: perPage,
    });
    // Mapping the entries into required data
    const data = response?.data?.pathways?.[type] || {};

    // Return the response
    return NextResponse.json({
      pagination: {
        page: data?.pagination_info?.current_page || 1,
        perPage: data?.pagination_info?.page_size || 10,
        total: data?.pagination_info?.total_items || 0,
        totalPages: data?.pagination_info?.total_pages || 0,
      },
      list: data?.contents || [],
      filters: data?.filters || [],
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
