@use 'mixins' as *;

.successView {
  background: var(--white);
  @include forMarginPadding(padding, size(0), size(40), size(0), size(40));

  .coachModalHeader {
    display: flex;
    justify-content: flex-end;
    height: size(72);
    position: relative;
    padding: size(20);

    .backBtn {
      @include leftToRight(size(24));
      top: size(24);
      width: size(30);
      height: size(30);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 7;
      background: var(--white);
      border: 1px solid var(--grey-900);
      @include borderRadius(50%);
      cursor: pointer;
      @include transitions(0.5s);
      fill: var(--grey-900);

      i {
        width: size(20);
        height: size(20);
        display: flex;
        padding: size(5);

        svg {
          fill-rule: evenodd;
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .header {
    display: flex;
    flex-direction: column;
    align-items: center;
    @include forMarginPadding(padding, size(56), size(55), size(64), size(55));

    .tick {
      width: size(48);
      height: size(48);
      @include forMarginPadding(margin, size(0), size(0), size(24), size(0));
    }

    .headerTitle {
      font-size: size(28);
      font-weight: 700;
      line-height: 1.2;
      letter-spacing: 0;
    }

    .description {
      font-size: size(20);
      font-weight: 400;
      line-height: 1.2;
      letter-spacing: 0;
      color: var(--grey-800);
    }
  }

  .details {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    @include forMarginPadding(padding, size(30), size(40), size(40), size(40));
    @include forMarginPadding(margin, size(0), size(0), size(24), size(0));
    @include borderRadius(size(24));
    background: var(--bg-shade-4);

    nav {
      display: flex;
      flex-direction: column;
      width: 100%;

      .detailsTitle {
        font-size: size(16);
        line-height: 120%;
        letter-spacing: 0;
        color: var(--grey-800);
        flex-grow: 1;

        border-bottom: size(1) solid var(--progressBg);
        text-align: center;
        @include forMarginPadding(padding, size(0), size(0), size(27), size(0));
        @include forMarginPadding(margin, size(0), size(0), size(37), size(0));
      }

      ul {
        @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
        @include forMarginPadding(padding, size(0), size(0), size(0), size(0));

        li {
          list-style: none;
          display: flex;
          justify-content: center;
          align-items: center;
          @include forMarginPadding(
            margin,
            size(0),
            size(0),
            size(10),
            size(0)
          );

          .nameAndDesignation {
            display: flex;
            flex-direction: column;
            align-items: center;
            @include forMarginPadding(
              padding,
              size(0),
              size(0),
              size(56),
              size(0)
            );

            .photo {
              width: size(104);
              height: size(104);
              @include borderRadius(50%);
            }

            .coachName {
              font-weight: 700;
              font-size: size(28);
              @include forMarginPadding(
                margin,
                size(0),
                size(0),
                size(20),
                size(0)
              );
            }

            .designation {
              font-weight: 400;
              font-size: size(20);
              color: var(--grey-800);
            }
          }

          .bookingDetailsTitle {
            font-weight: 700;
            font-size: size(16);
          }

          .timeAndDate {
            display: flex;
            justify-content: center;
            gap: size(20);
          }

          .detailsText {
            font-size: size(18);
            font-weight: 400;
          }

          i {
            width: size(24);
            height: size(24);
            @include forMarginPadding(
              margin,
              size(0),
              size(10),
              size(0),
              size(0)
            );

            svg {
              vertical-align: top;
            }
          }
        }
      }
    }
  }

  .time {
    text-transform: lowercase;
  }
}

.flexContainer {
  display: flex;
}

@include for-all-phone() {
  .popup {
    width: 90%;
    height: size(480);

    button {
      font-size: size(18);
      height: size(56);
      text-align: center;
    }
  }
}
