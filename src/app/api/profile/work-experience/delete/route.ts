import { NextRequest, NextResponse } from 'next/server';
import { ExternalDeleteUserWorkExperienceService } from '@/services/external/profile/work-experience';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';

// To handle a DELETE request to /api
export async function DELETE(request: NextRequest) {
  try {
    // Getting request body
    const workExperienceId: string = await request.json();
    // Updating user profile
    const response =
      await ExternalDeleteUserWorkExperienceService(workExperienceId);
    // Return the response
    return NextResponse.json({ user: response?.data });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error as AxiosError);
  }
}
