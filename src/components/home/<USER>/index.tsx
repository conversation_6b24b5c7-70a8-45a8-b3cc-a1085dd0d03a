import Image from 'next/image';
import Typography from '@/ui/typography';
import Button from '@/ui/form/button';
import talents_img from '@public/images/talents_img.png';

// Styles
import styles from './style.module.scss';
import { useTranslations } from 'next-intl';
import dictionaries from '@/dictionaries';
import DialogDrawer from '@/ui/dialog-drawer';
import { useState } from 'react';
import RecommendATalentStepper from './RecommendATalentStepper';
import useRecommendTalentStore from '@/stores/recommend-talent';
import PopupSuccess from '@/ui/popup-success';
import dictionary from '@/dictionaries';
import useStepForm from '@/hooks/form/useStepForm';

const RecommendTalent = () => {
  const t = useTranslations();
  const { resetStep, currentStep, nextStep, prevStep } = useStepForm();

  const [openRecommendATalent, setOpenRecommendATalent] = useState(false);
  const {
    setEmptyRecommendTalentData,
    setIsPopupActive,
    isPopupActive,
    setEmailError,
  } = useRecommendTalentStore();

  const openDrawer = () => {
    setOpenRecommendATalent(true);
  };

  const onClose = () => {
    setTimeout(() => {
      setEmptyRecommendTalentData();
    }, 500);
    setOpenRecommendATalent(false);
    setEmailError('');
    resetStep();
  };

  const onClosePopup = () => {
    setIsPopupActive(false);
  };

  return (
    <div className={styles.flexBox}>
      <div className={styles.text}>
        <Typography as="h3" className={styles.title}>
          {t(dictionaries.helpUsFindTalent)}
        </Typography>
        <Button size="md" color="black" outline onClick={openDrawer}>
          {t(dictionaries.recommendATalent)}
        </Button>
      </div>
      <div className={styles.img}>
        <Image src={talents_img} alt="" />
      </div>
      <DialogDrawer isOpened={openRecommendATalent} onClose={onClose}>
        <RecommendATalentStepper
          currentStep={currentStep}
          nextStep={nextStep}
          prevStep={prevStep}
          onClose={onClose}
        />
      </DialogDrawer>
      <PopupSuccess
        isOpened={isPopupActive}
        title={dictionary.recommendationSubmittedSuccessfully}
        onClose={onClosePopup}
      >
        {t(dictionary.thankYouForRecommendingALeader)}
      </PopupSuccess>
    </div>
  );
};

export default RecommendTalent;
