@use 'mixins' as *;

.memberItem {
  display: flex;
  align-items: baseline;
  border-bottom: solid 1px var(--grey-400);
  padding-top: size(10);

  &:last-child {
    border-bottom: none;
  }

  .avatar {
    width: size(40);
    height: size(40);
    @include borderRadius(50%);
    @include forMarginPadding(margin, size(0), size(12), size(0), size(0));
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    justify-content: center;
    text-transform: uppercase;
    @include borderRadius(50%);

    p {
      @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
      font-weight: 500;
      font-size: size(16);
    }
  }

  .ownerLabel {
    margin-left: size(10);
    padding: size(10);
    border-radius: 20%;
    background-color: var(--grey-600);
  }
}
