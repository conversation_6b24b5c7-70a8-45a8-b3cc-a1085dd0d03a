import { NextRequest, NextResponse } from 'next/server';
import { ExternalFetchReferredReceivedCoursesService } from '@/services/external/courses';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';

// To handle a GET request to /api
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const id = request.nextUrl.searchParams.get('userId') || '';
    // Fetching user profile
    const response = await ExternalFetchReferredReceivedCoursesService(id);
    // Return the response
    return NextResponse.json({
      receivedReferrals: response?.data,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error as AxiosError);
  }
}
