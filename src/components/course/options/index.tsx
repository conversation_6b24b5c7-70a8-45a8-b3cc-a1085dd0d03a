import { FC, useCallback, useMemo } from 'react';
import ICourse from '@/types/domain/learning/course';
import KababMenu from '@/ui/menu/kabab';
import useReferCourseStore from '@/stores/learning/courses/refer-course';
import { Share, ShareProfile } from '@/ui/icons';
import Typography from '@/ui/typography';
import styles from './style.module.scss';
import dictionary from '@/dictionaries';
import { useLocale } from 'next-intl';
import useShareCourseStore from '@/stores/learning/courses/share';
import { generateShareLink } from '@/utils/share-course';
import usePersonalListsStore from '@/stores/learning/personal-lists';
import DocumentPlus from '@/ui/icons/document-plus';
import useFavoriteCoursesStore from '@/stores/learning/courses/favorite';
interface IOptions {
  course: ICourse;
  className?: string;
}

// React component to handle course link
const Options: FC<IOptions> = (props) => {
  const { course } = props;
  const setVisible = useReferCourseStore((state) => state.setVisible);
  const setCourseInfo = useReferCourseStore((state) => state.setCourseInfo);
  const showShareToast = useShareCourseStore((state) => state.showShareToast);
  const { setSelectedCourse, setShowAddToListFromFavorites } =
    usePersonalListsStore();
  const locale = useLocale();

  const favoriteCoursesList = useFavoriteCoursesStore((state) => state.list);

  const onReferCourse = useCallback(
    (id?: number, name?: string, image?: string) => {
      setCourseInfo(id, name, image);
      setVisible(true);
    },
    [setVisible, setCourseInfo],
  );

  const shareCourse = (course: ICourse) => {
    const textArea = document.createElement('textarea');
    textArea.value = generateShareLink(
      course.id || (course.courseId as number),
      course.title || (course.course_name as string),
      course.description as string,
      course.image || (course.course_image as string),
      locale,
      type?.toLowerCase() === 'program' || type?.toLowerCase() === 'programme',
    );
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    showShareToast();
  };

  // Getting course type
  const type = useMemo(() => {
    return course?.component_type?.toString() || course?.type?.toString();
  }, [course?.component_type, course?.type]);

  const courseIdForCheck = useMemo(() => {
    return course?.id || course?.courseid || course?.courseId || 0;
  }, [course?.id, course?.courseid, course?.courseId]);

  const isFavorite = useMemo(() => {
    if (course?.is_favorite === true) {
      return true;
    }

    if (typeof courseIdForCheck !== 'number' || isNaN(courseIdForCheck)) {
      return false;
    }

    return favoriteCoursesList.some(
      (favCourse) => favCourse.courseid === courseIdForCheck,
    );
  }, [course?.is_favorite, courseIdForCheck, favoriteCoursesList]);

  return (
    <div className={styles.menuWrap}>
      <KababMenu increaseMargin={!!isFavorite}>
        <nav>
          <ul className={styles.menuList}>
            {isFavorite && (
              <li>
                <button
                  type="button"
                  onClick={() => {
                    const id = course.id || course.courseId || course.courseid;
                    if (typeof id === 'number') {
                      setSelectedCourse(id);
                      setShowAddToListFromFavorites(true);
                    } else {
                      console.warn(
                        'Course ID is not a valid number for Add to List:',
                        id,
                      );
                    }
                  }}
                >
                  <i>
                    <DocumentPlus />
                  </i>
                  <Typography as="span" dictionary={dictionary.addToList} />
                </button>
              </li>
            )}
            {type !== 'program' && type !== 'Program' ? (
              <li>
                <button
                  type="button"
                  onClick={() =>
                    onReferCourse(
                      course.id || course.courseid,
                      course.title,
                      course.image,
                    )
                  }
                >
                  <i className={styles.shareIcon}>
                    <ShareProfile />
                  </i>
                  <Typography as="span" dictionary={dictionary.referACourse} />
                </button>
              </li>
            ) : (
              ''
            )}
            <li>
              <button type="button" onClick={() => shareCourse(course)}>
                <i>
                  <Share />
                </i>
                <Typography as="span" dictionary={dictionary.Share} />
              </button>
            </li>
          </ul>
        </nav>
      </KababMenu>
    </div>
  );
};

// Exporting course component
export default Options;
