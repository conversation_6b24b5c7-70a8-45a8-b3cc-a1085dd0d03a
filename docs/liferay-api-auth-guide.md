# Liferay API Authentication Guide
## Complete Guide for UAE Government Platform Integration

**Last Updated:** January 30, 2025  
**Platform:** Tomouh Government Learning Platform  
**API Base URL:** `https://admin.stg.workspace.dge.gov.ae/o`

---

## 🔐 **Authentication Methods**

The Liferay API supports **two primary authentication methods**:

#### 1. **UAE Pass Authentication (Recommended for Government Users)**
#### 2. **Email/Password Authentication (Direct Login)**

---

## 🇦🇪 **Method 1: UAE Pass Authentication Flow**

### **Step 1: Initiate UAE Pass Login**
```typescript
// Redirect user to UAE Pass authentication
GET /auth/uae-pass/attempt?from={returnPath}
```

### **Step 2: UAE Pass Authorization**
The system redirects to:
```
https://stg-id.uaepass.ae/idshub/authorize?
  redirect_uri={your_return_url}&
  client_id=sandbox_stage&
  state=ShNP22hyl1jUU2RGjTRkpg==&
  response_type=code
```

### **Step 3: Handle UAE Pass Return**
```typescript
// UAE Pass returns with authorization code
GET /auth/uae-pass/return?code={authorization_code}&state={state}
```

### **Step 4: Exchange Code for Liferay Token**
```typescript
export const ExternalUAEPassAuthenticate = async (data: {
  code: string;
  returnURL: string;
}) => {
  const fetcher = await serverFetcher.liferay();
  
  return fetcher.request({
    url: '/adsg-authentication/v1.0/authenticate',
    method: 'POST',
    params: {
      isLocal: data.returnURL.includes('localhost'),
      source: 'ga-tomouh-web-app',
    },
    data: {
      accessCode: data.code,
      redirectURL: data.returnURL,
    },
  });
};
```

---

## 📧 **Method 2: Email/Password Authentication**

### **Direct Login Request**
```typescript
export const ExternalUserLoginService = async (data: {
  email: string;
  password: string;
}) => {
  const fetcher = await serverFetcher.liferay({
    withBearer: false,
  });

  return fetcher.request({
    url: '/adsg-authentication/v1.0/authenticate',
    method: 'POST',
    headers: {
      Authorization: 'Basic <EMAIL>:Liferay@DGE',
    },
    params: {
      source: 'ga-mobile-app',
    },
    data: {
      email: data.email,
      password: data.password,
    },
  });
};
```

---

## 🔑 **Using Bearer Token for API Calls**

### **After Authentication**
Once authenticated, use the Bearer token for all subsequent API calls:

```typescript
// Example: Fetch user profile
const fetcher = await serverFetcher.liferay({
  withBearer: true,  // This adds Authorization: Bearer {token}
});

const response = await fetcher.request({
  url: '/adsg-profiles-search-results/v1.0/search?userId={id}',
  method: 'GET',
});
```

### **Server Fetcher Configuration**
```typescript
public liferay = async (options: IOptions = {
  withBearer: true,
  isMultipart: false,
}) => {
  const headers = this.headers();
  
  // Adding Bearer token for authentication
  if (options.withBearer) {
    headers['Authorization'] = await getUserToken();
  }
  
  const instance = axios.create({
    baseURL: process.env.LIFERAY_API, // https://admin.stg.workspace.dge.gov.ae/o
    headers: headers,
  });
  
  return instance;
};
```

---

## 📋 **Complete Authentication Flow**

### **1. Environment Configuration**
```bash
# Required environment variables
LIFERAY_API=https://admin.stg.workspace.dge.gov.ae/o
UAE_PASS_URL=https://stg-id.uaepass.ae
UAE_PASS_ID=sandbox_stage
UAE_PASS_STATE=ShNP22hyl1jUU2RGjTRkpg==
```

### **2. Authentication Endpoints**
| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/adsg-authentication/v1.0/authenticate` | POST | UAE Pass & Email authentication |
| `/adsg-authentication/v1.0/logout` | DELETE | User logout |
| `/oauth2/token` | POST | OAuth2 token endpoint |

### **3. API Usage Examples**

#### **User Profile Management**
```typescript
// Fetch user profile
GET /adsg-profiles-search-results/v1.0/search?userId={id}

// Update user profile  
PATCH /adsg-profiles-search-results/v1.0/updateUser?userId={id}

// Search profiles
GET /adsg-profiles-search-results/v1.0/profile-search?keyword={keyword}
```

#### **Document Management**
```typescript
// Upload document
POST /adsg-document-upload/v1.0/obs-document/{folder}

// Get document URL
GET /adsg-document-upload/v1.0/obs-document-url/{id}
```

#### **One-Time Password**
```typescript
// Generate OTP
POST /adsg-smpp-verification/v1.0/generate-code

// Verify OTP
POST /adsg-smpp-verification/v1.0/verify-code
```

---

## 🔗 **Complete API Endpoint Reference**

### **Authentication Endpoints**
- `POST /adsg-authentication/v1.0/authenticate` - UAE Pass & Email authentication
- `DELETE /adsg-authentication/v1.0/logout` - User logout
- `POST /oauth2/token` - OAuth2 token endpoint

### **User Management Endpoints**
- `GET /adsg-talent-team/v1.0/profile-progress/{id}` - User progress tracking
- `GET /adsg-profiles-search-results/v1.0/search?userId={id}` - User profile fetch
- `PATCH /adsg-profiles-search-results/v1.0/updateUser?userId={id}` - User profile update
- `GET /adsg-profiles-search-results/v1.0/profile-search?keyword={keyword}` - Profile search

### **Document Management Endpoints**
- `GET /adsg-document-upload/v1.0/obs-document-url/{id}` - Document URL fetch
- `POST /adsg-document-upload/v1.0/obs-document/{folder}` - Document upload

### **One-Time Password Endpoints**
- `POST /adsg-smpp-verification/v1.0/generate-code` - OTP generation
- `POST /adsg-smpp-verification/v1.0/verify-code` - OTP verification

### **Profile & Recommendations**
- `GET /adsg-talent-recommendations/v1.0/recommendations/list` - Get recommendations
- `POST /adsg-talent-recommendations/v1.0/add/recommendation` - Ask for recommendations
- `PUT /adsg-talent-recommendations/v1.0/update/recommendation/{id}` - Update recommendation

### **Work Experience & Skills**
- `POST /c/workexperiences` - Add work experience
- `PUT /c/workexperiences/{id}` - Edit work experience
- `DELETE /c/workexperiences/{id}` - Delete work experience
- `GET /c/skillsendorsementses?filter=skillName eq '{skillName}' and talentProfileId eq '{talentProfileId}'` - Get endorsements
- `PUT /c/skillsendorsementses/{id}` - Update endorsement

### **System Data**
- `GET /headless-admin-list-type/v1.0/list-type-definitions?filter=name eq '{id}'` - Picklist fetch
- `GET /c/countries?pageSize=-1` - Get countries list

### **Course Management**
- `GET /adsg-courses/v1.0/courses/referred/received?userId={id}&page=1&pageSize=100` - Received course referrals
- `GET /adsg-courses/v1.0/courses/referred/sent?userId={id}&page=1&pageSize=100` - Sent course referrals
- `POST /adsg-courses/v1.0/courses/referred/course` - Refer course

### **Support Center**
- `GET /adsg-faq-headless/v1.0/faq` - FAQ system

---

## ⚠️ **Security Considerations**

### **🚨 Critical Issues Found**
1. **Hardcoded Basic Auth**: `Authorization: 'Basic <EMAIL>:Liferay@DGE'`
2. **Exposed Credentials**: Basic auth credentials in source code
3. **Token Management**: Ensure proper token storage and rotation

### **🔒 Best Practices**
1. **Use UAE Pass** for government users (more secure)
2. **Store tokens securely** (encrypted storage)
3. **Implement token refresh** logic
4. **Add request/response logging** for debugging
5. **Use HTTPS only** for all API calls

---

## 🛠️ **Quick Start Example**

```typescript
// 1. Initialize authentication
const authenticateWithLiferay = async (email: string, password: string) => {
  try {
    // Option A: UAE Pass (recommended)
    window.location.href = '/auth/uae-pass/attempt';
    
    // Option B: Email/Password
    const response = await fetch('/api/auth/email-login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password }),
    });
    
    const authData = await response.json();
    return authData;
  } catch (error) {
    console.error('Authentication failed:', error);
  }
};

// 2. Make authenticated API calls
const fetchUserProfile = async (userId: string) => {
  const response = await fetch(`/api/profile/fetch?userId=${userId}`, {
    headers: {
      'Authorization': `Bearer ${userToken}`,
    },
  });
  
  return response.json();
};
```

---

## 🔄 **Token Lifecycle Management**

### **Token Storage**
- **UAE Pass Tokens**: Government-managed lifecycle
- **Liferay Tokens**: Application-managed with refresh capability
- **Session Management**: Secure server-side session storage

### **Token Refresh**
```typescript
// Implement token refresh logic
const refreshToken = async () => {
  try {
    const response = await fetch('/api/auth/refresh', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${currentToken}`,
      },
    });
    
    const newTokenData = await response.json();
    return newTokenData;
  } catch (error) {
    // Redirect to login if refresh fails
    window.location.href = '/login';
  }
};
```

### **Logout Process**
```typescript
const logout = async () => {
  try {
    await fetch('/api/auth/logout/attempt', {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${userToken}`,
      },
    });
    
    // Clear local storage and redirect
    localStorage.clear();
    window.location.href = '/login';
  } catch (error) {
    console.error('Logout failed:', error);
  }
};
```

---

## 📞 **Support & Troubleshooting**

### **Common Issues**
1. **401 Unauthorized**: Check Bearer token validity
2. **403 Forbidden**: Verify user permissions
3. **UAE Pass Errors**: Check state parameter and redirect URLs
4. **CORS Issues**: Ensure proper domain configuration

### **Debug Tips**
1. **Enable request logging** for API calls
2. **Check network tab** for detailed error responses
3. **Verify environment variables** are correctly set
4. **Test with Postman** for API endpoint validation

This guide provides complete coverage of Liferay API authentication for the UAE government platform integration.
