import { FC } from 'react';
import Typography from '@/ui/typography';
import IBadge from '@/types/domain/learning/badge';

import Image from '@/ui/image';
import fallback from '@public/images/pro-perfomer.png';

// Styles
import styles from './style.module.scss';
import Link from '@/components/course/link';
import ICriteria from '@/types/domain/learning/badge/criteria';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';

interface ISingle {
  badge: IBadge;
}

const Single: FC<ISingle> = (props) => {
  // Destructuring props
  const { badge } = props;
  const t = useTranslations();
  const criteriaTitle = (criteria: ICriteria) => {
    if (!criteria?.title) return null; // Handle cases where x.title is undefined

    return criteria.type ? (
      <>
        <Typography as="h6">{criteria.title}</Typography>
        <Link
          course={{ ...criteria, type: criteria.type, id: criteria.id || 0 }}
        >
          <Typography
            as="span"
            className={styles.helpTxt}
            dictionary={
              criteria.type.toLowerCase() === 'program' ||
              criteria.type.toLowerCase() === 'programme'
                ? dictionary.viewProgramDetails
                : dictionary.viewCourseDetails
            }
          />
        </Link>
      </>
    ) : (
      <Typography as="h6">{criteria.title}</Typography>
    );
  };

  const getOperator = (operatorValue: string | undefined): string => {
    const lowerOperator = operatorValue?.toLowerCase();
    return lowerOperator === t('all') ? t('and') : t('or');
  };

  const getAwardOperator = (
    awardOperatorValue: string | undefined,
    index: number,
  ): JSX.Element | null => {
    const awardOperator = getOperator(awardOperatorValue);

    if (awardOperator && index === (badge?.award_criteria?.length ?? 0) - 1) {
      return (
        <div className={styles.awardOperator}>
          <span className={styles.awardOperatorLine} />
          <div className={styles.awardOperatorTxt}>
            <Typography as="span">
              {awardOperator.toLocaleUpperCase()}
            </Typography>
          </div>
          <span className={styles.awardOperatorLine} />
        </div>
      );
    }
    return null;
  };

  // Return JSX
  return (
    <div className={styles.popup}>
      <div className={styles.inner}>
        <div className={styles.content}>
          <div className={styles.badge}>
            <Image
              width={100}
              height={100}
              fallback={fallback}
              src={badge?.image_url}
              alt={badge?.name}
            />
          </div>
          <div className={styles.badgeTitle}>
            <Typography as="h4">{badge?.name}</Typography>
          </div>
          <div className={styles.criterias}>
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                width: '100%',
              }}
            >
              <Typography
                as="h5"
                dictionary={dictionary.criteriaToEarnThisBadge}
              />
            </div>
            {badge?.award_criteria?.map((award, i) => (
              <div className={styles.detail} key={'award-' + i}>
                {getAwardOperator(badge?.award_criteria_operator, i)}
                <Typography as="h6">{award?.title}</Typography>
                <nav className={styles.listPoints}>
                  <ol>
                    {award?.criteria?.map((criteriaItem, j) => (
                      <li key={'critaria-' + j}>
                        {criteriaTitle(criteriaItem)}
                        <div className={styles.title}>
                          <Typography as="p">Required grade: </Typography>
                          <Typography as="p">
                            {criteriaItem?.grade || '0'}%
                          </Typography>
                        </div>
                        <div className={styles.title}>
                          <Typography as="p">Completion date:</Typography>
                          <Typography as="p">
                            {criteriaItem?.date ||
                              badge?.completed_date ||
                              'N/A'}
                          </Typography>
                        </div>
                        {(award?.criteria?.length || 0) - 1 !== j && (
                          <div
                            className={
                              getOperator(award?.operator) === t('and')
                                ? styles.andTxt
                                : styles.orTxt
                            }
                          >
                            <Typography as="span">
                              {getOperator(award?.operator).toLocaleUpperCase()}
                            </Typography>
                          </div>
                        )}
                      </li>
                    ))}
                  </ol>
                </nav>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Single;
