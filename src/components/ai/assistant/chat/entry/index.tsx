import { FC, useState, ChangeEvent, useEffect } from 'react';
import styles from './style.module.scss';
import useCompletionStore from '@/stores/ai/completion';
import CompletionPayload from '@/hooks/ai/completion/useCompletionPayload';
import ChatPayload from '@/hooks/ai/chat/useChatPayload';
import { completionTools } from '@/hooks/ai/completion/constants';
import useChatStore from '@/stores/ai/chat';
import IMessages from '@/types/domain/ai/messages';
import { defaultSystem } from '@/hooks/ai/completion/constants';
import useAuthStore from '@/stores/auth';
import useAiUserProfile from '@/hooks/ai/profile/useAiUserProfile';
import useSessionStorageStore from '@/stores/ai/session-storage';
import useGenerativeStore from '@/stores/ai/generative';
import GenerativePayload from '@/hooks/ai/generative/useGenerativePayload';
import IGenerativePayload from '@/types/domain/ai/generativePayload';
import IUser from '@/types/domain/external/user';
import IChatPayload from '@/types/domain/ai/chatPayload';
import {
  titleParams,
  suggestionsParams,
} from '@/hooks/ai/generative/constants';
import { remark } from 'remark';
import html from 'remark-html';
import ISuggestion from '@/types/domain/ai/suggestion';
import Spinner from '@/ui/spinner';
import { Swiper, SwiperSlide } from 'swiper/react';
import PaperAirplane from '@/ui/icons/paper-airplane';
import { useLocale, useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';

interface IAssistant {
  onCloseChat?: (isActive: boolean) => void;
  isExpanded?: boolean;
}

const Entry: FC<IAssistant> = ({ isExpanded }) => {
  const [text, setText] = useState<string>('');
  const [suggestions, setSuggestions] = useState([]);
  const fetchCompletion = useCompletionStore((x) => x.fetch);
  const fetchChat = useChatStore((x) => x.fetch);
  const setAiContext = useChatStore((x) => x.setAiContext);
  const setCurrentSession = useChatStore((x) => x.setCurrentSession);
  const currentSession = useChatStore((x) => x.currentSession);
  const aiContext = useChatStore((x) => x.aiContext);
  const resetTypedText = useChatStore((x) => x.resetTypedText);
  const storedSession = useSessionStorageStore((x) => x.storedSession);
  const setStoredSession = useSessionStorageStore((x) => x.setStoredSession);
  const activeHistory = useSessionStorageStore((x) => x.activeHistory);
  const setActiveHistory = useSessionStorageStore((x) => x.setActiveHistory);
  const setActiveDate = useSessionStorageStore((x) => x.setActiveDate);
  const fetchGenerative = useGenerativeStore((x) => x.fetch);
  const hasGenerativeError = useGenerativeStore((x) => x.hasGenerativeError);
  const { setIsGenerating, isGenerating } = useGenerativeStore();
  const user: IUser | null = useAuthStore((x) => x.user);
  const userProfile = useAiUserProfile(user);
  const locale = useLocale();
  const t = useTranslations();

  useEffect(() => {
    if (!activeHistory) {
      setAiContext([
        {
          role: 'system',
          content: defaultSystem.content + userProfile,
        },
      ]);
      fetchSuggestions();
    }
  }, [user, userProfile, activeHistory, setAiContext]); //eslint-disable-line

  const fetchSuggestions = async () => {
    setSuggestions([]);
    const fetchedSuggestions = await fetchGenerative(
      GenerativePayload(
        suggestionsParams.desc,
        suggestionsParams.prompt + userProfile,
        locale,
        true,
      ) as IGenerativePayload,
    );
    setSuggestions(
      JSON.parse(fetchedSuggestions?.data?.choices[0].message.content).prompts,
    );
  };

  const markdownContent = async (content: string) => {
    const processedContent = await remark().use(html).process(content);
    const replacedContent = processedContent.value
      .toString()
      .replace(
        /https:\/\/learn\.adsg\.gov\.ae\/course\/view\.php\?id=(\d+)/g,
        `/${locale}/course/$1`,
      );
    return replacedContent;
  };

  const askAssistant = async (prompt: string) => {
    const userPrompt = {
      role: 'user',
      content: prompt,
    };

    let assistantAnswer = '';
    let suggestions = null;
    setActiveHistory(false);
    setAiContext([...aiContext, userPrompt as IMessages]);
    setCurrentSession([...currentSession, userPrompt as IMessages]);
    setIsGenerating(true);
    setText('');
    setActiveDate('Today');

    if (!storedSession?.id) {
      const titleResponse = await fetchGenerative(
        GenerativePayload(
          titleParams.desc,
          titleParams.prompt + userPrompt.content,
          locale,
          false,
        ) as IGenerativePayload,
      );

      setStoredSession({
        id: Date.now(),
        messages: [],
        context: [],
        date: new Date(),
        title: titleResponse.data.choices[0].message.content,
      });
    } else {
      setStoredSession({
        ...storedSession,
        messages: currentSession,
        context: aiContext,
        date: new Date(),
      });
    }

    const response = await fetchCompletion(
      CompletionPayload(
        [...aiContext, userPrompt] as IMessages[],
        completionTools,
      ),
    );

    const toolCall = response?.data?.choices[0]?.message?.tool_calls?.[0];

    if (toolCall?.function?.name === 'searchCoursesRAG') {
      const args = JSON.parse(toolCall.function.arguments);
      const query = args.query;

      const chatResponse = await fetchChat(ChatPayload(query) as IChatPayload);
      assistantAnswer = chatResponse?.data?.data.agent_response?.text as string;
      suggestions =
        chatResponse?.data?.data.search_results['gov-academy-courses'];
    } else {
      assistantAnswer = response?.data.choices?.[0]?.message?.content as string;
    }

    setAiContext([
      ...aiContext,
      userPrompt as IMessages,
      {
        role: 'assistant',
        content: assistantAnswer,
      },
    ]);
    setCurrentSession([
      ...currentSession,
      userPrompt as IMessages,
      {
        role: 'assistant',
        content: await markdownContent(assistantAnswer),
      },
      {
        role: 'suggestions',
        content: suggestions,
      },
    ] as IMessages[]);
    resetTypedText(true);
    setIsGenerating(false);
  };

  useEffect(() => {
    setCurrentSession(currentSession as IMessages[]);
  }, [setCurrentSession, currentSession]);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    setText(e.currentTarget.value);
  };

  return (
    <div className={styles.entryWrapper}>
      <div
        className={
          styles.suggestionsWrapper +
          ` ${isExpanded ? styles.suggestionsExpanded : ''}` +
          ` ${!currentSession?.length ? styles.suggestionsVisible : ''}`
        }
      >
        {!suggestions.length && !hasGenerativeError ? <Spinner /> : null}
        <Swiper spaceBetween={16} slidesPerView={'auto'}>
          {suggestions.map((suggestion: ISuggestion, index) => (
            <SwiperSlide className={styles.suggestionSlide} key={index}>
              <button
                key={index}
                className={styles.suggestion}
                onClick={() =>
                  askAssistant(`${suggestion.label} ${suggestion.title}`)
                }
              >
                <div className={styles.suggestionLabel}>{suggestion.label}</div>
                <div className={styles.suggestionTitle}>{suggestion.title}</div>
              </button>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
      <div
        className={
          styles.promptWrapper + ` ${isExpanded ? styles.promptExpanded : ''}`
        }
      >
        <input
          className={styles.chatInput}
          onChange={handleChange}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              askAssistant(text);
            }
          }}
          value={text || ''}
          placeholder={t(dictionary.askMeAnything)}
        />
        <button
          className={styles.promptCta}
          disabled={text.length < 1 || (isGenerating as boolean)}
          onClick={() => askAssistant(text)}
        >
          <PaperAirplane />
        </button>
      </div>
    </div>
  );
};

export default Entry;
