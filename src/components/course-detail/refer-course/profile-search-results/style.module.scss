@use 'mixins' as *;

.resultsWrapper {
  background-color: var(--white);
  border-radius: size(8);
  border: solid 1px var(--grey-600);
  @include forMarginPadding(padding, size(0), size(24), size(0), size(24));
  @include forMarginPadding(margin, size(8), size(0), size(0), size(0));
  max-height: size(400);
  overflow-y: auto;
  position: absolute;
  top: size(90);
  width: 100%;
  z-index: 1;

  li {
    border-bottom: 1px solid var(--grey-400);
    list-style: none;
    cursor: pointer;
    font-size: size(16);
    @include forMarginPadding(padding, size(16), size(0), size(16), size(0));

    &:last-child {
      border-bottom: 0;
    }

    svg {
      height: size(30);
      width: size(30);
    }
  }

  @media (min-width: 576px) {
    &::-webkit-scrollbar-track {
      background-color: var(--white);
    }

    &::-webkit-scrollbar {
      width: size(8);
      background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: size(8);
      background-color: var(--grey-900);
    }
  }
}

.resultName {
  font-size: size(16);
}

.result {
  align-items: center;
  display: flex;
  gap: size(12);

  img {
    border-radius: 50%;
    height: size(30);
    width: size(30);
  }
}

.hidden {
  display: none;
}
