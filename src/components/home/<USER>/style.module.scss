@use 'mixins' as *;

.flexBox {
  display: flex;
  align-items: flex-end;
  width: 100%;
  min-height: size(350);
  overflow: hidden;
  margin-bottom: size(20);
  background: var(--white);
  @include borderRadius(var(--radius));
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
}

.text {
  width: 45%;
  @include forMarginPadding(padding, size(40), 8%, size(40), size(40));

  h3 {
    font-weight: 500;
    margin-bottom: 5.5rem;
  }

  a {
    display: inline-block;
    width: initial;
    @include borderRadius(var(--radius));
  }
}

.img {
  width: 55%;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    vertical-align: bottom;
  }
}

@include for-all-phone() {
  .flexBox {
    display: block;
  }

  .text {
    @include forMarginPadding(padding, size(25), size(16), size(25), size(16));
  }

  .text,
  .img {
    width: 100%;
  }
}
