import Typography from '@/ui/typography';
import styles from './style.module.scss';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';
import Button from '@/ui/form/button';
import { FC, useEffect, useMemo, useState } from 'react';
import useCoachSlotsStore from '@/stores/learning/coaches/slots';
import ISlot from '@/types/domain/learning/coach/slot';
import SessionDate from './session-date';
import Item from '../item';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';

interface IAvailableSessions {
  coachId?: string;
  viewAllSessions?: () => void;
}

interface IDayWithSlots {
  date: string;
  slots: ISlot[];
}

const AvailableSessions: FC<IAvailableSessions> = ({
  viewAllSessions,
  coachId,
}) => {
  const t = useTranslations();
  const [selectedDate, setSelectedDate] = useState(0);

  // Getting list of available slots
  const { list, isFetching, fetch: fetchSlots } = useCoachSlotsStore();

  // Effect to fetch the available slots
  useEffect(() => {
    // If coach id is available
    if (coachId) {
      // Claering the error
      // setError(null);
      // Fetching the booking slots
      fetchSlots(coachId);
    }
  }, [coachId, fetchSlots]);

  const daysWithSlots = useMemo(() => {
    if (list.length === 0) return [];

    const uniqueDates: string[] = [];

    list.forEach((slot) => {
      if (slot.slot_date && !uniqueDates.includes(slot.slot_date))
        uniqueDates.push(slot.slot_date);
    });

    const _daysWithSlots: IDayWithSlots[] = [];

    uniqueDates.forEach((date) => {
      const newObject = {
        date: date,
        slots: new Array<ISlot>(),
      };

      const filtered = list.filter((slot) => slot.slot_date === date);
      const sorted = filtered.sort((a, b) => {
        const aTime = new Date(a?.time_from || '').getTime();
        const bTime = new Date(b?.time_from || '').getTime();
        return aTime - bTime;
      });
      newObject.slots = sorted;
      _daysWithSlots.push(newObject);
    });

    return _daysWithSlots;
  }, [list]);

  return (
    <div className={styles.availableSessions}>
      <div className={styles.header}>
        <div className={styles.title}>
          <Typography>{t(dictionary.sessionsAvailable)}</Typography>
          <button
            type="button"
            className={styles.viewAll}
            onClick={viewAllSessions}
          >
            <span className="resizable">{t(dictionary.viewAll)}</span>
          </button>
        </div>
        <Typography className={styles.subTitle}>
          Book 1:1 sessions from the options based on your needs
        </Typography>
      </div>
      <WithLoader loader={<Loading />} loading={isFetching}>
        <div className={styles.sessionDates}>
          {daysWithSlots.map((day, index) => {
            return (
              <SessionDate
                key={day.date}
                dayWithSlots={day}
                selectDate={setSelectedDate}
                sessionDayIndex={index}
                selectedIndex={selectedDate}
              />
            );
          })}
        </div>
        <div className={styles.timeSlots}>
          <Typography className={styles.timeSlotsTitle}>
            {t(dictionary.availableTimeSlot)}
          </Typography>
          <div className={styles.slotList}>
            {list.length !== 0 &&
              daysWithSlots[selectedDate].slots?.map((sessionSlot, index) => {
                return (
                  <Item
                    key={sessionSlot.slot_id}
                    slot={sessionSlot}
                    title={t(dictionary.Session) + ` ${index + 1}`}
                  />
                );
              })}
          </div>
        </div>
      </WithLoader>
      <Button
        type="button"
        color="black"
        onClick={() => {}}
        // disabled={!slots}
        className={styles.bookCta}
      >
        {t(dictionary.bookASession)}
      </Button>
    </div>
  );
};

export default AvailableSessions;
