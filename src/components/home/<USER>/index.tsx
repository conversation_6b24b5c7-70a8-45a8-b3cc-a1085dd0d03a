import Image from 'next/image';
import challenge_banner from '@public/images/challenge-banner.png';
import challenge_banner_ar from '@public/images/challenge-banner-ar.png';

// Styles
import styles from './style.module.scss';
import { useLocale } from 'next-intl';

import CONFIG from '@/config';
import { Link } from '@/i18n/routing';
import { sendGAEvent } from '@next/third-parties/google';

const ChallengeBanner = () => {
  const locale = useLocale();

  const handleClick = () => {
    sendGAEvent('event', CONFIG.analytics.challenge_banner_clicked, {
      value: 'Challenge Banner',
    });
  };

  return (
    <div className={styles.flexBox}>
      <Link
        href={CONFIG.routes.discover.page}
        aria-label="Discover landing page"
        onClick={handleClick}
      >
        <div className={styles.img}>
          <Image
            src={locale !== 'ar' ? challenge_banner : challenge_banner_ar}
            alt="Challenge Banner"
          />
        </div>
      </Link>
    </div>
  );
};

export default ChallengeBanner;
