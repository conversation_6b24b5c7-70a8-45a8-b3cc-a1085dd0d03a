import Image from 'next/image';
import Typography from '@/ui/typography';
import Button from '@/ui/form/button';
import {
  Close,
  EditIcon,
  ImageIcon,
  PopperIcon,
  QuestionIcon,
} from '@/ui/icons';

// styles
import styles from './style.module.scss';
import BackOverly from '@/ui/back-overly';
import { PostTypeEnum } from '../../discussion';
import { ChangeEvent, FC, useState } from 'react';
import useCommunityDetailsPostsStore from '@/stores/my-network/community-details/posts';
import { useParams } from 'next/navigation';
import { ICommunityDiscussion } from '@/types/domain/my-network/communityDetails';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';

interface IShareYourThoughtsPopup {
  title?: string;
  imageUrl?: string;
  postType: PostTypeEnum;
  handleCloseDialog: () => void;
  details?: ICommunityDiscussion;
}

const ShareYourThoughtsPopup: FC<IShareYourThoughtsPopup> = ({
  title,
  imageUrl,
  postType,
  handleCloseDialog,
  details,
}) => {
  const postTypeMap = {
    [PostTypeEnum.discussion]: {
      label: dictionary.discussion,
      icon: <EditIcon />,
      style: '',
      placeholder: dictionary.shareThoughtsIdeasOrUpdates,
    },
    [PostTypeEnum.question]: {
      label: dictionary.question,
      icon: <QuestionIcon />,
      style: styles.question,
      placeholder: dictionary.askAQuestion,
    },
    [PostTypeEnum.celebration]: {
      label: dictionary.celebrate,
      icon: <PopperIcon />,
      style: styles.popper,
      placeholder: dictionary.whatDoYouWantToCelebrate,
    },
  };
  // Getting translations
  const t = useTranslations();
  const communityId = useParams().slug as string;
  const { createPost, updatePost } = useCommunityDetailsPostsStore();
  const [postText, setPostText] = useState<string>(
    details && details.content ? details.content : '',
  );
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [image, setImage] = useState<string>(
    details && details?.images.length > 0 ? details?.images[0].url : '',
  );
  const [errorMessage, setErrorMessage] = useState<string | undefined>('');

  const handleSetPostText = (event: ChangeEvent<HTMLTextAreaElement>) => {
    setPostText(event.target.value);
  };

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    const input = event.target;
    const files = input.files;
    if (files?.length) {
      if (files[0].size >= 2097152) {
        setErrorMessage('File size exceeds the limit!');
        input.value = '';
        return;
      }
      setErrorMessage(undefined);
      const imageUrl = URL.createObjectURL(files[0]);
      setImage(imageUrl);
      setSelectedFile(files[0]);
      input.value = '';
    } else {
      setImage('');
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setImage('');
  };

  let formData: FormData | undefined = undefined;
  const handleSubmit = async () => {
    if (selectedFile) {
      formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('folder', '');
    }
    if (details && postText) {
      updatePost(postType, details.id, postText, formData);
    } else {
      createPost(Number(communityId), postType, postText, formData);
    }
  };

  return (
    <>
      <BackOverly />
      <div className={styles.popup}>
        <div className={styles.header}>
          <div className={styles.title}>
            {imageUrl && (
              <div className={styles.photo}>
                <Image
                  src={imageUrl as string}
                  alt=""
                  width={0}
                  height={0}
                  sizes="100vw"
                />
              </div>
            )}
            {title && <Typography as="h6">{title}</Typography>}
            <button
              type="button"
              className={styles.close}
              onClick={handleCloseDialog}
            >
              <i>
                <Close />
              </i>
            </button>
          </div>
          <div className={styles.postType}>
            <div className={styles.type}>
              <div
                className={styles.avatar + ' ' + postTypeMap[postType].style}
              >
                <i>{postTypeMap[postType].icon}</i>
              </div>
              <Typography as="span" dictionary={postTypeMap[postType].label} />
            </div>
          </div>
        </div>
        <div className={styles.body}>
          <textarea
            className={styles.textarea}
            placeholder={t(postTypeMap[postType].placeholder)}
            onChange={handleSetPostText}
            value={postText}
          />
          {image && (
            <div className={styles.media}>
              <Image
                src={image}
                alt=""
                style={{ width: '100%', objectFit: 'cover' }}
                width={150}
                height={150}
              />
            </div>
          )}
          {errorMessage && (
            <Typography as="p" className={styles.error}>
              {errorMessage}
            </Typography>
          )}
        </div>
        <div className={styles.footer}>
          <div className={styles.addPhoto}>
            {image ? (
              <>
                <button type="button" onClick={handleRemoveFile}>
                  <i>
                    <ImageIcon />
                  </i>
                  {t(dictionary.removePhoto)}
                </button>
              </>
            ) : (
              <>
                <input
                  type="file"
                  id="addPhoto"
                  accept="image/*"
                  hidden
                  onChange={handleFileChange}
                />
                <label htmlFor="addPhoto">
                  <i>
                    <ImageIcon />
                  </i>
                  {t(dictionary.addPhoto)}
                </label>
              </>
            )}
          </div>
          <div className={styles.buttonWrapper}>
            <Button
              type="button"
              color="black"
              className="dialog-form-action"
              disabled={postText?.length === 0 && !selectedFile}
              onClick={handleSubmit}
              dictionary={dictionary.post}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default ShareYourThoughtsPopup;
