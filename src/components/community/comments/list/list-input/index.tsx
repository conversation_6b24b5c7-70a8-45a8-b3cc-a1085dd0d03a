import styles from '.././style.module.scss';
import { ChangeEvent, useState } from 'react';
import Typography from '@/ui/typography';
import { Close, ImageIcon } from '@/ui/icons';
import PaperAirplane from '@/ui/icons/paper-airplane';
import Image from 'next/image';
import { getFullNameInitials } from '@/utils/helpers';
import useAuthStore from '@/stores/auth';

interface IListInput {
  onClickSend: (commentText: string, formData: FormData | undefined) => void;
  text?: string;
}

const ListInput: React.FC<IListInput> = ({ onClickSend, text }) => {
  const { user } = useAuthStore();
  const [commentText, setCommentText] = useState<string>(text || '');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [image, setImage] = useState<string>('');
  const fullNameInitials = getFullNameInitials(user?.fullname || '');
  const handleSetCommentText = (event: ChangeEvent<HTMLInputElement>) => {
    setCommentText(event.target.value);
  };

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    const input = event.target;
    const files = input.files;

    if (files?.length) {
      const imageUrl = URL.createObjectURL(files[0]);
      setImage(imageUrl);
      setSelectedFile(files[0]);
      input.value = '';
    } else {
      setImage('');
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setImage('');
  };

  const handleSendComment = () => {
    let formData: FormData | undefined = undefined;

    if (selectedFile) {
      formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('folder', '');
    }
    onClickSend(commentText, formData);
    setCommentText('');
    handleRemoveFile();
  };

  return (
    <>
      <div className={styles.containerInput}>
        <div className={styles.avatar}>
          <Typography>{fullNameInitials}</Typography>
        </div>
        <input
          type="text"
          className={styles.postInput}
          placeholder="Add a comment"
          value={commentText}
          onChange={handleSetCommentText}
        />
        <div className={styles.icon}>
          <input
            type="file"
            id="addPhotoComment"
            accept="image/*"
            hidden
            onChange={handleFileChange}
          />
          <label htmlFor="addPhotoComment">
            <i>
              <ImageIcon />
            </i>
          </label>
        </div>
        <div className={styles.iconAirplane}>
          <button
            type="button"
            onClick={handleSendComment}
            disabled={!selectedFile && !commentText}
          >
            <i>
              <PaperAirplane />
            </i>
          </button>
        </div>
      </div>
      {image && (
        <div className={styles.inputImageContainer}>
          <div className={styles.inputImage}>
            <Image src={image} alt="" width={150} height={150} />
          </div>
          <button
            type="button"
            className={styles.close}
            onClick={handleRemoveFile}
          >
            <i>
              <Close />
            </i>
          </button>
        </div>
      )}
    </>
  );
};
export default ListInput;
