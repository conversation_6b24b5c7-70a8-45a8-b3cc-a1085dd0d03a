import { fetchFaq } from '@/services/external/support-center';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const search = request.nextUrl.searchParams.get('search') || '';
  try {
    const response = await fetchFaq(search);
    return NextResponse.json({
      faqQuestions: response?.data,
    });
  } catch (error) {
    return ErrorResponse(error as AxiosError);
  }
}
