'use client';
import React, { FC, ReactNode, useMemo, useCallback } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import WithLoader from '@/ui/skeleton/with-loader';
import Paper from '@/ui/paper';
import Typography from '@/ui/typography';
import Loading from '@/components/common/loading';
import FilterButtons, {
  IFilterOption,
} from '@/components/common/filter-buttons';
import { motion } from 'motion/react';
import { useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';

// Styles
import styles from './style.module.scss';

interface ICardsSection {
  title?: string;
  description?: string;
  listingPageUrl?: string | null;
  items?: ReactNode[];
  limit?: number | null;
  forceViewAll?: boolean;
  className?: string;
  wrapperClass?: string;
  slidesConfig?: {
    desktop?: {
      view: number;
      gap: number;
    };
    tablet?: {
      view: number;
      gap: number;
    };
    mobile?: {
      view: number;
      gap: number;
    };
  };
  loading?: boolean | null;
  noDataMessage?: ReactNode;
  filters?: IFilterOption[];
  activeFilterOption?: string;
  onChangeFilter?: (id: string) => void;
  changeDisplayContext?: (event: React.MouseEvent<HTMLElement>) => void;
}

const CardsSection: FC<ICardsSection> = (props) => {
  // Deconstructing props
  const {
    title,
    description,
    listingPageUrl,
    items = [],
    limit = null,
    forceViewAll,
    className,
    wrapperClass,
    slidesConfig,
    loading,
    noDataMessage = 'No data found',
    filters,
    activeFilterOption,
    onChangeFilter,
    changeDisplayContext,
  } = props;

  const t = useTranslations();

  // Check if translation exists
  const hasTranslation = useCallback((key?: string) => {
    // if key is not provided
    if (!key) return false;
    // Check if key exists in dictionary
    return key in dictionary;
  }, []);

  // Translating title
  const _title = useMemo(() => {
    // If translation exists
    if (hasTranslation(title)) {
      return t(title);
    }
    // Return label
    return title;
  }, [title, hasTranslation, t]);

  // Handling limit based slides
  const slides = useMemo(() => {
    const _items = items || [];
    if (limit && limit > 0) {
      return _items?.slice(0, limit);
    }
    return _items;
  }, [items, limit]);

  // Handling listing title
  const listingTitle = useMemo(() => {
    // If limit is set and items length is greater than limit
    if (limit && limit > 0 && items?.length > limit) {
      return dictionary.viewAll;
    }
    // Return empty string
    return '';
  }, [items, limit]);

  // Return JSX
  return (
    <Paper
      title={_title}
      titleVariant="large"
      linkTitle={!forceViewAll ? listingTitle : dictionary.viewAll}
      link={listingPageUrl || ''}
      changeDisplayContext={changeDisplayContext}
      className={wrapperClass}
    >
      {description && (
        <div className={styles.content}>
          <Typography as="p">{description}</Typography>
        </div>
      )}
      <FilterButtons
        filterOptions={filters}
        activeOption={activeFilterOption}
        onChange={onChangeFilter}
        isScrollable={false}
      />
      <WithLoader loader={<Loading />} loading={loading}>
        <motion.div
          initial={{
            opacity: 0,
            position: 'relative',
          }}
          animate={{ opacity: 1 }}
          transition={{
            ease: 'easeInOut',
            duration: 0.5,
          }}
        >
          {slides?.length === 0 && !loading ? (
            <>{noDataMessage}</>
          ) : (
            <Swiper
              spaceBetween={slidesConfig?.mobile?.gap || 8}
              slidesPerView={slidesConfig?.mobile?.view || 1}
              navigation={true}
              modules={[Navigation]}
              breakpoints={{
                1024: {
                  slidesPerView: slidesConfig?.desktop?.view || 4,
                  spaceBetween: slidesConfig?.desktop?.gap || 18,
                },
                768: {
                  slidesPerView: slidesConfig?.tablet?.view || 2,
                  spaceBetween: slidesConfig?.tablet?.gap || 16,
                },
              }}
              className={className}
            >
              {slides?.map((item, index) => (
                <SwiperSlide className={styles.slide} key={index}>
                  {item}
                </SwiperSlide>
              ))}
            </Swiper>
          )}
        </motion.div>
      </WithLoader>
    </Paper>
  );
};

export default CardsSection;
