import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchCourseDetailService } from '@/services/totara/courses';

export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const courseId = request.nextUrl.searchParams.get('courseId') || '';

    // Fetching records counts
    const response = await TotaraFetchCourseDetailService(courseId);

    // Return the response
    return NextResponse.json(response?.data);
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
