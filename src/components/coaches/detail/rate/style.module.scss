@use 'mixins' as *;

.body {
  width: 100%;
  @include forMarginPadding(padding, size(0), size(24), size(24), size(24));
  overflow-y: auto;
  height: calc(100vh - size(192));

  @media (min-width: 576px) {
    &::-webkit-scrollbar-track {
      background-color: var(--white);
    }

    &::-webkit-scrollbar {
      width: size(8);
      background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: size(8);
      background-color: var(--grey-900);
    }
  }
}

.footer {
  display: flex;
  gap: size(16);
  width: 100%;
  z-index: 9;
  background: var(--white);
  @include forMarginPadding(padding, size(24), size(24), size(24), size(24));
}

.rating {
  ul {
    @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    display: flex;

    li {
      @include forMarginPadding(margin, size(0), size(10), size(0), size(0));
      list-style: none;
      width: size(30);
      height: size(30);

      button {
        @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
        @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
        background: none;
        border: none;
        cursor: pointer;
        @include transitions(0.5s);
        width: 100%;
        height: 100%;
        display: block;

        &:disabled {
          cursor: auto;
        }

        i {
          position: relative;
          display: block;
          width: 100%;
          height: 100%;

          img {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            @include leftToRight(0);
            @include transitions(0.5s);

            &.filled {
              opacity: 0;
              visibility: hidden;
            }
          }
        }

        &.active {
          img {
            &.outline {
              opacity: 0;
              visibility: hidden;
            }

            &.filled {
              opacity: 1;
              visibility: visible;
            }
          }
        }
      }
    }
  }
}

.textWrapper {
  margin-bottom: size(24);
}

.subtitle {
  border-bottom: 1px solid var(--grey-400);
  padding: size(24) size(0);
  font-size: size(24);
}

.description {
  font-size: size(20);
}

.backDetail {
  display: flex;
  align-items: center;
  font-size: size(40);
  line-height: 1;
  min-height: size(72);
  padding: size(24);
  background: none;
  border: none;
  cursor: pointer;

  i {
    width: size(26);
    height: size(26);
    @include forMarginPadding(margin, size(0), size(10), size(0), size(0));

    svg {
      vertical-align: top;
    }
  }
}

@include for-dark-theme() {
}

@include for-all-phone() {
}
