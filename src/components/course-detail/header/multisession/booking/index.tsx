import { FC, useCallback, useEffect } from 'react';
import Button from '@/ui/form/button';
import styles from './style.module.scss';
import Slot from './slot';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';
import useMultisessionCourseStore from '@/stores/learning/courses/multisession';
import { AvailableSlotMultiday } from '@/types/domain/learning/course';
import useCourseDetailStore from '@/stores/learning/courses/detail';

interface IBookingCohort {
  list: AvailableSlotMultiday[];
  closeDrawer: () => void;
  managerApprovalRequired?: boolean;
}

// React component to handle coach booking
const BookingCohort: FC<IBookingCohort> = (props) => {
  // Deconstructing props
  const { list, closeDrawer, managerApprovalRequired } = props;

  const courseDetail = useCourseDetailStore((state) => state.detail);

  const checkLineManager = useMultisessionCourseStore(
    (x) => x.checkLineManager,
  );
  const lineManagerCheck = useMultisessionCourseStore(
    (x) => x.lineManagerCheck,
  );
  const lineManagerCheckLoading = useMultisessionCourseStore(
    (x) => x.isFetching,
  );
  const selected = useMultisessionCourseStore((x) => x.selected);
  const bookASlot = useMultisessionCourseStore((x) => x.bookASlot);
  const isBooking = useMultisessionCourseStore((x) => x.isBooking);
  const setPopupMessage = useMultisessionCourseStore((x) => x.setPopupMessage);

  const t = useTranslations();

  const handleBooking = useCallback(async () => {
    if (managerApprovalRequired && !lineManagerCheck?.success) {
      closeDrawer();
      setPopupMessage({
        title: t(dictionary.lineManagerNotAssigned),
        message: t(dictionary.contactAdminTeam),
      });
      return;
    }
    const response = await bookASlot(courseDetail?.courseId as number);
    if (response.success) {
      closeDrawer();
      if (managerApprovalRequired) {
        setPopupMessage({
          refreshData: true,
          title: t(dictionary.yourRequestToEnrollInThisCourseHasBeenReceived),
          message: t(dictionary.onceYourRequestIsApprovedWeWillNotify),
          slot: selected!,
        });
      } else {
        setPopupMessage({
          success: true,
          title: t(dictionary.yourRequestToEnrollInThisCourseHasBeenReceived),
          message: t(dictionary.onceYourRequestIsApprovedWeWillNotify),
          slot: selected!,
          refreshData: true,
        });
      }
    }
  }, [
    closeDrawer,
    lineManagerCheck?.success,
    setPopupMessage,
    managerApprovalRequired,
    courseDetail?.courseId,
    bookASlot,
    selected,
    t,
  ]);

  useEffect(() => {
    checkLineManager();
  }, [checkLineManager]);

  // Return JSX
  return (
    <>
      <div className={styles.sessionlist}>
        <WithLoader loader={<Loading />} loading={lineManagerCheckLoading}>
          <ul>
            {list.map((session, i) => (
              <Slot key={i} session={session} />
            ))}
          </ul>
        </WithLoader>
      </div>
      <div className={styles.footer}>
        <Button
          type="button"
          color="black"
          disabled={!selected?.id}
          loading={isBooking}
          onClick={handleBooking}
          className={styles.innerBookCta}
        >
          {managerApprovalRequired
            ? t(dictionary.requestACohort)
            : t(dictionary.confirmACohort)}
        </Button>
      </div>
    </>
  );
};

export default BookingCohort;
