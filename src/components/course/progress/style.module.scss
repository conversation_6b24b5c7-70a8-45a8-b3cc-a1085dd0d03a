@use 'mixins' as *;

.progressBar {
  align-items: center;
  display: flex;
  width: 70%;
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));

  span {
    font-size: size(12);
    font-weight: 700;
    @include forMarginPadding(margin, size(0), size(0), size(0), size(8));
  }

  div[class*='progress'] {
    height: size(5);
    width: size(82);

    @include for-all-phone() {
      width: 100%;
    }

    div[class*='bar'] {
      border-radius: size(30) 0 0 size(30);
    }
  }
}

@include for-dark-theme() {
  .progressBar {
    div[class*='progress'] {
      background: var(--grey-500);

      div[class*='bar'] {
      }
    }
  }
}
