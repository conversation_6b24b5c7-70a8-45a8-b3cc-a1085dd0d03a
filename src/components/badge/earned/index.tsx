import { FC } from 'react';
import Typography from '@/ui/typography';
import BackOverly from '@/ui/back-overly';
import IBadge from '@/types/domain/learning/badge';

import Image from '@/ui/image';
import { Close } from '@/ui/icons';
import fallback from '@public/images/pro-perfomer.png';

// Styles
import styles from './style.module.scss';
import Button from '@/ui/form/button';
import dictionary from '@/dictionaries';

interface IDetail {
  badge: IBadge;
  onClose: () => void;
}

const Earned: FC<IDetail> = (props) => {
  // Destructuring props
  const { badge, onClose } = props;

  // Return JSX
  return (
    <div className={styles.popup}>
      <BackOverly onClick={onClose} />
      <div className={styles.inner}>
        <button type="button" className={styles.closeBtn} onClick={onClose}>
          <i className="fa-icon">
            <Close />
          </i>
        </button>
        <div className={styles.content}>
          <div className={styles.preheader}>
            <Typography
              as="h4"
              style={{ fontWeight: 'normal' }}
              dictionary={dictionary.Congratulations}
            />
          </div>
          <div className={styles.header}>
            <Typography
              as="h3"
              dictionary={dictionary.youHaveEarnedANewBadge}
            />
          </div>
          <div className={styles.subheader}>
            <Typography
              dictionary={dictionary.CongratulationsOnEarningANewBadge}
            />
          </div>
          <div className={styles.badge}>
            <Image
              width={100}
              height={100}
              fallback={fallback}
              src={badge?.image_url}
              alt={badge?.name}
            />
          </div>
          <div className={styles.badgeTitle}>
            <Typography as="h5">{badge?.name}</Typography>
          </div>
          <Button className={styles.btn} onClick={onClose}>
            <Typography
              style={{ margin: 0 }}
              dictionary={dictionary.viewEarnedBadges}
            />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Earned;
