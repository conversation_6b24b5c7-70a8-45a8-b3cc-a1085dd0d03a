import {
  useState,
  useCallback,
  useRef,
  useEffect,
  useLayoutEffect,
} from 'react';
import { useTranslations } from 'next-intl';
import useCreatorFilters from '@/hooks/helpers/useCreatorFilters';
import CuratorsSelectionDialog from './curators-selection-dialog';
import ChipButton from '@/ui/form/chip-button';
import Typography from '@/ui/typography';
import styles from './style.module.scss';
import dictionary from '@/dictionaries';
import { RightArrow } from '@/ui/icons';
import Loading from '@/components/common/loading';

const BROWSER_CONTEXT = 16;

const size = (pixels: number, context: number = BROWSER_CONTEXT): string => {
  return `${pixels / context}rem`;
};

const measurementContainerBaseStyle: React.CSSProperties = {
  position: 'absolute',
  visibility: 'hidden',
  top: -9999,
  left: -9999,
  zIndex: -1,
  display: 'flex',
  flexWrap: 'wrap',
  gap: `${size(8)}`,
};

const Curators = () => {
  const t = useTranslations();
  const containerRef = useRef<HTMLUListElement>(null);
  const [isAccordionOpen, setIsAccordionOpen] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [itemsForExpandedView, setItemsForExpandedView] = useState<string[]>(
    [],
  );
  const [viewMode, setViewMode] = useState<'selectedOnly' | 'all'>('all');

  const { isFetching, allCreators, selectedCreators, setCreatorsFilter } =
    useCreatorFilters();

  const [visibleItems, setVisibleItems] = useState<string[]>([]);
  const [hiddenSelectedCount, setHiddenSelectedCount] = useState(0);
  const itemWidths = useRef<Map<string, number>>(new Map());
  const [containerWidth, setContainerWidth] = useState(0);

  // Set initial view mode based on whether items are selected on load
  useEffect(() => {
    if (selectedCreators.length > 0) {
      setViewMode('selectedOnly');
      setIsAccordionOpen(true);
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useLayoutEffect(() => {
    const observer = new ResizeObserver((entries) => {
      const entry = entries[0];
      if (entry?.contentRect.width) {
        setContainerWidth(entry.contentRect.width);
      }
    });

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [isAccordionOpen]);

  useLayoutEffect(() => {
    if (isExpanded) {
      setVisibleItems(itemsForExpandedView);
      setHiddenSelectedCount(0);
      return;
    }

    if (!containerWidth || itemWidths.current.size < allCreators.length) return;

    const gap = 8;
    const plusChipWidth = 60;

    // Determine which list of creators to process based on the view mode
    const listToProcess =
      viewMode === 'selectedOnly' ? selectedCreators : allCreators;

    let finalVisible: string[] = [];
    let currentRowWidth = 0;
    let rowCount = 1;

    // Phase 1 -> Fill the two rows with as many items as possible
    for (const creator of listToProcess) {
      const itemWidth = itemWidths.current.get(creator) || 0;

      if (currentRowWidth + itemWidth + gap <= containerWidth) {
        currentRowWidth += itemWidth + gap;
        finalVisible.push(creator);
      } else if (rowCount < 2) {
        rowCount++;
        currentRowWidth = itemWidth + gap;
        finalVisible.push(creator);
      } else {
        break;
      }
    }

    const initialVisibleSet = new Set(finalVisible);
    const hiddenSelectedItems = selectedCreators.filter(
      (c) => !initialVisibleSet.has(c),
    );

    // Phase 2 -> If items are hidden, adjust the visible list to make space for the "+N" el
    if (hiddenSelectedItems.length > 0) {
      //  Iterate backwards from the current visible list. The first list that can 'fit in' "+N" el
      for (let i = finalVisible.length; i >= 0; i--) {
        const currentVisible = finalVisible.slice(0, i);

        // Calculate
        let lastRowW = 0;
        let tempRowCount = 1;
        for (const item of currentVisible) {
          const w = itemWidths.current.get(item) || 0;
          if (lastRowW + w + gap <= containerWidth) {
            lastRowW += w + gap;
          } else {
            tempRowCount++;
            lastRowW = w + gap;
          }
        }

        // Check if the +N el fits with this list
        if (
          tempRowCount <= 2 &&
          lastRowW + gap + plusChipWidth <= containerWidth
        ) {
          // It fits!
          finalVisible = currentVisible;
          break;
        }
      }
    }

    // Phase 3 -> recalculate the final hidden count based on the adjusted visible list
    const finalVisibleSet = new Set(finalVisible);
    const finalHiddenCount = selectedCreators.filter(
      (c) => !finalVisibleSet.has(c),
    ).length;

    setVisibleItems(finalVisible);
    setHiddenSelectedCount(finalHiddenCount);
  }, [
    containerWidth,
    allCreators,
    selectedCreators,
    isExpanded,
    itemsForExpandedView,
    viewMode,
  ]);

  const handleAccordionToggle = () => {
    setIsAccordionOpen((prev) => !prev);
  };

  const handleChipClick = useCallback(
    (creator: string) => {
      const isSelectingNewItem = !selectedCreators.includes(creator);

      const newSelection = isSelectingNewItem
        ? [...selectedCreators, creator]
        : selectedCreators.filter((c) => c !== creator);

      if (isSelectingNewItem) {
        setViewMode('all');
      }

      if (newSelection.length === 0) {
        setViewMode('all');
      }

      setCreatorsFilter(newSelection);
    },
    [selectedCreators, setCreatorsFilter],
  );

  const handleApplyModalSelection = useCallback(
    (newSelection: string[]) => {
      setViewMode('selectedOnly');
      setCreatorsFilter(newSelection);
      setIsModalOpen(false);
      setIsExpanded(false);
    },
    [setCreatorsFilter],
  );

  const handleExpandClick = useCallback(() => {
    setItemsForExpandedView(selectedCreators);
    setIsExpanded(true);
  }, [selectedCreators]);

  const showMoreButtonIsVisible = allCreators.length > visibleItems.length;

  return (
    <nav className={styles.filter}>
      <div className={styles.filterHeader}>
        <Typography as="h4" dictionary={dictionary.Curators} />
        <div
          className={`${styles.accordionAction} ${isAccordionOpen ? styles.openState : styles.closeState}`}
          onClick={handleAccordionToggle}
        >
          <RightArrow />
        </div>
      </div>

      {isFetching && (
        <div className={styles.loadingContainer}>
          <Loading />
        </div>
      )}

      {isAccordionOpen && (
        <div className={styles.filterContent}>
          <ul className={styles.listContainer} ref={containerRef}>
            <div style={measurementContainerBaseStyle}>
              {/* Measure all items to calculate widths */}
              {!isFetching &&
                allCreators.map((creator) => (
                  <li
                    key={'measure-' + creator}
                    ref={(el) => {
                      if (el) itemWidths.current.set(creator, el.offsetWidth);
                    }}
                  >
                    <ChipButton variant="rounded" active={false}>
                      {creator}
                    </ChipButton>
                  </li>
                ))}
            </div>

            {!isFetching &&
              visibleItems.map((creator) => (
                <li key={`creator-${creator}`}>
                  <ChipButton
                    active={selectedCreators.includes(creator)}
                    variant="rounded"
                    onClick={() => handleChipClick(creator)}
                  >
                    {creator}
                  </ChipButton>
                </li>
              ))}

            {hiddenSelectedCount > 0 && !isExpanded && (
              <li>
                <ChipButton
                  active
                  variant="rounded"
                  onClick={handleExpandClick}
                >
                  +{hiddenSelectedCount}
                </ChipButton>
              </li>
            )}
          </ul>

          {!isFetching && showMoreButtonIsVisible && (
            <button
              onClick={() => setIsModalOpen(true)}
              className={styles.showMoreButton}
            >
              {t(dictionary.showMore)}
            </button>
          )}
        </div>
      )}

      <CuratorsSelectionDialog
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        allCreators={allCreators}
        initialSelection={selectedCreators}
        onApply={handleApplyModalSelection}
      />
    </nav>
  );
};

export default Curators;
