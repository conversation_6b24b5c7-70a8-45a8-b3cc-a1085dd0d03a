import type { ICache } from './types'

export async function with<PERSON>ache<T>(
  cache: ICache,
  opts: { namespace: string; ttlMs?: number; keyParts: Record<string, string | number | undefined> },
  producer: () => Promise<T>,
): Promise<T> {
  const key = `${opts.namespace}:${Object.entries(opts.keyParts)
    .filter(([, v]) => v !== undefined && v !== null)
    .map(([k, v]) => `${k}=${String(v)}`)
    .sort()
    .join('|') || '-'}`
  const hit = cache.get<T>(key)
  if (hit !== null) return hit
  const data = await producer()
  cache.set<T>(key, data, { ttlMs: opts.ttlMs })
  return data
}

