import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchBookedCoachSessionsService } from '@/services/totara/coaches';

/**
 * Route to get the coaches
 */
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const page = request.nextUrl.searchParams.get('page') || '1';
    const limit = request.nextUrl.searchParams.get('limit') || '10';
    // Fetching booked sessions
    const response = await TotaraFetchBookedCoachSessionsService({
      page: page,
      limit: limit,
    });

    if (response?.data?.exception) {
      return NextResponse.json({
        error: response.data,
      });
    }
    // Return the response
    return NextResponse.json({
      total: response?.data?.total_records || 0,
      list: response?.data,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
