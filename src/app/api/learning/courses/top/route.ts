import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchTopCoursesService } from '@/services/totara/courses';

/**
 * Route to get the courses
 */
export async function GET() {
  try {
    // Fetching records counts
    const response = await TotaraFetchTopCoursesService();

    // Return the response
    return NextResponse.json({
      list: response?.data?.records || [],
      total: response?.data?.records?.length || 0,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
