@use 'mixins' as *;

.addToListModalCard {
  height: size(120);
  display: flex;
  align-items: center;
  gap: size(16);
  @include forMarginPadding(padding, size(12), size(0), size(12), size(0));
  border-top: 1px solid var(--grey-400);
  max-width: 100%;

  .addToListModalCardImage {
    width: size(74);
    height: size(74);
    @include borderRadius(var(--radius));
    position: relative;
    overflow: hidden;

    .image {
      width: size(74);
      height: size(74);
      background-repeat: no-repeat;
      background-position: center center;
      background-size: cover;
      position: relative;
      transform: scale(1, 1);
      transition: 0.4s all cubic-bezier(0.77, 0.23, 0.23, 0.81);
    }

    @include rtl {
      @include forMarginPadding(margin, size(0), size(0), size(0), size(16));
    }
  }
  .addToListModalCardDetails {
    max-width: calc(100% - size(140));
    h4.listName {
      word-wrap: break-word;
      overflow-wrap: break-word;
      hyphens: auto;
      font-size: size(18);
      color: var(--grey-900);
      @include forMarginPadding(margin, size(0), size(0), size(12), size(0));
      line-height: 1;
    }

    span.totalCourses {
      font-size: size(14);
      color: var(--grey-800);
      display: flex;
      flex-direction: row;
      align-items: center;

      svg {
        width: size(16);
        height: size(16);
        fill: var(--grey-800);
        margin-right: size(3);
      }
    }
  }

  .addToListModalCardButtonContainer {
    margin-left: auto;
    display: flex;
    align-items: center;
    .addToListModalCardButton {
      display: flex;
      align-items: center;
      justify-content: center;
      justify-self: flex-end;
      background-color: transparent;
      border: 1px solid var(--grey-900);
      border-radius: 50%;
      cursor: pointer;
      height: size(24);
      width: size(24);
      min-height: size(24);
      min-width: size(24);

      svg {
        fill: var(--grey-900);
        height: size(10);
        width: size(10);
      }
    }

    .addToListModalCardButtonSuccess {
      border: none;
      background-color: var(--success-900);
    }
  }
}
