import { FC, useCallback, useState } from 'react';
import styles from './style.module.scss';
import { CalendarRounded, Circle, Clock, Pin } from '@/ui/icons';
import Typography from '@/ui/typography';
import { formatLocalTime } from '@/utils/time';
import dictionary from '@/dictionaries';
import moment from 'moment';
import 'moment/locale/ar';
import { format } from '@/utils/date';
import { useLocale } from 'next-intl';
import KababMenu from '@/ui/menu/kabab';
// import Edit from '@/ui/icons/edit';
import usePhysicalCourseStore from '@/stores/learning/courses/physical';
import useCourseDetailStore from '@/stores/learning/courses/detail';
import CancelConfirmPopup from '../cancel-confirm-popup';
import { Location } from '@/types/domain/learning/course';

interface ITimeSlot {
  date?: string;
  startDate?: string;
  endDate?: string;
  assignedByAdmin?: boolean;
  isCompleted?: boolean;
  location?: Location;
  pendingSlotId?: string;
  isAssignedSlot?: boolean;
}

const TimeSlot: FC<ITimeSlot> = ({
  date,
  startDate,
  endDate,
  location,
  pendingSlotId,
  isAssignedSlot,
}) => {
  const [showCancelPopup, setShowCancelPopup] = useState(false);
  const locale = useLocale();

  const localStartDate = formatLocalTime(startDate);
  const localEndDate = formatLocalTime(endDate);

  const formattedDate =
    date && moment(date, 'YYYY-MM-DD').isValid()
      ? format(date as string, locale, 'YYYY-MM-DD', 'dddd DD MMM YYYY')
      : 'DD MMM YYYY';

  const cancelBookedSlot = usePhysicalCourseStore((x) => x.cancelBookedSlot);
  const courseDetail = useCourseDetailStore((x) => x.detail);
  const fetchCourseDetail = useCourseDetailStore((x) => x.fetch);

  const handleCancel = useCallback(async () => {
    const res = await cancelBookedSlot(
      courseDetail?.courseId as number,
      pendingSlotId
        ? (pendingSlotId as unknown as number)
        : (courseDetail?.assigned_slot?.id as number),
    );
    if (res.success) {
      setShowCancelPopup(false);
      fetchCourseDetail(courseDetail?.courseId as unknown as string);
    } else {
      setShowCancelPopup(false);
    }
  }, [
    cancelBookedSlot,
    courseDetail?.assigned_slot?.id,
    courseDetail?.courseId,
    fetchCourseDetail,
    pendingSlotId,
  ]);

  return (
    <div className={styles.timeSlotWrapper}>
      <div className={styles.slotHeader}>
        <span className={styles.iconWrapper}>
          <CalendarRounded />
        </span>
        <Typography dictionary={dictionary.slotDetails} />
      </div>
      <div className={styles.slotHeader}>
        <span className={styles.dateIconWrapper}>
          <CalendarRounded />
        </span>
        <span className={styles.dateText}>{formattedDate}</span>
      </div>
      <div className={styles.slotHeader}>
        <span className={styles.dateIconWrapper}>
          <Clock />
        </span>
        <span className={styles.dateText}>
          {localStartDate
            .locale(locale === 'ar' ? 'ar' : 'en')
            .format('h:mm a')}{' '}
          -{' '}
          {localEndDate.locale(locale === 'ar' ? 'ar' : 'en').format('h:mm a')}
        </span>
      </div>
      {location && !Array.isArray(location) && location.room && (
        <div className={styles.slotHeader}>
          <span className={styles.dateIconWrapper}>
            <Pin />
          </span>
          <span className={styles.dateText}>{location.room}</span>
        </div>
      )}
      {(pendingSlotId || isAssignedSlot) && (
        <div className={styles.menuWrapper}>
          <KababMenu>
            <nav>
              <ul className={styles.menuList}>
                {/* <li>
                  <button type="button" className={styles.menuItem}>
                    <i>
                      <Edit />
                    </i>
                    <Typography 
                      as="span" 
                      // dictionary={dictionary.delete}
                    >
                      Change slot
                    </Typography>
                  </button>
                </li> */}
                <li>
                  <button
                    type="button"
                    className={styles.menuItem}
                    onClick={() => setShowCancelPopup(true)}
                  >
                    <i>
                      <Circle />
                    </i>
                    <Typography
                      as="span"
                      dictionary={
                        isAssignedSlot
                          ? dictionary.cancelSlot
                          : dictionary.cancelRequest
                      }
                    />
                  </button>
                </li>
              </ul>
            </nav>
          </KababMenu>
        </div>
      )}
      <CancelConfirmPopup
        isOpened={showCancelPopup}
        onAccept={handleCancel}
        onCancel={() => setShowCancelPopup(false)}
        isAssignedSlot={isAssignedSlot}
      />
    </div>
  );
};

export default TimeSlot;
