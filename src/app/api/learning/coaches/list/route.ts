import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchCoachesService } from '@/services/totara/coaches';

/**
 * Route to get the coaches
 */
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const search = request.nextUrl.searchParams.get('search') || '';
    const category = request.nextUrl.searchParams.get('category') || '';
    // Fetching coaches categories
    const response = await TotaraFetchCoachesService({
      search: search,
      category: category,
    });
    // Return the response
    return NextResponse.json({
      total: response?.data?.length || 0,
      list: response?.data,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
