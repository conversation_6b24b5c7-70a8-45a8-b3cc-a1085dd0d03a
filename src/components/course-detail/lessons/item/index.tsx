import { FC } from 'react';
import Link from 'next/link';
import Typography from '@/ui/typography';
import ExternalIcon from '@/ui/icons/external';
import Button from '@/ui/form/button';

import styles from '../style.module.scss';

interface IItem {
  title?: string;
  text?: string;
  cta?: {
    label: string;
    href: string;
  };
}

const Item: FC<IItem> = (props) => {
  const { title, cta } = props;

  return (
    <div className={styles.itemCard}>
      <div className={styles.itemData}>
        <Typography as="p" className={styles.title}>
          {title}
        </Typography>
      </div>

      {cta?.label && (
        <Link href={cta?.href} className={styles.externalLink}>
          <i>
            <ExternalIcon />
          </i>
        </Link>
      )}
      {cta?.href && (
        <Button href={cta.href} color="black">
          {cta?.label}
        </Button>
      )}
    </div>
  );
};

export default Item;
