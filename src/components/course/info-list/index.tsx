import cn from 'classnames';
import ICourse from '@/types/domain/learning/course';
import Duration from '../duration';
import Lessons from '../lessons';
import { FC } from 'react';
import Rating from '../rating';

//Styles
import styles from './style.module.scss';

interface IInfoList {
  course: ICourse;
  hasRating?: boolean;
  hasLesson?: boolean;
  hasDuration?: boolean;
  className?: string;
}

const InfoList: FC<IInfoList> = (props) => {
  // Deconstructing props
  const {
    course,
    hasRating = true,
    hasLesson = true,
    hasDuration = true,
    className,
  } = props;

  const classNames = cn({
    [styles.list]: true,
    [className || '']: className,
  });

  // Return JSX
  return (
    <nav className={classNames}>
      <ul>
        {hasDuration && (
          <li>
            <Duration course={course} />
          </li>
        )}
        {hasLesson && (
          <li>
            <Lessons course={course} />
          </li>
        )}
        {hasRating && course.rating !== 0 && (
          <li>
            <Rating course={course} />
          </li>
        )}
      </ul>
    </nav>
  );
};

export default InfoList;
