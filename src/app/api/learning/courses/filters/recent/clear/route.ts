import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraClearRecentSearchesService } from '@/services/totara/courses';

/**
 * Route to get the announcements
 */
export async function GET() {
  try {
    // Fetching records counts
    const response = await TotaraClearRecentSearchesService();

    // Return the response
    return NextResponse.json({
      success: response?.data?.success,
      message: response?.data?.message,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
