@use 'mixins' as *;

.topBarContainer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  .dateNavigation {
    display: flex;
    button {
      background-color: var(--cool-grey-700);
      border: none;
      border-radius: size(6);
      cursor: pointer;
    }

    .todayButton {
      padding: size(10.5) size(12);
      font-size: size(16);
      line-height: size(11);
      color: var(--grey-900);
      background: var(--grey-400);
    }

    .dateDisplay {
      color: var(--grey-900);
      font-size: size(20);
      font-weight: 700;
      line-height: size(14);
      margin: auto size(12) auto size(27);
    }

    .navButtons {
      display: flex;
      gap: size(12);

      .navButton {
        width: size(30);
        height: size(30);
        background: var(--grey-400);
        svg {
          width: size(30);
          height: size(30);
          path {
            fill: var(--grey-900);
          }
        }
      }

      .navButtonPrev svg {
        transform: rotate(90deg);
      }

      .navButtonNext svg {
        transform: rotate(-90deg);
      }
      .navButtonPrev.rtl svg {
        transform: rotate(-90deg);
      }

      .navButtonNext.rtl svg {
        transform: rotate(90deg);
      }
    }
  }

  .controlsSection {
    display: flex;
    gap: size(24);

    .viewSwitcher {
      display: flex;
      gap: size(12);

      .viewButton {
        background-color: transparent;
        border: none;
        outline: none;
        cursor: pointer;

        svg {
          width: size(24);
          height: size(24);
        }
      }
      .viewButtonList svg circle,
      .viewButtonList svg line {
        stroke: var(--grey-900);
        fill: var(--grey-900);
      }

      .viewButtonCalendar svg path {
        fill: var(--grey-900);
      }

      .viewButton.viewBtnActive {
        width: size(38);
        height: size(38);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background-color: var(--grey-900);
      }
      .viewBtnActive.viewButtonList svg circle,
      .viewBtnActive.viewButtonList svg line {
        stroke: var(--white);
        fill: var(--white);
      }

      .viewBtnActive.viewButtonCalendar svg path {
        fill: var(--white);
      }
    }
  }
}
@media screen and (max-width: 768px) {
  .topBarContainer {
    flex-wrap: wrap;
    gap: size(8);
    padding: 0 size(8);

    .dateNavigation {
      flex-wrap: wrap;
      align-items: center;
      gap: size(6);

      .todayButton {
        font-size: size(10);
        padding: size(6) size(10);
      }

      .dateDisplay {
        font-size: size(12);
        line-height: size(12);
        margin: 0 size(8);
        white-space: nowrap;
      }

      .navButtons {
        gap: size(4);

        .navButton {
          width: size(18);
          height: size(18);

          svg {
            width: size(14);
            height: size(14);
          }
        }
      }
    }

    .controlsSection {
      flex-wrap: wrap;
      align-items: center;
      gap: size(12);
      margin-left: auto;

      .viewSwitcher {
        gap: size(8);

        .viewButton {
          svg {
            width: size(18);
            height: size(18);
          }
        }

        .viewButton.viewBtnActive {
          width: size(30);
          height: size(30);
        }
      }
    }
  }
}
