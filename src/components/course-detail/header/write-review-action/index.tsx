import { FC, useCallback } from 'react';
import Button from '@/ui/form/button';
import dictionary from '@/dictionaries';

interface IWriteReviewAction {
  onReviewAction?: () => void;
}

const WriteReviewAction: FC<IWriteReviewAction> = ({ onReviewAction }) => {
  /**
   * Method to handle course start
   */
  const onWriteReview = useCallback(() => {
    if (onReviewAction) {
      onReviewAction();
    }
  }, [onReviewAction]);

  return (
    <Button
      color="black"
      type="button"
      onClick={onWriteReview}
      dictionary={dictionary.writeAReview}
    />
  );
};

export default WriteReviewAction;
