import WithLoader from '@/ui/skeleton/with-loader';
import styles from './style.module.scss';
import Loading from '@/components/common/loading';
import Typography from '@/ui/typography';
import dictionary from '@/dictionaries';

import { useState } from 'react';
import DialogDrawer from '@/ui/dialog-drawer';
import Button from '@/ui/form/button';
import GeneralInfo from './general-info';
import OrganizationInfo from './organization-info';
import ContactInfo from './contact-info';
import ReportInfo from './report-info';

const SidePanel = () => {
  const isFetching = false;
  const [openViewProfileDetails, setOpenViewProfileDetails] = useState(false);

  const closeProfileDetails = () => {
    setOpenViewProfileDetails(false);
  };

  const viewProfileDetails = () => {
    setOpenViewProfileDetails(true);
  };

  const onAddToContacts = () => {
    throw new Error('Function not implemented.');
  };

  return (
    <div className={styles.sideBar}>
      <WithLoader loader={<Loading />} loading={isFetching}>
        <Typography
          as="p"
          className={styles.title}
          dictionary={dictionary.yourExecutiveProfile}
        />
        <GeneralInfo />
        <OrganizationInfo />
        <div className={styles.viewProfile}>
          <button
            className={styles.viewProfileButton}
            onClick={viewProfileDetails}
          >
            <Typography dictionary={dictionary.viewProfileDetails} />
          </button>
        </div>
      </WithLoader>
      <DialogDrawer
        isOpened={openViewProfileDetails}
        onClose={closeProfileDetails}
      >
        <GeneralInfo />
        <ContactInfo />
        <ReportInfo />
        <Button
          type="button"
          onClick={onAddToContacts}
          color="black"
          className="dialog-form-action"
        >
          <Typography as="span" dictionary={dictionary.addToContacts} />
        </Button>
      </DialogDrawer>
    </div>
  );
};
export default SidePanel;
