import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchCoachesCategoriesService } from '@/services/totara/coaches';

/**
 * Route to get the coaches categories
 */
export async function GET() {
  try {
    // Fetching coaches categories
    const response = await TotaraFetchCoachesCategoriesService();
    // Split the response data
    const list = response?.data?.coachspecialization?.split(',') || [];
    // Return the response
    return NextResponse.json({
      total: list?.length || 0,
      list: list,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
