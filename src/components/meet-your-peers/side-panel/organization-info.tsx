import styles from './style.module.scss';
import Typography from '@/ui/typography';
import Organization from '@/ui/icons/organization';

const OrganizationInfo = () => {
  return (
    <div className={styles.profileGeneralInfoOrganization}>
      <Typography as="p" className={styles.profileOrganizationTitle}>
        <Organization />
        <span>Organization</span>
      </Typography>
      <Typography as="p" className={styles.profileOrganizationText}>
        <span className={styles.profileGrayText}>Reports to</span>
        <span>Mahsa <PERSON></span>
      </Typography>
      <Typography as="p" className={styles.profileOrganizationText}>
        <span className={styles.profileGrayText}>Team size</span>
        <span>12</span>
      </Typography>
    </div>
  );
};
export default OrganizationInfo;
