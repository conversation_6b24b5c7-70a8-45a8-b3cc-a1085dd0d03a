import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';
import { ExternalUpdateEndorsementService } from '@/services/external/profile/skills/update-endorsement';

/**
 * Route to get the learning types
 */
export async function POST(request: NextRequest) {
  try {
    // Getting request payload
    const data = await request.json();
    // Updating endorsement
    const response = await ExternalUpdateEndorsementService(data);

    return NextResponse.json({ endorsement: response.data });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error as AxiosError);
  }
}
