import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchCertificatesService } from '@/services/totara/certificates';
import { TotaraFetchBadgesService } from '@/services/totara/badges';
import { TotaraFetchCompletedCoursesService } from '@/services/totara/courses';

/**
 * Route to get the announcements
 */
export async function GET() {
  try {
    // Fetching records counts
    const response = await Promise.all([
      TotaraFetchCompletedCoursesService({ page: 1, perPage: 1000 }),
      TotaraFetchCertificatesService(),
      TotaraFetchBadgesService(),
    ]);

    // Return the response
    return NextResponse.json({
      courses: response?.[0]?.data?.courses?.length || 0,
      certificates: response?.[1]?.data?.certificates?.length || 0,
      badges: response?.[2]?.data?.userbadges?.length || 0,
      results: 0,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
