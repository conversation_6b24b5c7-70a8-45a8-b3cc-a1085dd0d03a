import { getServerSideConfigBooleanValue } from '@/firebase/firebase-admin';
import AIRecommendedCoursesScreen from '@/screens/discover/ai-recommended-courses';
import { externalFeatureAccessService } from '@/services/external/feature-access';
import { Features } from '@/types/domain/feature-access/featureAccess';
import { getUserEmail } from '@/utils/auth';
import { checkFeatureAccess } from '@/utils/feature-access';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';

export const metadata: Metadata = {
  title: 'Discover - AI recommended',
};

/**
 * Next page for AI recommended courses
 *
 * route: /discover/ai-recommended
 */
export default async function AIRecommendedCoursesPage() {
  const aiFeatureEnabled = await getServerSideConfigBooleanValue('aiFeature');
  const email = await getUserEmail();
  const featuresResponse = await externalFeatureAccessService(email!);
  const userAIAccess = checkFeatureAccess(
    Features.AI,
    featuresResponse?.data?.features,
  );

  if (!aiFeatureEnabled && !userAIAccess) notFound();

  return <AIRecommendedCoursesScreen />;
}
