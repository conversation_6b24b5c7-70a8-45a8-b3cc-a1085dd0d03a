import CommunityScreen from '@/screens/community';
import { TotaraFetchCommunityDetailsService } from '@/services/totara/my-network';
import type { Metadata } from 'next';

type Props = {
  params: Promise<{ slug: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const id = (await params).slug;

  try {
    const response = await TotaraFetchCommunityDetailsService(Number(id));
    if (response.data.exception) {
      throw new Error(response.data?.message);
    }

    return {
      title: response.data?.network_details?.title,
      description: response.data?.network_details?.summary,
      openGraph: {
        title: response.data?.network_details?.title,
        description: response.data?.network_details?.summary,
        images: [{ url: response.data?.network_details?.imageurl }],
      },
      twitter: {
        title: response.data?.network_details?.title,
        description: response.data?.network_details?.summary,
        images: [{ url: response.data?.network_details?.imageurl }],
      },
    };
  } catch (error) {
    console.log(error);
    return {};
  }
}

const CommunityPage = () => {
  // Return JSX
  return <CommunityScreen />;
};

export default CommunityPage;
