@use 'mixins' as *;

.coachEducation {
  display: flex;
  gap: size(16);

  border-bottom: size(1) solid var(--cool-grey-800);
  @include forMarginPadding(margin, size(0), size(0), size(32), size(0));
  @include forMarginPadding(padding, size(0), size(0), size(40), size(0));

  &:last-child {
    border-bottom: none;
  }

  .schoolLogo {
    .photo {
      width: size(130);
      height: size(130);
      @include borderRadius(50%);
    }

    i {
      display: flex;
      justify-content: center;
      align-items: center;
      @include borderRadius(50%);
      background-color: var(--bg-shade-10);
      width: size(64);
      height: size(64);

      svg {
        fill: var(--grey-800);
        width: size(28);
        height: size(28);
      }
    }
  }

  .educationDetails {
    .schoolName {
      font-weight: 700;
      font-size: size(20);
      color: var(--grey-900);

      @include forMarginPadding(margin, size(0), size(0), size(6), size(0));
    }

    .degreeName {
      font-size: size(16);
      color: var(--grey-900);

      @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
    }

    .datesAndLocation {
      display: flex;
      gap: size(6);

      font-size: size(14);
      font-weight: 400;
      color: var(--grey-700);
    }
  }
}
