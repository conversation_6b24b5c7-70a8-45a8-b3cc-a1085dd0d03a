import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchTrendingCoursesService } from '@/services/totara/courses';

/**
 * Route to get the courses
 */
export async function GET() {
  try {
    // Fetching records counts
    const response = await TotaraFetchTrendingCoursesService();

    // Return the response
    return NextResponse.json({
      list: response?.data?.records || [],
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
