import { NextRequest, NextResponse } from 'next/server';
import { ExternalGetEndorsementsService } from '@/services/external/profile/skills/get-endorsements';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';

// To handle a GET request to /api
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const skillName = request.nextUrl.searchParams.get('skillName') || '';
    const talentProfileId =
      request.nextUrl.searchParams.get('talentProfileId') || '';
    // Fetching user profile
    const response = await ExternalGetEndorsementsService(
      skillName,
      talentProfileId,
    );
    // Return the response
    return NextResponse.json({
      endorsements: response?.data,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error as AxiosError);
  }
}
