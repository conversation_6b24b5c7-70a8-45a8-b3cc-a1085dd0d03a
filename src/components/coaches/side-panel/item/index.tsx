import { FC, useCallback } from 'react';
import { StaticImageData } from 'next/image';
import Typography from '@/ui/typography';
// import userImage from '@public/images/mahsa.png';
import { Pin, Clock, Teams, Dots } from '@/ui/icons';

import styles from '../style.module.scss';
import { useLocale, useTranslations } from 'next-intl';
import { hasTranslation } from '@/utils/translate/helpers';
import dictionary from '@/dictionaries';
import moment from 'moment';

interface IItem {
  title?: string;
  designation?: string;
  userImg?: StaticImageData | string;
  day?: string;
  time?: string;
  location?: string;
  description?: string | null;
  showDescription?: boolean;
  meetingLink?: string | null;
  photo?: StaticImageData | string;
}

const BookedSessionItem: FC<IItem> = (props) => {
  const {
    title,
    designation,
    day,
    time,
    location,
    description,
    showDescription = false,
    meetingLink,
    photo,
  } = props;
  const locale = useLocale();
  // Getting translations
  const t = useTranslations();

  const _hasTranslation = useCallback(hasTranslation, [designation]);

  const translateDesignation = (designation: string | undefined) => {
    if (!designation) return designation;

    if (designation?.includes('\n')) {
      return designation
        .split('\n')
        .map((item) => (_hasTranslation(item) ? t(item) : item))
        .join(' ');
    } else {
      return _hasTranslation(designation) ? t(designation) : designation;
    }
  };

  const designationTranslation = translateDesignation(designation);

  const bookedSessionDate = moment
    .unix(Number(day))
    .locale(locale === 'ar' ? 'ar' : 'en');

  const withPhotoView = photo ? true : false;

  return (
    <div className={styles.card}>
      <div className={styles.header}>
        {withPhotoView ? (
          <div>
            <img
              src={photo as string}
              alt="coach photo"
              className={`img-fluid ${styles.photo}`}
            />
          </div>
        ) : (
          <div className={styles.sessionDate}>
            <div>{bookedSessionDate.format('D')}</div>
            <div className={styles.month}>
              {bookedSessionDate.format('MMM').toUpperCase()}
            </div>
          </div>
        )}

        <div className={styles.info}>
          <Typography className={styles.sessionWith}>Session with</Typography>
          <Typography as="span" className="title">
            {title}
          </Typography>
          <Typography as="span" className={styles.designation}>
            {designationTranslation}
          </Typography>
        </div>
        {withPhotoView ? (
          <div className={styles.options}>
            <Dots />
          </div>
        ) : (
          ''
        )}
      </div>
      <div className={styles.body}>
        {withPhotoView && (
          <div className={styles.sessionDate}>
            <div>{bookedSessionDate.format('D')}</div>
            <div className={styles.month}>
              {bookedSessionDate.format('MMM').toUpperCase()}
            </div>
          </div>
        )}
        <nav className={styles.detailList}>
          <ul>
            <li>
              <i className="fa-icon">
                <Clock />
              </i>
              <Typography as="span">{time}</Typography>
            </li>
            {location && (
              <li>
                <i className="fa-icon">
                  <Pin />
                </i>
                <Typography as="span">{location}</Typography>
              </li>
            )}
            {meetingLink && (
              <li>
                <i className="fa-icon">
                  <Teams />
                </i>
                <Typography
                  dictionary={dictionary.meetingLink}
                  as="span"
                  className={styles.meetingLinkTypography}
                >
                  <a href={meetingLink} target="_blank"></a>
                </Typography>
              </li>
            )}
            {showDescription && description && (
              <li>
                <Typography as="span">{description}</Typography>
              </li>
            )}
          </ul>
        </nav>
      </div>
    </div>
  );
};

export default BookedSessionItem;
