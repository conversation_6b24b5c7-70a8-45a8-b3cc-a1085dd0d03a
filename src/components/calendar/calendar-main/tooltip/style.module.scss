@use 'mixins' as *;

.eventTooltip {
  position: absolute;
  //   transform: translateX(-50%);
  background-color: var(--white);
  border: 1px solid var(--grey-400);
  border-radius: 8px;

  box-shadow: 0px 20px 20px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  min-width: 280px;
  max-width: 400px;
  color: #333;
  font-size: 14px;
  line-height: 1.5;
  text-align: left;
  transition:
    opacity 0.2s ease-out,
    transform 0.2s ease-out;

  .tooltipWrapper {
    padding: size(24) size(32);
    opacity: 1;
    display: flex;
    flex-direction: column;
    gap: size(24);
    position: relative;
    z-index: 999;

    .arrow {
      position: absolute;
      width: size(16);
      height: size(16);
      background-color: var(--white);
      transform: rotate(45deg);

      z-index: 998;
      border-radius: size(3);
    }

    .banner {
      height: size(64);
      width: size(64);

      img {
        height: 100%;
        width: 100%;
        object-fit: cover;
        border-radius: size(12);
      }
    }

    .header {
      display: flex;
      flex-direction: column;
      gap: size(16);

      .courseTitle {
        color: var(--grey-900);
      }

      .basicInfo {
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        gap: size(8);

        span {
          font-size: size(14);
          line-height: size(10);
          color: var(--grey-900);
          display: flex;
          flex-direction: row;
          align-items: center;
        }

        .time {
          margin: 0;
          position: relative;

          svg {
            margin-right: size(4);
          }
        }

        .date,
        .location,
        .time {
          svg {
            width: size(16);
            height: size(16);
            margin-right: size(4);

            path {
              color: var(--grey-900);
              stroke: var(--grey-900);
            }
          }
        }
      }

      .curator {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: size(8);

        i {
          display: block;
          width: size(16);
          height: size(16);
          overflow: hidden;
          border-radius: 50%;
        }

        span {
          font-size: size(14);
          color: var(--grey-900);
          line-height: size(10);
        }
      }
    }

    .viewDetailsButton {
      border: none;
      outline: none;
      background-color: transparent;
      color: var(--text-link-blue);
      font-size: size(14);
      line-height: size(10);
      cursor: pointer;
      text-decoration: underline;
      text-transform: capitalize;
      width: fit-content;
    }
  }
}
