@use 'mixins' as *;

.itemWrapper {
  display: flex;
  padding-top: size(20);
  padding-bottom: size(20);
  border-bottom: solid 1px var(--grey-400);

  &:last-child {
    border-bottom: none;
  }

  .genericDocument {
    width: size(100);
    height: size(100);
  }

  .itemDetails {
    padding-left: size(20);
    display: flex;
    flex-direction: column;
    justify-content: center;

    .fileName {
      font-size: size(22);
      font-weight: 500;
      @include forMarginPadding(margin, size(0), size(0), size(8), size(0));
      @include for-all-phone() {
        font-size: size(16);
      }
    }

    .downloadBtn {
      display: flex;
      align-items: center;

      svg {
        width: size(20);
        height: size(20);
        fill: var(--grey-900);
        @include for-all-phone() {
          width: size(16);
          height: size(16);
        }
      }

      .fileSize {
        margin-left: size(6);
        margin-bottom: 0;
        font-size: size(16);
        @include for-all-phone() {
          font-size: size(14);
        }
      }
    }
  }
}

// @include for-all-phone() {
//   .fileName {
//     font-size: size(16);
//   }

//   .downloadBtn {
//     .fileSize {
//       font-size: size(14);
//     }
//   }
// }
