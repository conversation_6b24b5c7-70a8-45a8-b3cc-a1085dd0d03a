import { FC, useState } from 'react';

import FilterIcon from '@/ui/icons/filter';

import styles from './style.module.scss';
import Head from './head';
import Button from '@/ui/form/button';
import Typography from '@/ui/typography';
import dictionary from '@/dictionaries';
import Roles from './roles';
import Entity from './entity';
import DialogDrawer from '@/ui/dialog-drawer';

const Filters: FC = () => {
  // Implement logic similar to loginc in component Filter in Discover feature
  const [open, setOpen] = useState(false);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const onApplyFilter = () => {
    throw new Error('Function not implemented.');
  };

  return (
    <>
      <button className={styles.filterBtn} onClick={handleOpen}>
        <i>
          <FilterIcon />
        </i>
      </button>
      <DialogDrawer isOpened={open} onClose={handleClose}>
        <div className={styles.wrapper}>
          <Head />
          <Roles />
          <Entity />

          <Button
            type="button"
            onClick={onApplyFilter}
            color="black"
            className="dialog-form-action"
          >
            <Typography as="span" dictionary={dictionary.applyFilters} />
          </Button>
        </div>
      </DialogDrawer>
    </>
  );
};
export default Filters;
