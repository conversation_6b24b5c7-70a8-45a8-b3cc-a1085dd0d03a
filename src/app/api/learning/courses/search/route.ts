import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraSearchCoursesService } from '@/services/totara/courses';
import IPagination from '@/types/domain/internal/base/pagination';
import { mapProgramToCourse2 } from '@/utils/helpers';
import ICourse from '@/types/domain/learning/course';

/**
 * Route to get the announcements
 */
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const categoryID = request.nextUrl.searchParams.get('categoryID') || null;
    const keyword = request.nextUrl.searchParams.get('keyword') || null;
    const type = request.nextUrl.searchParams.get('learningType') || null;
    const product = request.nextUrl.searchParams.get('product') || null;
    const duration = request.nextUrl.searchParams.get('duration') || null;
    const page = request.nextUrl.searchParams.get('page') || '1';
    const perPage = request.nextUrl.searchParams.get('perPage') || '10';

    const creatorString = request.nextUrl.searchParams.get('creator') || null;
    let creator: string[] | null = null;

    if (creatorString) {
      try {
        const parsed = JSON.parse(creatorString);
        if (Array.isArray(parsed)) {
          creator = parsed;
        }
      } catch (e) {
        console.error(
          "Could not parse 'creator' parameter. Ignoring the filter.",
          e,
        );
      }
    }

    // Fetching records counts
    const response = await TotaraSearchCoursesService({
      categoryID: categoryID,
      keyword: keyword,
      duration: duration,
      product: product,
      creator: creator,
      learningType: type,
      page: parseInt(page),
      perPage: parseInt(perPage),
    });

    const pagination: IPagination = {
      total: response?.data?.pagination_detail?.total_items || 0,
      page: response?.data?.pagination_detail?.current_page || 0,
      perPage: response?.data?.pagination_detail?.page_size || 0,
      totalPages: response?.data?.pagination_detail?.total_pages || 0,
    };

    const programmes = mapProgramToCourse2(
      response?.data?.selfenrolprograms || [],
    );

    const internalcourses = (response?.data?.internalcourses || []).map(
      (course: ICourse) => {
        return { ...course, type: 'course' };
      },
    );

    const librarycourses = (response?.data?.librarycourses || []).map(
      (course: ICourse) => {
        return { ...course, type: 'course' };
      },
    );

    // Return the response
    return NextResponse.json({
      list: [...internalcourses, ...librarycourses, ...programmes],
      pagination: pagination,
      subcategory: response?.data?.subcategory,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
