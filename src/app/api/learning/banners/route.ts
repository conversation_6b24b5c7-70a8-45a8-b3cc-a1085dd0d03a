import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchBannerService } from '@/services/totara/banner';

/**
 * Route to get the announcements
 */
export async function GET() {
  try {
    // Updating user profile
    const response = await TotaraFetchBannerService();
    // Return the response
    return NextResponse.json({
      total: response?.data?.length || 0,
      list: response?.data || [],
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
