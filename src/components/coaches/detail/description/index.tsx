'use client';
import { FC, useCallback, useMemo, useState } from 'react';
// import useRateCoachStore from '@/stores/learning/coaches/rate-coach';
// import { RATE_STATUS } from '../constants';
import Typography from '@/ui/typography';
// import Button from '@/ui/form/button';
import { Close, WorkOutline } from '@/ui/icons';
import Image from 'next/image';
import star from '@public/images/yellow-star.svg';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';
import styles from './style.module.scss';
import CoachExperienceList from '../../experience';
import { ICoachExperience } from '@/types/domain/learning/coach/experience';
import Tabs from '@/ui/tabs';
import AvailableSessions from '../booking/widget';
import CoachReview from './reviews';
import {
  coachReviews,
  coachEducation,
  coachWorkHistory,
} from '../../mock-data';
import Education from './education';
import CoachWorkHistory from './work-history';

interface IDescription {
  coachId?: string;
  image?: string;
  title?: string;
  designation?: string;
  department?: string;
  experience?: ICoachExperience[];
  skills?: string[];
  content?: string;
  slots?: number;
  rating?: string;
  users?: string;
  onNext?: () => void;
  onRate?: () => void;
  onBack?: () => void;
}

const Description: FC<IDescription> = (props) => {
  const {
    coachId,
    title,
    image,
    designation,
    experience,
    skills = [],
    content,
    // slots,
    rating,
    onNext: viewAllSessions,
    // onRate,
    onBack,
  } = props;
  const t = useTranslations();
  // Property to hold active tab id
  const [tab, setTab] = useState('1');

  const handleChangeTab = useCallback((id: string) => {
    setTab(id);
  }, []);

  const TabsOptions = useMemo(
    () => [
      { id: '1', label: t(dictionary.Overview) },
      { id: '2', label: 'Experience' },
      { id: '3', label: t(dictionary.education) },
      { id: '4', label: t(dictionary.reviews) },
    ],
    [t],
  );

  // State to hold show more content
  const [showMore, setShowMore] = useState(false);
  // const { rateStatus, rateMessage } = useRateCoachStore();
  /**
   * Method to calculate if use can see
   * more details
   */
  const canShowMore = useMemo(() => {
    return content && content?.length > 500;
  }, [content]);

  /**
   * Method to get the details with
   * show more funtionality
   */
  const details = useMemo(() => {
    // If show more is true
    if (showMore) return content;
    // If show more is false and can show more
    if (!showMore && canShowMore) return content?.slice(0, 500) + '...';
    // If show more is false and can't show more
    return content;
  }, [content, showMore, canShowMore]);

  /**
   * Method to toggle show more
   */
  const toggleShowMore = useCallback(() => {
    setShowMore((x) => !x);
  }, [setShowMore]);

  const skillsList = useMemo(() => {
    if (skills?.length) {
      const restCount = skills?.length > 3 ? skills?.length - 3 : 0;
      return skills?.map((item, index) => {
        if (index < 3) {
          return (
            <div className={styles.skillItem} key={item}>
              <Typography as="span" className={styles.skill}>
                {index < 3 ? item : `+${restCount}`}
              </Typography>
            </div>
          );
        }
      });
    }
  }, [skills]);

  // Return JSX
  return (
    <>
      <div className={styles.coachModalHeader}>
        <button
          type="button"
          aria-label="Back To Screen"
          className={styles.backBtn}
          onClick={onBack}
        >
          <i>
            <Close />
          </i>
        </button>
      </div>
      <div className={styles.body}>
        <div className={styles.banner}>
          <div
            className={styles.bannerImg}
            style={{
              backgroundImage: `url(${image})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          />
        </div>
        <div className={styles.bannerData}>
          <div className={styles.nameAndRating}>
            {title && <Typography as="h2">{title}</Typography>}
            {Number(rating) != 0 ? (
              <>
                <div className={styles.rating}>
                  <Image
                    src={star}
                    className="img-fluid"
                    alt=""
                    width={16}
                    height={16}
                  />
                  {rating}
                </div>
              </>
            ) : null}
          </div>
          <div className={styles.infoWrapper}>
            {designation && (
              <>
                <WorkOutline />
                <Typography as="p">{designation}</Typography>
              </>
            )}
          </div>
          <CoachExperienceList displayCount={3} experience={experience} />
          {
            <div>
              <Typography as="h4" dictionary={dictionary.skills} />
              <div className={styles.skills}>{skillsList}</div>
            </div>
          }
        </div>
        <Tabs active={tab} tabs={TabsOptions} onChange={handleChangeTab} />
        <div className={styles.tabSections}>
          {tab === '1' && (
            <>
              {details && (
                <>
                  <div className={styles.section}>
                    <Typography as="h4" dictionary={dictionary.Overview} />
                    <Typography as="p" className={styles.description + ' mb-0'}>
                      {details}
                    </Typography>
                    {canShowMore && (
                      <button
                        type="button"
                        className={styles.moreBtn}
                        onClick={toggleShowMore}
                      >
                        <span className="resizable">
                          {showMore
                            ? t(dictionary.viewLess)
                            : t(dictionary.viewMore)}
                        </span>
                      </button>
                    )}
                  </div>
                  <AvailableSessions
                    viewAllSessions={viewAllSessions}
                    coachId={coachId}
                  />
                  {/* {slots ? (
                    <div className={styles.section}>
                      <div className={styles.alert}>
                        <div className={styles.icon}>
                          <i className="fa-icon">
                            <CalendarOutline />
                          </i>
                        </div>
                        <Typography>
                          {slots} {t(dictionary.sessionsAvailable)}
                        </Typography>
                      </div>
                    </div>
                  ) : null} */}
                  <div className={styles.footer}>
                    {/* {rateStatus ? (
                      <Button onClick={onRate}>
                        {t(dictionary.rateYourCoach)}
                      </Button>
                    ) : null}
                    {rateMessage == RATE_STATUS['ALREADY_RATED'] ? (
                      <Button onClick={onRate}>
                        {t(dictionary.viewYourRating)}
                      </Button>
                    ) : null} */}
                  </div>
                </>
              )}
            </>
          )}
          {tab === '2' && (
            <>
              <Typography>Experience</Typography>
              {coachWorkHistory.map((workHistory) => {
                return (
                  <CoachWorkHistory
                    key={workHistory.id}
                    logo={workHistory.logo}
                    designation={workHistory.designation}
                    department={workHistory.department}
                    employer={workHistory.employer}
                    date_from={workHistory.date_from}
                    date_to={workHistory.date_to}
                    time_period={workHistory.time_period}
                    location={workHistory.location}
                  />
                );
              })}
            </>
          )}
          {tab === '3' && (
            <>
              <Typography className={styles.tabTitle}>Education</Typography>
              {coachEducation.map((education) => {
                return (
                  <Education
                    key={education.id}
                    logo={education.logo}
                    school={education.school}
                    degree_name={education.degree_name}
                    date_from={education.date_from}
                    date_to={education.date_to}
                    location={education.location}
                  />
                );
              })}
            </>
          )}
          {tab === '4' && (
            <>
              <Typography className={styles.tabTitle}>Reviews</Typography>
              {coachReviews.map((review) => {
                return (
                  <CoachReview
                    userPhoto={review.userPhoto}
                    firstName={review.firstName}
                    rating={review.rating}
                    date={review.date}
                    comment={review.comment}
                    key={review.reviewId}
                  />
                );
              })}
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default Description;
