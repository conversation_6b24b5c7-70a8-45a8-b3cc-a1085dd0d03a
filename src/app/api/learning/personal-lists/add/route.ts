import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraAddPersonalListService } from '@/services/totara/personal-lists';

export async function GET(request: NextRequest) {
  const name = request.nextUrl.searchParams.get('name') || '';

  try {
    const response = await TotaraAddPersonalListService(name);

    return NextResponse.json({
      addList: response?.data || null,
    });
  } catch (error) {
    return ErrorResponse(error);
  }
}
