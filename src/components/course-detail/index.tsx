import { useCallback, useEffect, useMemo, useState } from 'react';
import { useParams } from 'next/navigation';
import Paper from '@/ui/paper';
import Tabs from '@/ui/tabs';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '../common/loading';
import Banner from './banner';
import Header from './header';
import Description from './description';
import Prerequisites from './prerequisites';
import Instructors from './instructors';
import Certificates from './certificate';
import Activities from './activities';
import Lessons from './lessons';
import Reviews from './reviews';
import useCourseDetailStore from '@/stores/learning/courses/detail';
import useCoreCourseStore from '@/stores/learning/courses/core-course';
import { useTranslations } from 'next-intl';

// Styles
import styles from './style.module.scss';
import WriteReview from './write-review';
import useToggleable from '@/hooks/helpers/useToggle';
import ICourse from '@/types/domain/learning/course';
import dictionary from '@/dictionaries';
import Agenda from './agenda';
import LocationTab from './location';

const CourseDetail = () => {
  // Getting params
  const courseId = useParams().slug;
  const t = useTranslations();

  // Popup state
  const popup = useToggleable(false);

  // Getting fetch action
  const onFetch = useCourseDetailStore((state) => state.fetch);

  // Getting loading state
  const isFetching = useCourseDetailStore((state) => state.isFetching);

  // Getting course detail
  const courseDetail = useCourseDetailStore((state) => state.detail);
  const onCoreCourseFetch = useCoreCourseStore((state) => state.fetch);
  const isFetchingCore = useCoreCourseStore((state) => state.isFetching);
  const coreCourseDetails = useCoreCourseStore(
    (state) => state.coreCourseDetails,
  );
  const sections = coreCourseDetails?.mobile_course?.course?.sections;

  // Property to hold active tab id
  const [tab, setTab] = useState('1');

  /**
   * Method to handle change active tab
   */
  const handleChangeTab = useCallback((id: string) => {
    setTab(id);
  }, []);

  useEffect(() => {
    if (courseId) {
      onFetch(courseId as string);
    }
  }, [courseId, onFetch]);

  useEffect(() => {
    if (courseId) {
      onCoreCourseFetch(courseId as string);
    }
  }, [courseId, onCoreCourseFetch]);

  const shouldShowLocationTab =
    courseDetail?.course_day === 'One Day' ||
    (courseDetail?.assigned_slot_multiday &&
      courseDetail?.assigned_slot_multiday?.length > 0);

  const TabsOptions = useMemo(
    () =>
      [
        { id: '1', label: t(dictionary.info) },
        { id: '2', label: t(dictionary.activities) },
        { id: '3', label: t(dictionary.agenda) },
        { id: '4', label: t(dictionary.reviews) },
        { id: '5', label: t(dictionary.location) },
      ].filter((tab) => {
        if (
          courseDetail?.courseType === 'Self-paced' ||
          (courseDetail?.course_agenda?.length === 0 && !shouldShowLocationTab)
        ) {
          return tab.id !== '3' && tab.id !== '5';
        } else if (courseDetail?.course_agenda?.length === 0) {
          return tab.id !== '3';
        } else if (!shouldShowLocationTab) {
          return tab.id !== '5';
        }
        return true;
      }),
    [
      courseDetail?.courseType,
      courseDetail?.course_agenda?.length,
      shouldShowLocationTab,
      t,
    ],
  );

  return (
    <>
      <Banner
        isLoading={isFetching}
        image={courseDetail?.course_image}
        course={courseDetail as ICourse}
      />
      <WithLoader
        loading={isFetching && isFetchingCore}
        loader={<Loading className={styles.courseDetailWrapper} />}
      >
        <Paper className="overflow-visible pt-0">
          <div className={styles.headerFixed}>
            <Header
              title={courseDetail?.course_name}
              courseId={courseId}
              courseType={courseDetail?.courseType}
              dueDate={courseDetail?.due_date}
              hours={courseDetail?.duration}
              lessons={courseDetail?.lessons?.length}
              rating={courseDetail?.review_overall_rating}
              learners={courseDetail?.completed_users}
              academyName={courseDetail?.course_curatorname}
              academyImage={courseDetail?.course_curatorlogo}
              isEnrolled={courseDetail?.isEnrolled}
              progress={courseDetail?.progress}
              status={courseDetail?.status}
              reviewAvailabe={courseDetail?.review_available}
              onShowReview={popup.open}
            />
            <Tabs active={tab} tabs={TabsOptions} onChange={handleChangeTab} />
          </div>
          {popup.visible && <WriteReview onClose={popup.close} />}
          <div className={styles.descriptionBody}>
            {tab === '1' && (
              <>
                {courseDetail?.course_description && (
                  <Description content={courseDetail?.course_description} />
                )}
                {!!courseDetail?.course_prerequisites?.length && (
                  <Prerequisites list={courseDetail?.course_prerequisites} />
                )}
                {!!courseDetail?.instructors?.length && (
                  <Instructors list={courseDetail?.instructors} />
                )}
                {!!courseDetail?.certificate?.length && (
                  <Certificates list={courseDetail?.certificate} />
                )}
              </>
            )}
            {tab === '2' && (
              <>
                {!courseDetail?.is_external_course ? (
                  <Activities
                    list={sections}
                    isEnrolled={courseDetail?.isEnrolled}
                  />
                ) : (
                  <Lessons
                    list={sections}
                    courseType={courseDetail?.courseType}
                    isEnrolled={courseDetail?.isEnrolled}
                  />
                )}
              </>
            )}
            {tab === '3' && <Agenda agenda={courseDetail?.course_agenda} />}
            {tab === '4' && (
              <Reviews
                reviews={courseDetail?.reviews}
                overallRating={courseDetail?.review_overall_rating}
              />
            )}
            {tab === '5' && shouldShowLocationTab && <LocationTab />}
          </div>
        </Paper>
      </WithLoader>
    </>
  );
};

export default CourseDetail;
