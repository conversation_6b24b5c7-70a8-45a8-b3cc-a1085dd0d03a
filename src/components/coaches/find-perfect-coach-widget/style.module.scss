@use 'mixins' as *;

.matchedCoaches {
  @include forMarginPadding(padding, size(24), size(24), size(24), size(24));

  .header {
    display: flex;
    justify-content: space-between;
    @include forMarginPadding(margin, size(6), size(0), size(32), size(0));

    .titleWrapper {
      p.title {
        font-size: size(26);
        font-weight: 700;
        line-height: 1.2;
        letter-spacing: size(0);
      }

      .subText {
        font-size: size(18);
        font-weight: 400;
        line-height: 1.2;
        letter-spacing: 0%;
        color: var(--grey-700);
      }
    }

    .regerate {
      display: flex;
      background-color: var(--white);
      width: size(148);
      height: size(40);
      font-size: size(16);
      color: var(--grey-900);

      svg {
        fill: var(--grey-900);
        height: size(24);
        width: size(24);
      }
    }
  }

  .coachesList {
    min-height: size(100);
    position: relative;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: size(16);
  }
}

.findPerfectCoachWrapper {
  display: flex;
  min-height: size(290);

  .findPerfectCoach {
    display: flex;
    justify-content: space-between;
    flex-grow: 1;

    background-image: url(/images/find-coach-banner.png);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;

    @include borderRadius(size(12));

    .descriptionWrapper {
      @include forMarginPadding(padding, size(48), size(0), size(0), size(48));
      flex-grow: 1;

      .title {
        color: var(--always-dark);
        font-size: size(32);
        font-weight: 700;
        line-height: 1.2;
        letter-spacing: size(0);
        @include forMarginPadding(margin, size(0), size(0), size(24), size(0));
      }

      .description {
        @include forMarginPadding(margin, size(0), size(0), size(40), size(0));

        span {
          color: var(--grey-800);
          display: block;
          font-size: size(22);
          font-weight: 400;
          line-height: 1.3;
          letter-spacing: size(0);
        }
      }

      .findCoachBtn {
        background-color: var(--always-dark);
        color: var(--always-white);
        width: size(248);
        height: size(56);

        &:hover {
          background-color: var(--always-white);
          color: var(--always-dark);
        }
      }
    }
  }
}

@include for-dark-theme {
  .description {
    span {
      color: #262626 !important;
    }
  }

  .findCoachBtn {
    &:hover {
      border-color: var(--always-dark);
    }
  }
}
