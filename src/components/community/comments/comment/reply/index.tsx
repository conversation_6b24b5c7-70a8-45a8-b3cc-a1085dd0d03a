import Typography from '@/ui/typography';
import styles from './../style.module.scss';
import {
  EApiAreaTypes,
  EApiComponentTypes,
  IReply,
} from '@/types/domain/my-network/communityPost';
import { LikeIcon } from '@/ui/icons';
import dictionary from '@/dictionaries';
import { getFullNameInitials } from '@/utils/helpers';
import Image from 'next/image';
import useAuthStore from '@/stores/auth';
import useCommunityDetailsCommentsStore from '@/stores/my-network/community-details/comments';
import CommentKebab from '../commentKebab';
import Reply from '@/ui/icons/reply';
import LikeFillIcon from '@/ui/icons/like-fill';
import React, { useState } from 'react';

interface IReplyItem {
  reply: IReply;
  postId: number;
  setUserTag: (userfullname: string) => void;
  commentId: number;
}

const ReplyItem: React.FC<IReplyItem> = ({
  reply,
  postId,
  setUserTag,
  commentId,
}) => {
  const [isPreview, setIsPreview] = useState(false);
  const { totaraProfile } = useAuthStore();
  const { deleteComment, toggleLikeStatus } =
    useCommunityDetailsCommentsStore();

  const fullNameInitials = getFullNameInitials(reply.userfullname);

  const onClickDelete = () => {
    deleteComment(reply.id, postId);
  };

  const onClickReply = () => {
    setUserTag(reply.userfullname);
  };

  const onClickLike = () => {
    toggleLikeStatus(
      postId,
      reply.is_liked,
      EApiAreaTypes.REPLY,
      EApiComponentTypes.TOTARA_COMMENT,
      commentId,
      reply.id,
    );
  };

  const handlePreview = (isPreview: boolean) => {
    setIsPreview(!isPreview);
  };

  return (
    <div className={styles.commentWrapper}>
      <div className={styles.avatar}>
        <Typography>{fullNameInitials}</Typography>
      </div>

      <div className={styles.containerContent}>
        <div className={styles.containerComment}>
          {reply.userid === totaraProfile?.id && (
            <CommentKebab onClickDelete={onClickDelete} />
          )}

          <div className={styles.headerComment}>
            <Typography as="span" className={styles.title}>
              {reply.userfullname}
            </Typography>
            <span aria-hidden="true" className="dot-breaker"></span>
            <Typography as="span" className={styles.time}>
              {reply.timecreated}
            </Typography>
          </div>
          <div className={styles.message}>
            <Typography as="p"> {reply.contenttext}</Typography>
          </div>
          {reply.images.length > 0 && (
            <div className={styles.postImage}>
              {reply.images.map(
                (image) =>
                  image?.url && (
                    <React.Fragment key={image.url}>
                      <Image
                        src={image.url}
                        alt=""
                        width={200}
                        height={200}
                        onClick={() => handlePreview(isPreview)}
                      />
                      {isPreview && (
                        <div
                          className={styles.lightBoxOverlay}
                          onClick={() => handlePreview(isPreview)}
                        >
                          <div
                            className={styles.lightBoxContent}
                            onClick={(e) => e.stopPropagation()}
                          >
                            <img
                              src={image.url}
                              alt={image.url}
                              className={styles.lightBoxImage}
                            />
                          </div>
                        </div>
                      )}
                    </React.Fragment>
                  ),
              )}
            </div>
          )}
        </div>

        <div className={styles.containerLike}>
          <button
            type="button"
            className={styles.ftButton}
            onClick={onClickLike}
          >
            {reply.is_liked ? (
              <i>
                <LikeFillIcon />
              </i>
            ) : (
              <i>
                <LikeIcon />
              </i>
            )}
            <Typography as="span">
              <Typography as="span" dictionary={dictionary.like} /> (
              {reply.likes})
            </Typography>
          </button>
          <button
            type="button"
            className={styles.ftButton}
            onClick={onClickReply}
          >
            <i>
              <Reply />
            </i>
            <Typography as="span">
              <Typography as="span" dictionary={dictionary.reply} />
            </Typography>
          </button>
        </div>
      </div>
    </div>
  );
};
export default ReplyItem;
