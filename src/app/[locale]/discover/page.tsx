import DiscoverScreen from '@/screens/discover';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Discover',
};

interface IDiscoverPageProps {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function DiscoverPage(props: IDiscoverPageProps) {
  const searchParams = await props.searchParams;

  // Return Screen
  return (
    <DiscoverScreen searchPage={Object.entries(searchParams)?.length > 0} />
  );
}
