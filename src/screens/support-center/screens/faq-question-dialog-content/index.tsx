import Typography from '@/ui/typography';
import React, { useMemo } from 'react';

// Minimal HTML sanitizer to remove scripts and event handlers.
function sanitizeHtmlSimple(html: string): string {
  if (!html) return '';
  let output = html.replace(
    /<\s*(script|style)[^>]*>[\s\S]*?<\s*\/\s*\1>/gi,
    '',
  );

  output = output.replace(/on[a-z]+\s*=\s*"[^"]*"/gi, '');
  output = output.replace(/on[a-z]+\s*=\s*'[^']*'/gi, '');
  output = output.replace(/on[a-z]+\s*=\s*[^\s>]+/gi, '');
  output = output.replace(/javascript:/gi, '');
  // Optionally allow only a subset of tags by stripping others
  output = output.replace(
    /<(?!\/?(p|br|ul|ol|li|strong|em|a)(\s|>|$))[^>]+>/gi,
    '',
  );
  // Remove dangerous attributes like style, formaction
  output = output.replace(
    /\s(style|formaction|srcdoc)\s*=\s*("[^"]*"|'[^']*'|[^\s>]+)/gi,
    '',
  );
  // Ensure target and rel on links are safe if present
  output = output.replace(/<a\s+([^>]*?)>/gi, (_m, attrs) => {
    let safe = attrs
      .replace(/\s*target\s*=\s*("[^"]*"|'[^']*'|[^\s>]+)/gi, '')
      .replace(/\s*rel\s*=\s*("[^"]*"|'[^']*'|[^\s>]+)/gi, '');
    safe += ' target="_blank" rel="noopener noreferrer"';
    return `<a ${safe}>`;
  });
  return output;
}

const FaqQuestionDialogContent: React.FC<{ answer: string }> = ({ answer }) => {
  const sanitized = useMemo(() => {
    try {
      return sanitizeHtmlSimple(answer || '');
    } catch {
      return '';
    }
  }, [answer]);

  return (
    <>
      {sanitized ? (
        <div dangerouslySetInnerHTML={{ __html: sanitized }} />
      ) : (
        <Typography as="span">{answer || ''}</Typography>
      )}
    </>
  );
};

export default FaqQuestionDialogContent;
