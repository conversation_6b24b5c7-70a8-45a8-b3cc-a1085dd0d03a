@use 'mixins' as *;

.menuWrap {
  position: relative;
  @include rightToLeft(size(0));
  top: size(0);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: size(22);
  height: size(22);
  z-index: 9;

  div[class*='container'] > button {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    width: size(18);
    height: size(18);

    &:before {
      display: none;
    }

    i {
      width: 100%;
      height: 100%;

      svg {
        width: 100%;
        height: 100%;
      }
    }

    + button {
      @include forMarginPadding(margin, size(0), size(0), size(0), size(10));
    }
  }

  div[class*='menu'] {
    min-width: size(240);
  }
}

.menuList {
  background-color: var(--cool-grey-500);
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));

  li {
    list-style: none;
    border-bottom: solid 1px var(--cool-grey-800);

    &:last-child {
      border: none;
    }

    button {
      border: none;
      background: none;
      @include forMarginPadding(
        padding,
        size(12),
        size(12),
        size(12),
        size(12)
      );
      line-height: 1;
      display: flex;
      align-items: center;
      width: 100%;
      cursor: pointer;
      transition: 0.2s all ease-in-out;

      &:hover {
        background-color: var(--cool-grey-700);
        text-decoration: underline;
        transition: 0.2s all ease-in-out;
      }

      i {
        width: size(24);
        height: size(24);
        @include forMarginPadding(margin, size(0), size(10), size(0), size(0));

        svg {
          fill: var(--grey-900);
          fill-rule: evenodd;
        }

        &.plus {
          background: var(--grey-900);
          fill: var(--white);
          @include borderRadius(50%);
          display: flex;
          justify-content: center;
          align-items: center;

          svg {
            width: size(11);
            height: size(11);
          }
        }

        @include rtl {
          transform: scale(-1, 1);
        }
      }

      span {
        font-size: size(14);
        display: inline;
        padding-bottom: 0;
        transition: all 0.5s linear;
        background: linear-gradient(
          to bottom,
          var(--grey-900) 0%,
          var(--grey-900) 98%
        );
        background-size: 0 1px;
        background-repeat: no-repeat;
        background-position: left 100%;

        @include rtl {
          background-position: right 100%;
        }
      }
    }
  }
}

@include for-dark-theme() {
  .menuList {
    background-color: var(--grey-300);

    li {
      border-bottom: solid 1px var(--grey-400);

      &:last-child {
        border: none;
      }

      button {
        &:hover {
          background-color: #141414;
        }
      }
    }
  }
}
