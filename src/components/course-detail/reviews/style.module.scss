@use 'mixins' as *;

.reviewList {
  @include forMarginPadding(padding, size(0), size(0), size(30), size(0));
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: size(24);

  li {
    display: flex;
    align-items: center;
  }
}

.header {
  display: flex;
  @include forMarginPadding(margin, size(0), size(0), size(40), size(0));
  h4 {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }
}

.overallRating {
  display: flex;
  align-items: center;
  line-height: 1;
  @include forMarginPadding(margin, size(0), size(0), size(0), auto);

  i {
    width: size(16);
    height: size(16);
    @include forMarginPadding(margin, size(0), size(4), size(0), size(0));
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.ratingText {
  font-size: size(22);
  display: block;
  font-weight: 700;
}

.overRating {
  font-size: size(16);
  color: var(--grey-900);
  display: block;
  opacity: 0.7;
  @include forMarginPadding(margin, size(0), size(0), size(0), size(4));
}

.imageBox {
  background-color: #fff;
  overflow: hidden;
  @include borderRadius(50%);
  width: size(48);
  height: size(48);
  flex-shrink: 0;
  @include forMarginPadding(margin, size(0), size(14), size(0), size(0));
}

.name {
  font-size: size(16);
  font-weight: 700;
  display: block;
  @include forMarginPadding(margin, size(0), size(0), size(8), size(0));
}

.review {
  display: flex;
  align-items: center;
}

.rating {
  display: flex;

  i {
    display: flex;
    width: size(16);
    height: size(16);
    @include forMarginPadding(margin, size(0), size(5), size(0), size(0));
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  span {
    font-size: size(14);
    font-weight: 500;
  }
  + .ago {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(6));
  }
}

.ago {
  color: var(--grey-800);
  font-size: size(14);
  position: relative;
  @include forMarginPadding(padding, size(0), size(0), size(0), size(10));
  &:before {
    position: absolute;
    @include leftToRight(0);
    top: 0;
    bottom: 0;
    @include forMarginPadding(margin, auto, auto, auto, auto);
    width: size(4);
    height: size(4);
    @include borderRadius(50%);
    background: var(--grey-800);
    content: '';
  }
}

@include for-all-phone() {
  .reviewList {
    grid-template-columns: 1fr;
    gap: size(16);
  }
}
