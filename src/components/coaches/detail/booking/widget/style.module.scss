@use 'mixins' as *;

.availableSessions {
  border: size(1) solid var(--progressBg);
  @include borderRadius(size(16));
  @include forMarginPadding(padding, size(24), size(24), size(24), size(24));

  .header {
    .title {
      display: flex;
      justify-content: space-between;

      p {
        color: var(--grey-900);
        font-size: size(20);
        font-weight: 700;
        line-height: 1.2;
        letter-spacing: 0;
      }

      button.viewAll {
        color: var(--text-link-blue);
        font-size: size(16);
        background: none;
        border: none;
        cursor: pointer;

        span {
          display: inline;
          padding-bottom: 0;
          transition: all 0.5s linear;
          background: linear-gradient(
            to bottom,
            var(--text-link-blue) 0%,
            var(--text-link-blue) 98%
          );
          background-size: 0 1px;
          background-repeat: no-repeat;
          background-position: left 100%;

          @include rtl {
            background-position: right 100%;
          }
        }
      }
    }

    .subTitle {
      font-size: size(16);
      font-weight: 400;
      line-height: 1.2;
      letter-spacing: 0;
      color: var(--grey-700);
    }
  }

  .sessionDates {
    display: flex;
    gap: size(17);

    @include forMarginPadding(padding, size(0), size(0), size(32), size(0));
    @include forMarginPadding(margin, size(0), size(0), size(32), size(0));

    border-bottom: size(1) solid var(--grey-400);

    .sessionDate {
      border: size(1) solid var(--progressBg);
      @include borderRadius(size(12));
      @include forMarginPadding(
        padding,
        size(15),
        size(13),
        size(13),
        size(15)
      );
    }
  }

  .timeSlots {
    @include forMarginPadding(margin, size(0), size(0), size(24), size(0));

    .timeSlotsTitle {
      font-size: size(18);
      font-weight: 700;
      line-height: 1.2;
      color: var(--grey-900);
    }

    .slotList {
      display: flex;
      flex-direction: column;
      gap: size(12);
    }
  }
}
