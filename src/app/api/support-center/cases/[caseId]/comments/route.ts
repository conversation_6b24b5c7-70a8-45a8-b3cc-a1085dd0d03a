import { NextRequest, NextResponse } from 'next/server';
import { AxiosError } from 'axios';
import { ErrorResponse } from '@/utils/fetcher';
import {
  addComment,
  addAttachment,
} from '@/services/external/support-center/cases';

// Server side API that adds a comment to a support center case
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ caseId: string }> },
) {
  try {
    const data = await request.json();
    const { caseId } = await params;

    // Create the comment payload
    const commentPayload = {
      emiratesId: data.emiratesId || '',
      caseNumber: caseId,
      commentBody: data.comment,
    };

    // Add the comment using the dedicated comments endpoint
    const commentResponse = await addComment(commentPayload);

    // If there's an additional file attachment, add it separately
    if (data.attachment) {
      const fileAttachmentPayload = {
        emiratesId: data.emiratesId || '',
        caseNumber: caseId,
        attachmentName: data.attachment.name,
        attachmentFileBytes: data.attachment.fileBytes,
        attachmentFileSize: data.attachment.fileSize,
        attachmentExtension: data.attachment.extension,
      };

      await addAttachment(fileAttachmentPayload);
    }

    return NextResponse.json({
      data: {
        success: true,
        message: 'Comment added successfully',
        commentId: `comment-${Date.now()}`,
        caseId,
        commentResponse: commentResponse.data,
      },
    });
  } catch (error) {
    return ErrorResponse(error as AxiosError);
  }
}
