@use 'mixins' as *;

.flexBox {
  width: 100%;
  @include borderRadius(var(--radius));
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));
}

.img {
  width: 100%;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    vertical-align: bottom;
    @include borderRadius(var(--radius));
  }
}
