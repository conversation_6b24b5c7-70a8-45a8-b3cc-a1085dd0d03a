import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import {
  TotaraFetchStreaksByMonthService,
  TotaraFetchStreaksService,
} from '@/services/totara/streaks';

/**
 * Route to get the announcements
 */
export async function GET() {
  try {
    // Fetching streaks
    const response = await Promise.all([
      TotaraFetchStreaksService(),
      TotaraFetchStreaksByMonthService(),
    ]);
    // Return the response
    return NextResponse.json({
      total: response?.[0]?.data?.current_streaks || 0,
      goal: response?.[0]?.data?.goal || 0,
      day: response?.[0]?.data?.day || [],
      week: response?.[0]?.data?.current_weekstreak_record || [],
      month: response?.[1]?.data?.learning_records || [],
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
