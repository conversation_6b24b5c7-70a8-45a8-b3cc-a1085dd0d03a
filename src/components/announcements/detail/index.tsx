import { useEffect, useState, FC } from 'react';
import Image from 'next/image';
import { format } from '@/utils/date';
import WithLoader from '@/ui/skeleton/with-loader';
import Typography from '@/ui/typography';
import Loading from '@/components/common/loading';
import useLearningAnnouncementsStore from '@/stores/learning/announcements';
import IAnnouncement from '@/types/domain/learning/announcement';
import { CalendarRounded } from '@/ui/icons';

// Styles
import styles from './style.module.scss';
import { useLocale } from 'next-intl';

interface IAnnouncementDetail {
  id: string | undefined;
}

const AnnouncementDetail: FC<IAnnouncementDetail> = ({ id }) => {
  // Property to hold announcement detail
  const [detail, setDetail] = useState<IAnnouncement | null>(null);
  const locale = useLocale();
  // Getting fetching state
  const isFetching = useLearningAnnouncementsStore((x) => x.isFetching);

  // Geting the announcements
  const announcements = useLearningAnnouncementsStore((x) => x.list);

  useEffect(() => {
    if (id) {
      setDetail(
        announcements.findLast((x) => x.id.toString() === id.toString()) ||
          null,
      );
    }
  }, [id, announcements]);

  return (
    <WithLoader loader={<Loading />} loading={isFetching}>
      <div className={styles.detail}>
        <div className={styles.banner}>
          {detail?.image && (
            <Image
              src={detail?.image}
              width={1200}
              height={500}
              alt=""
              className="img-fluid"
            />
          )}
        </div>
        <div className={styles.content}>
          <Typography as="h3" className={styles.announcementTitle}>
            {detail?.subject}
          </Typography>
          <div className={styles.date}>
            <i className={styles.calIcon}>
              <CalendarRounded />
            </i>
            <Typography as="p">
              {format(detail?.created || '', locale)}
            </Typography>
          </div>
          <div dangerouslySetInnerHTML={{ __html: detail?.message || '' }} />
        </div>
      </div>
    </WithLoader>
  );
};

export default AnnouncementDetail;
