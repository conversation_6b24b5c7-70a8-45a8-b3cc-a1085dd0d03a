import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraGetPersonalListsService } from '@/services/totara/personal-lists';

export async function GET() {
  try {
    const response = await TotaraGetPersonalListsService();

    return NextResponse.json({
      personalLists: response?.data.personallist || null,
    });
  } catch (error) {
    return ErrorResponse(error);
  }
}
