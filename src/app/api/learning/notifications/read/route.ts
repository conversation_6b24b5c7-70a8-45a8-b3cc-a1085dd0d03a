import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraMarkNotificationAsReadService } from '@/services/totara/notifications';

/**
 * Route to mark a notification as read
 */
export async function GET(request: NextRequest) {
  try {
    const notificationId =
      request.nextUrl.searchParams.get('notificationId') || null;

    if (!notificationId) {
      return NextResponse.json(
        { message: 'Notification ID is required' },
        { status: 400 },
      );
    }

    // Mark the notification as read
    const response = await TotaraMarkNotificationAsReadService(notificationId);

    // Return the response
    return NextResponse.json({
      total: response?.data?.notifications?.length || 0,
      list: response?.data?.notifications || [],
    });
  } catch (error) {
    return ErrorResponse(error);
  }
}
