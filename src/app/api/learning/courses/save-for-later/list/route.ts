import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchFavoriteCoursesService } from '@/services/totara/courses';
import ICourse from '@/types/domain/learning/course';

/**
 * Route to get the courses
 */
export async function GET() {
  try {
    // Fetch the data
    const response = await TotaraFetchFavoriteCoursesService({
      page: 1,
      perPage: 100,
    });

    // Creating array of saved courses ids
    const savedcourses =
      response?.data?.savedcourses?.map((x: ICourse) => x.courseid) || [];

    // Return the response
    return NextResponse.json({
      list: savedcourses,
      total: savedcourses?.length || 0,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error);
  }
}
