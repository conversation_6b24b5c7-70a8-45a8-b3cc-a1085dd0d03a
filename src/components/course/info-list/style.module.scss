@use 'mixins' as *;

.list {
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));
  ul {
    @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    display: flex;
    align-items: center;

    li {
      list-style: none;
      @include forMarginPadding(margin, size(0), size(10), size(0), size(0));
      display: flex;
      align-items: center;

      i {
        width: size(14);
        height: size(14);
        @include forMarginPadding(margin, size(0), size(4), size(0), size(0));
        display: block;

        svg {
          width: 100%;
          height: 100%;
          fill-rule: evenodd;
          fill: var(--grey-900);
          vertical-align: top;
        }
      }

      span {
        color: var(--grey-900);
        font-size: size(14);
        line-height: size(14);
        font-weight: 400;
      }

      &:nth-child(3) {
        @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
      }

      &:empty {
        display: none;
      }
    }
  }
  &:last-child {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }
}

@include for-all-phone() {
  .list {
    ul {
      li {
        i {
          width: size(14);
          height: size(14);
        }

        span {
          font-size: size(14);
          line-height: size(14);
        }
      }
    }
  }
}

@include for-dark-theme() {
  .list {
    ul {
      li {
        i {
          svg {
            fill: var(--grey-800);
          }
        }

        span {
          color: var(--grey-800);
        }
      }
    }
  }
}
