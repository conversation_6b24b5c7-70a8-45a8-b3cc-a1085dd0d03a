import React, {
  useRef,
  useEffect,
  useCallback,
  useMemo,
  useState,
} from 'react';
import ReactDOM from 'react-dom';
import moment from 'moment';
import IFormatedEvent from '@/types/domain/calendar/formated-event';
import styles from './style.module.scss';
import { CalendarTimeSlot, ClockFrame, Pin } from '@/ui/icons';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';
import govAcademy from '@public/images/gov-academy.png';
import Image from '@/ui/image';
import { Link } from '@/i18n/routing';
import CONFIG from '@/config';

const BROWSER_CONTEXT = 16; // Default browser context for rem calculation

function size(pixels: number, context = BROWSER_CONTEXT) {
  return `${pixels / context}rem`;
}

interface TooltipProps {
  content: IFormatedEvent;
  position: { top: number; left: number; width: number; height: number };
  onClose: () => void;
  eventElementId?: string;
  courseId?: string;
  arrowPosition?: {
    top: number;
    onRight: boolean;
    width: number;
    height: number;
  };
}

const Tooltip: React.FC<TooltipProps> = ({
  content,
  position,
  onClose,
  eventElementId,
  courseId,
  arrowPosition,
}) => {
  const tooltipRef = useRef<HTMLDivElement>(null);
  const [tooltipWidth, setTooltipWidth] = useState<number | undefined>(
    undefined,
  );
  const [tooltipHeight, setTooltipHeight] = useState<number | undefined>(
    undefined,
  );

  const t = useTranslations();

  useEffect(() => {
    if (tooltipRef.current) {
      setTooltipWidth(tooltipRef.current.clientWidth);
      setTooltipHeight(tooltipRef.current.clientHeight);
    }
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        tooltipRef.current &&
        !tooltipRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  const tooltipStyle: React.CSSProperties = useMemo(() => {
    if (tooltipWidth === undefined) {
      return {};
    }

    return {
      top: size(position.top - 60),
      left:
        position.left === 0 ? size(-tooltipWidth - 20) : size(position.left),
    };
  }, [position, tooltipWidth]);

  const arrowStyle: React.CSSProperties = useMemo(() => {
    if (
      !arrowPosition ||
      tooltipWidth === undefined ||
      tooltipHeight === undefined
    ) {
      return {};
    }

    let leftValue;
    if (arrowPosition.onRight) {
      leftValue = size(tooltipWidth - 8);
    } else {
      leftValue = size(-8);
    }

    return {
      top: position.top > 0 ? size(30) : size(arrowPosition.top + 60),
      left: leftValue,
    };
  }, [arrowPosition, position, tooltipWidth, tooltipHeight]);

  const getCurator = useCallback(() => {
    const curatorLogo = govAcademy;
    const curatorName = t(dictionary.govAcademy);
    return { curatorLogo, curatorName };
  }, [t]);

  if (!content || !position) {
    return null;
  }

  const portalRoot = eventElementId
    ? document.getElementById(eventElementId) || document.body
    : document.body;

  return ReactDOM.createPortal(
    <div ref={tooltipRef} className={styles.eventTooltip} style={tooltipStyle}>
      <div className={styles.tooltipWrapper}>
        {arrowPosition && tooltipWidth !== undefined && (
          <div className={styles.arrow} style={arrowStyle}></div>
        )}
        <div className={styles.banner}>
          <img src={content?.image} alt={content.coursename} />
        </div>
        <div className={styles.header}>
          <h4 className={styles.courseTitle + ' mb-0'}>{content.coursename}</h4>

          <div className={styles.basicInfo}>
            <span className={styles.date}>
              <CalendarTimeSlot />
              {moment(content.startDateTime).format('D MMMM,YYYY')}
            </span>
            <span className={styles.time}>
              <ClockFrame />
              {content.startTime} - {content.endTime}
            </span>

            {content.eventlocation?.address && (
              <span className={styles.location}>
                <Pin />
                {content.eventlocation.address}
              </span>
            )}

            <div className={styles.curator}>
              <i>
                <Image
                  src={getCurator().curatorLogo}
                  alt={getCurator().curatorName}
                  className="img-fluid"
                />
              </i>
              <span className={styles.curatorName}>
                {getCurator().curatorName}
              </span>
            </div>
          </div>
        </div>

        <button className={styles.viewDetailsButton}>
          <Link
            href={CONFIG.routes.course.replace('{id}', courseId as string)}
            target="_blank"
          >
            {t(dictionary.viewDetails)}
          </Link>
        </button>
      </div>
    </div>,
    portalRoot,
  );
};

export default Tooltip;
