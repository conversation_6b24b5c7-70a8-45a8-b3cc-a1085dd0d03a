import React, { ChangeEvent, useCallback, useMemo, useState } from 'react';
import Typography from '@/ui/typography';
import dictionary from '@/dictionaries';
import { Reset } from '@/ui/icons';

//import styles
import styles from './style.module.scss';
import Select from '@/ui/form/select';
import CoachLevelFilter from './coach-level';
import StarRatingFilter from './star-rating';
import TypeOfCoachingFilter from './type-of-coaching';
import Button from '@/ui/form/button';
import useCoachesCategoriesStore from '@/stores/learning/coaches/categories';
import DatePicker from '@/ui/form/date-picker';
import { format } from '@/utils/date';
import useCoachesStore from '@/stores/learning/coaches/list';

interface IFilterData {
  industry: string;
  skills: string;
  coachLevel: string;
  language: string;
  gender: string;
  dateRange: string;
  startDate: string;
  endDate: string;
  rating: string;
  typeOfCoaching: string;
}

const FilterDetails = () => {
  const onCoachesFetch = useCoachesStore((x) => x.fetch);

  const filterStartData = {
    industry: '',
    skills: '',
    coachLevel: '',
    language: '',
    gender: '',
    dateRange: '',
    startDate: '',
    endDate: '',
    rating: '',
    typeOfCoaching: '',
  };

  const [filterData, setFilterData] = useState<IFilterData>(filterStartData);
  const skillsList = useCoachesCategoriesStore((x) => x.list);
  const languageList = useMemo(() => {
    return ['Arabic', 'English'];
  }, []);
  const dateRangeItems = useMemo(() => {
    return ['Next 7 days', 'Next 30 days', 'Next 6 months', 'Custom'];
  }, []);
  const presentDate = new Date().toISOString().split('T')[0];

  const skillsListFormated = useMemo(() => {
    return skillsList.map((item) => {
      return { id: item, name: item };
    });
  }, [skillsList]);

  const languageListFormated = useMemo(() => {
    return languageList.map((item) => {
      return { id: item, name: item };
    });
  }, [languageList]);

  const dateRangeItemsFormated = useMemo(() => {
    return dateRangeItems.map((item) => {
      return { id: item, name: item };
    });
  }, [dateRangeItems]);

  const onReset = () => {
    setFilterData(filterStartData);
  };

  const handleChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    setFilterData((prev) => {
      return {
        ...prev,
        [e.target.name]:
          e.target.type === 'checkbox' ? e.target.checked : e.target.value,
      };
    });
  }, []);

  const handleSelectChange = useCallback(
    (id: string, name: string | undefined, fieldName?: string) => {
      console.log('handle select change: ', id, name, fieldName);
      setFilterData((prev) => {
        return {
          ...prev,
          [fieldName as string]: name,
        };
      });
    },
    [],
  );

  const applyFilter = () => {
    console.log(filterData);

    onCoachesFetch({ category: filterData.skills });
  };

  // flag to enable filter button
  const enableFilterButton = useMemo(() => {
    for (const [, value] of Object.entries(filterData)) {
      if (value) return true;
    }
    return false;
  }, [filterData]);

  return (
    <>
      <div className={`dialog-heading ${styles.filterHeading}`}>
        <Typography as="h2" dictionary={dictionary.Filter} />

        <button type="button" className={styles.resetBtn} onClick={onReset}>
          <i>
            <Reset />
          </i>
          <Typography as="span" dictionary={dictionary.resetAll} />
        </button>
      </div>
      <hr className={styles.sep} />

      <div className="dialog-content">
        <div className="dialog-form-wrapper">
          <Select
            label={dictionary.industry}
            labelStyle2={true}
            name="industry"
            placeholder={'select industry'}
            selectedItemId={filterData.industry}
            onSelectChange={handleSelectChange}
          />
          <hr className={styles.sep} />

          <Select
            label={dictionary.skills}
            labelStyle2={true}
            name="skills"
            placeholder={'select skills'}
            selectedItemId={filterData.skills}
            items={skillsListFormated}
            onSelectChange={handleSelectChange}
          />
          <hr className={styles.sep} />

          <CoachLevelFilter
            selected={filterData.coachLevel}
            fieldName={'coachLevel'}
            onSelectChange={handleSelectChange}
          />
          <hr className={styles.sep} />

          <Select
            label={dictionary.selectLanguage}
            labelStyle2={true}
            name="language"
            placeholder={dictionary.selectLanguage}
            selectedItemId={filterData.language}
            items={languageListFormated}
            onSelectChange={handleSelectChange}
          />
          <hr className={styles.sep} />

          <div className={styles.genderWrapper}>
            <Typography as="h3" dictionary={dictionary.gender} />
            <div className={styles.gender}>
              <input
                type="radio"
                id="male"
                name="gender"
                value="male"
                onChange={handleChange}
              />
              <Typography as="label" dictionary={dictionary.male} />
              <input
                type="radio"
                id="female"
                name="gender"
                value="female"
                onChange={handleChange}
              />
              <Typography as="label" dictionary={dictionary.female} />
            </div>
          </div>
          <hr className={styles.sep} />

          <Select
            label={dictionary.date}
            labelStyle2={true}
            name="dateRange"
            placeholder={'select range'}
            selectedItemId={filterData.dateRange}
            items={dateRangeItemsFormated}
            onSelectChange={handleSelectChange}
          />
          <hr className={styles.sep} />

          {filterData.dateRange === 'Custom' && (
            <div className={styles.customDates}>
              <DatePicker
                label={dictionary.startDate}
                name="startDate"
                presentationFormat="DD MMM, YYYY"
                value={format(
                  filterData.startDate as string,
                  'en',
                  'YYYY-MM-DD HH:mm:ss',
                  'YYYY-MM-DD',
                )}
                onChange={handleChange}
                min={presentDate}
                placeholder={dictionary.datePlaceholder}
              />
              <DatePicker
                label={dictionary.endDate}
                name="endDate"
                presentationFormat="DD MMM, YYYY"
                value={format(
                  filterData.endDate as string,
                  'en',
                  'YYYY-MM-DD HH:mm:ss',
                  'YYYY-MM-DD',
                )}
                onChange={handleChange}
                placeholder={dictionary.datePlaceholder}
              />
            </div>
          )}

          <StarRatingFilter
            selected={filterData.rating}
            fieldName={'rating'}
            onSelectChange={handleSelectChange}
          />
          <hr className={styles.sep} />

          <TypeOfCoachingFilter
            selected={filterData.typeOfCoaching}
            fieldName={'typeOfCoaching'}
            onSelectChange={handleSelectChange}
          />
          <hr className={styles.sep} />

          <div className={styles.applyFilterButton}>
            <Button
              type="button"
              onClick={applyFilter}
              disabled={!enableFilterButton}
              color="black"
            >
              <Typography as="span" dictionary={dictionary.applyFilters} />
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};

export default FilterDetails;
