import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraRateCoachService } from '@/services/totara/coaches/rate-coach.ts';

/**
 * Route to get the coach rate
 */
export async function POST(request: NextRequest) {
  try {
    // Getting request params
    const data = await request.json();

    const response = await TotaraRateCoachService(data.coachId, data.rate);
    // Return the response
    return NextResponse.json(response.data);
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
