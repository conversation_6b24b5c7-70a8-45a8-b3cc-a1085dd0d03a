import Image from 'next/image';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';
import Paper from '@/ui/paper';
import Typography from '@/ui/typography';
import useAuthStore from '@/stores/auth';
import { motion } from 'motion/react';

import icon from '@public/images/icon-5.png';
import iconDark from '@public/images/icon-5-dark.png';

// Styles
import styles from './style.module.scss';
import dictionary from '@/dictionaries';
import { useLocale, useTranslations } from 'next-intl';
import { getLocalizedValue, usernameFormatter } from '@/utils/helpers';

const LongGreetingPanel = () => {
  // Getting auth user
  const isLoading = useAuthStore((x) => x.isFetching);
  // Getting auth user
  const user = useAuthStore((x) => x.user);
  const t = useTranslations();
  const locale = useLocale();
  return (
    <WithLoader
      loader={<Loading className={styles.greetingLoading} />}
      loading={isLoading}
    >
      <Paper className={styles.greatingPannel}>
        <motion.div
          initial={{
            opacity: 0,
            position: 'relative',
          }}
          animate={{ opacity: 1 }}
          transition={{
            ease: 'easeInOut',
            duration: 0.6,
          }}
          className={styles.box}
        >
          <div className={styles.icon}>
            <Image src={icon} alt="" className="img-fluid lightMode" />
            <Image src={iconDark} alt="" className="img-fluid darkMode" />
          </div>
          <Typography as="h1">
            {t(dictionary.hi)}{' '}
            <strong>
              {usernameFormatter(getLocalizedValue(user, 'firstName', locale))}
            </strong>
            ,
          </Typography>
          <Typography
            as="p"
            dictionary={dictionary.thisIsTheSpaceWhereYouCanLearn}
          />
        </motion.div>
      </Paper>
    </WithLoader>
  );
};

export default LongGreetingPanel;
