import CONFIG from '@/config';
import serverFetcher from '@/utils/fetcher/server-fetcher';
import type { SupportCenterResponse } from '@/utils/fetcher/server-fetcher/interceptors/support-center.interceptor';

export interface FaqCategory {
  id: number;
  name: string;
  description: string;
}

export interface FaqItem {
  id: number;
  question: string;
  answer: string;
}

export const getFaqCategories = async (search?: string) => {
  const fetcher = await serverFetcher.supportCenter({ withBearer: true });
  return fetcher.request<SupportCenterResponse<FaqCategory[]>>({
    url: CONFIG.endpoints.external.supportCenter.faqCategories,
    method: 'GET',
    params: search ? { search } : undefined,
  });
};

export const getFaqItems = async (
  params: { categoryId?: number; search?: string } = {},
) => {
  const fetcher = await serverFetcher.supportCenter({ withBearer: true });
  const query: Record<string, string | number> = {};
  if (typeof params.categoryId === 'number')
    query['category_id'] = params.categoryId;
  if (params.search) query['search'] = params.search;

  return fetcher.request<SupportCenterResponse<FaqItem[]>>({
    url: CONFIG.endpoints.external.supportCenter.faqItems,
    method: 'GET',
    params: query,
  });
};

export const getFaqItemById = async (id: number) => {
  const fetcher = await serverFetcher.supportCenter({ withBearer: true });
  return fetcher.request<SupportCenterResponse<FaqItem>>({
    url: CONFIG.endpoints.external.supportCenter.getFaqItem(id),
    method: 'GET',
  });
};
