import {
  FC,
  useCallback,
  ChangeEvent,
  KeyboardEvent,
  MouseEvent,
  useEffect,
} from 'react';
import InputField from '@/ui/form/input-field';
import { Close, SearchIcon } from '@/ui/icons';
import useSearchCoursesStore from '@/stores/learning/courses/search';
import useSearchCourses from '@/hooks/helpers/search/course/useSearchCourses';

// Styles
import styles from './style.module.scss';
import { useTranslations } from 'next-intl';
import dictionaries from '@/dictionaries';
import { useSearchParams } from 'next/navigation';

interface IField {
  closePopup?: () => void;
}

/**
 * React component to handle search field
 */
const Field: FC<IField> = (props) => {
  const { closePopup } = props;

  // Getting methods from custom hook to handle search courses
  const { updateQueryParam, removeQueryParam } = useSearchCourses();

  // Getting search value
  const search = useSearchCoursesStore((x) => x.filters?.keyword) || '';

  // Getting update filter method
  const setFilter = useSearchCoursesStore((x) => x.setFilter);
  const t = useTranslations();
  const searchParams = useSearchParams();

  /**
   * Effect to handle search value change on initial load
   */
  useEffect(() => {
    if (!searchParams.size) {
      setFilter({
        key: 'keyword',
        value: '',
      });
      removeQueryParam('search');
    }
  }, [searchParams]); //eslint-disable-line

  /**
   * Method to handle change input field
   */
  const onChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      // setting keyword filter
      setFilter({
        key: 'keyword',
        value: e.currentTarget.value,
      });

      // Checking search value is empty
      if (e.currentTarget.value === '') {
        // Removing query param from route
        removeQueryParam('search');
      }
    },
    [removeQueryParam, setFilter],
  );

  /**
   * Method to handle enter pressed
   */
  const onEnter = useCallback(
    (e: KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter') {
        // Updating query param in route
        updateQueryParam('search', search);

        // Closing popup
        if (closePopup) {
          closePopup();
        }
      }
    },
    [updateQueryParam, search, closePopup],
  );

  /**
   * Method to clear input field
   */
  const clear = useCallback(
    (e: MouseEvent<HTMLButtonElement>) => {
      // Stop propogation to prevent parent click
      e.stopPropagation();

      // Clear search value
      setFilter({
        key: 'keyword',
        value: '',
      });

      // Remove search query param
      removeQueryParam('search');
    },
    [removeQueryParam, setFilter],
  );

  // Return JSX
  return (
    <div className={styles.search}>
      <InputField
        placeholder={t(dictionaries.WhatDoYouWantToLearn)}
        ariaLabel="search"
        endIcon=""
        className={styles.input}
        value={search}
        focus={true}
        onChange={onChange}
        onKeyDown={onEnter}
      />
      <button type="button" className={styles.searchBtn} onClick={clear}>
        <i>{search && search?.length > 0 ? <Close /> : <SearchIcon />}</i>
      </button>
    </div>
  );
};

// Return JSX
export default Field;
