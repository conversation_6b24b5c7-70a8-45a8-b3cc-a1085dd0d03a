import { CACHE_TTLS } from '@/config/cache';
import { getFaq } from '@/services/internal/support-center/faq';
import { buildCacheKey } from '@/utils/cache/keys';
import { clientCache } from '@/utils/cache/memory';
import { ThrowError } from '@/utils/fetcher';
import { create } from 'zustand';

export interface FaqQuestionProp {
  ar_SA: string;
  en_US: string;
}
export interface FaqQuestion {
  answer: FaqQuestionProp;
  category: FaqQuestionProp;
  id: string;
  question: FaqQuestionProp;
}

interface SupportCenterFaqStore {
  isFetching: boolean;
  faqQuestions: FaqQuestion[] | [];
  getQuestions: (searchQuery: string) => Promise<void>;
}

const getClientLocale = () => {
  if (typeof document !== 'undefined') {
    const lang = document.documentElement.getAttribute('lang') || 'en';
    return lang.startsWith('ar') ? 'ar' : 'en';
  }
  return 'en';
};

const useSupportCenterFaqStore = create<SupportCenterFaqStore>((set) => ({
  isFetching: false,
  faqQuestions: [],
  getQuestions: async (searchQuery: string) => {
    try {
      const locale = getClientLocale();
      const key = buildCacheKey('faq', { locale, search: searchQuery || '' });
      const hit = clientCache.get<{ items: FaqQuestion[] }>(key);
      if (hit) {
        set({ faqQuestions: hit.items, isFetching: false });
        return;
      }

      set({ isFetching: true });
      const response = await getFaq(searchQuery);
      const items = (response?.data.faqQuestions.items || []) as FaqQuestion[];

      set({ isFetching: false, faqQuestions: items });

      clientCache.set(key, { items }, { ttlMs: CACHE_TTLS.faq });

      return response?.data || null;
    } catch (error) {
      set({ isFetching: false });
      throw ThrowError(error);
    }
  },
}));

export { useSupportCenterFaqStore };
