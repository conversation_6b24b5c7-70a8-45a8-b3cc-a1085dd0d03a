import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import IGenerateOTP from '@/types/domain/external/one-time-password/generate';
import { ExternalGenerateOTPService } from '@/services/external/one-time-password';

// To handle a POST request to /api
export async function POST(request: NextRequest) {
  try {
    // Getting request body
    const data: IGenerateOTP = await request.json();
    // Generating OTP
    const response = await ExternalGenerateOTPService(data);
    // Return the response
    return NextResponse.json({
      status: response?.data?.status,
      message: response?.data?.description,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error);
  }
}
