import { NextRequest, NextResponse } from 'next/server';
import { ExternalGenerativeService } from '@/services/external/ai/generative';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';

// Server side api for generative AI.
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    const response = await ExternalGenerativeService(data);

    return NextResponse.json({
      data: response.data,
    });
  } catch (error) {
    return ErrorResponse(error as AxiosError);
  }
}
