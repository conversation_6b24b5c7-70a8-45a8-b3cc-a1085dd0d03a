import React, { FC } from 'react';
import styles from './style.module.scss';
import IFormatedEvent from '@/types/domain/calendar/formated-event';
import moment from 'moment';
import EmptyMessage from '@/ui/empty-message';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';
import 'moment/locale/ar';
import { CalendarTimeSlot } from '@/ui/icons';

interface IAgendaItems {
  events?: IFormatedEvent[];
  handleShowEventDetails: (event: IFormatedEvent) => void;
}

const AgendaItems: FC<IAgendaItems> = ({ events, handleShowEventDetails }) => {
  const t = useTranslations();

  const cats = [
    ...new Set(events?.map((obj) => obj.startDateTime?.split('T')[0])),
  ];
  const groupedEvents = cats.map((cat) =>
    events?.filter((obj) => obj.startDateTime?.split('T')[0] === cat),
  );

  // const localizedDays = useLocalizedDays();
  return (
    <div className={styles.agendaItemsWrapper}>
      <div className={styles.title}>{t('upcomingEvents')}</div>
      {events?.length ? (
        groupedEvents.map((group, i) => (
          <div key={i} className={styles.groupWrapper}>
            {group?.map((event, j) => (
              <div
                key={`${event.id + '-' + j}`}
                className={styles.eventWrapper}
                onMouseDown={() => {
                  handleShowEventDetails(event);
                }}
              >
                <div className={styles.eventImage}>
                  {event.image ? (
                    <img src={event.image} alt={event.text} />
                  ) : (
                    ''
                  )}
                </div>
                <div className={styles.eventInfo}>
                  {/*<div className={styles.dateRange}>*/}
                  {/*  <span className={styles.dateRangeValue}>*/}
                  {/*    {event.startTime} -*/}
                  {/*  </span>*/}
                  {/*  <span className={styles.dateRangeValue}>*/}
                  {/*    {event.endTime}*/}
                  {/*  </span>*/}
                  {/*</div>*/}
                  <div className={styles.eventName}>{event.text}</div>
                  <div className={styles.eventDate}>
                    <CalendarTimeSlot />
                    <div className={styles.date}>
                      {' '}
                      {moment(event.startDateTime).format('D MMMM,YYYY')}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ))
      ) : (
        <EmptyMessage
          icon="calendar"
          title={dictionary.noUpcomingLearningEvents}
          description={dictionary.noUpcomingLearningEventsDesc}
        />
      )}
    </div>
  );
};

export default AgendaItems;
