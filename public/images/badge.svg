<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 34.8 33.3">
  <defs>
    <style>
      .cls-1 {
        mask: url(#mask);
      }

      .cls-2 {
        stroke: #fff;
        stroke-linejoin: round;
        stroke-opacity: .8;
      }

      .cls-2, .cls-3, .cls-4 {
        fill: none;
      }

      .cls-3 {
        opacity: .9;
        stroke: url(#linear-gradient-4);
        stroke-width: .2px;
      }

      .cls-3, .cls-5, .cls-6 {
        isolation: isolate;
      }

      .cls-7 {
        fill: #d9d9d9;
      }

      .cls-8 {
        fill: url(#radial-gradient);
      }

      .cls-9 {
        fill: url(#linear-gradient);
      }

      .cls-4 {
        stroke: url(#linear-gradient-3);
        stroke-width: 1.8px;
      }

      .cls-10 {
        fill: url(#radial-gradient-2);
        fill-opacity: .3;
      }

      .cls-6 {
        fill: url(#linear-gradient-2);
        fill-opacity: .2;
        mix-blend-mode: multiply;
      }

      .cls-11 {
        fill: url(#radial-gradient-3);
        fill-rule: evenodd;
      }
    </style>
    <linearGradient id="linear-gradient" x1="10.9" y1="36.6" x2="23.6" y2="6.8" gradientTransform="translate(0 36.7) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fdf0d5"/>
      <stop offset="1" stop-color="#f8d169"/>
    </linearGradient>
    <radialGradient id="radial-gradient" cx="-258.5" cy="452.8" fx="-258.5" fy="452.8" r="1" gradientTransform="translate(-9103.2 4882.5) rotate(90) scale(18.8 -20.1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fecc2b"/>
      <stop offset="1" stop-color="#f1a420"/>
    </radialGradient>
    <radialGradient id="radial-gradient-2" cx="-284.6" cy="369.5" fx="-284.6" fy="369.5" r="1" gradientTransform="translate(4438.5 -596.9) rotate(-42.3) scale(12.9 -6.8)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </radialGradient>
    <linearGradient id="linear-gradient-2" x1="17.4" y1="31.2" x2="17.4" y2="7" gradientTransform="translate(0 36.7) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#765913" stop-opacity="0"/>
      <stop offset="1" stop-color="#816011"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="17.4" y1="31.2" x2="17.4" y2="7" gradientTransform="translate(0 36.7) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#f6cd62"/>
      <stop offset="1" stop-color="#ffe9b8"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="17.4" y1="33.4" x2="17.4" y2="6.3" gradientTransform="translate(0 36.7) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fceac0"/>
      <stop offset="1" stop-color="#ffe296"/>
    </linearGradient>
    <mask id="mask" x="5.4" y="5.8" width="24.1" height="22.9" maskUnits="userSpaceOnUse">
      <g id="mask0_837_508">
        <path class="cls-7" d="M16.7,6c.4-.3.9-.3,1.3,0l10.9,8c.4.3.5.8.4,1.2l-4.2,12.6c-.2.5-.6.8-1.1.8h-13.5c-.5,0-.9-.3-1.1-.8l-4.2-12.6c-.2-.5,0-1,.4-1.2l10.9-8Z"/>
      </g>
    </mask>
    <radialGradient id="radial-gradient-3" cx="-275.1" cy="449.4" fx="-275.1" fy="449.4" r="1" gradientTransform="translate(-18827.6 -10782.4) rotate(134.7) scale(20.3 -46.7)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#f9dda1"/>
      <stop offset="1" stop-color="#be7e1d"/>
    </radialGradient>
  </defs>
  <!-- Generator: Adobe Illustrator 28.7.2, SVG Export Plug-In . SVG Version: 1.2.0 Build 154)  -->
  <g class="cls-5">
    <g id="Layer_1">
      <path class="cls-9" d="M16.1.4c.8-.6,1.8-.6,2.6,0l15.2,11c.8.6,1.1,1.6.8,2.5l-5.8,17.8c-.3.9-1.2,1.5-2.1,1.5H8c-1,0-1.8-.6-2.1-1.5L.1,13.9c-.3-.9,0-1.9.8-2.5L16.1.4Z"/>
      <g>
        <path class="cls-8" d="M16.7,6c.4-.3.9-.3,1.3,0l10.9,8c.4.3.5.8.4,1.2l-4.2,12.6c-.2.5-.6.8-1.1.8h-13.5c-.5,0-.9-.3-1.1-.8l-4.2-12.6c-.2-.5,0-1,.4-1.2l10.9-8Z"/>
        <path class="cls-10" d="M16.7,6c.4-.3.9-.3,1.3,0l10.9,8c.4.3.5.8.4,1.2l-4.2,12.6c-.2.5-.6.8-1.1.8h-13.5c-.5,0-.9-.3-1.1-.8l-4.2-12.6c-.2-.5,0-1,.4-1.2l10.9-8Z"/>
        <path class="cls-6" d="M16.7,6c.4-.3.9-.3,1.3,0l10.9,8c.4.3.5.8.4,1.2l-4.2,12.6c-.2.5-.6.8-1.1.8h-13.5c-.5,0-.9-.3-1.1-.8l-4.2-12.6c-.2-.5,0-1,.4-1.2l10.9-8Z"/>
      </g>
      <path class="cls-4" d="M18.6,5.3c-.7-.5-1.7-.5-2.4,0L5.3,13.3c-.7.5-1,1.4-.7,2.2l4.2,12.6c.3.8,1,1.4,1.9,1.4h13.5c.9,0,1.6-.6,1.9-1.4l4.2-12.6c.3-.8,0-1.7-.7-2.2l-10.9-8Z"/>
      <path class="cls-3" d="M19.2,4.4c-1.1-.8-2.5-.8-3.6,0L4.8,12.5c-1,.8-1.5,2.1-1.1,3.3l4.1,12.6c.4,1.2,1.6,2.1,2.9,2.1h13.4c1.3,0,2.4-.8,2.9-2.1l4.1-12.6c.4-1.2,0-2.6-1.1-3.3l-10.8-8Z"/>
      <g class="cls-1">
        <g>
          <path class="cls-11" d="M17.1,12.7l-3.8,2.3,1.5,1.1,2.4-1.5c.2-.1.4-.1.6,0l2.4,1.5,1.6-1-3.9-2.4c-.1,0-.2,0-.4,0s-.3,0-.4,0ZM22.2,16.2l-1.5.9v2.4c0,.2-.1.4-.3.5l-2.4,1.5v1.9l3.8-2.5s0,0,0,0c.2-.1.4-.4.4-.7v-4.1ZM16.8,23.5v-1.9l-2.4-1.5c-.2-.1-.3-.3-.3-.5v-2.4l-1.5-1.1v4.3h0c0,.1,0,.3.1.4,0,.1.2.2.3.3,0,0,0,0,0,0l3.8,2.5ZM12.4,14.1l4-2.5s0,0,0,0c.3-.2.6-.3,1-.3s.7,0,1,.3c0,0,0,0,0,0l4.1,2.5s0,0,.1,0c.3.2.5.4.6.7.2.3.2.6.2.9v4.5c0,.7-.4,1.4-1,1.7l-4,2.6s0,0,0,0c-.3.2-.6.2-.9.3,0,0,0,0,0,0s0,0,0,0c-.3,0-.6,0-.9-.3,0,0,0,0,0,0l-4-2.6c-.3-.2-.5-.4-.7-.7-.2-.3-.3-.7-.3-1v-4.5c0-.7.4-1.4,1-1.7ZM15.3,17.2v1.4l1.5-.8v-1.6l-1.5.9ZM18,16.2v1.6l1.5.8v-1.4l-1.5-.9ZM18.9,19.6l-1.5-.7-1.5.7,1.5.9,1.5-.9Z" shape-rendering="crispEdges"/>
          <path class="cls-2" d="M16.3,22.6v-.7l-2.2-1.4c-.3-.2-.5-.6-.5-.9v-2.2l-.5-.4v3.3l3.2,2.2ZM16.3,22.6l-3-2h0s0,0,0,0c0,0,0,0,0,0,0,0,0,0,0-.1l3.2,2.2ZM18.5,22.6v-.7l2.2-1.4c.3-.2.5-.6.5-.9v-2.1l.5-.3v3.2c0,0,0,.2-.1.2h0s0,0,0,0h0s-3,2-3,2ZM16.2,11.2h0l-4,2.5c-.8.4-1.2,1.3-1.2,2.2v4.5h0c0,.4.1.9.3,1.3.2.4.5.7.9.9l4,2.6h0s0,0,0,0c.4.2.8.3,1.2.3,0,0,0,0,0,0s0,0,0,0c.4,0,.8-.1,1.2-.3,0,0,0,0,0,0h0l4-2.6c.8-.4,1.2-1.3,1.2-2.2v-4.5c0-.4,0-.8-.3-1.2-.2-.3-.4-.6-.8-.9,0,0-.1,0-.2-.1l-4.1-2.5h0s0,0,0,0c-.4-.2-.8-.3-1.2-.3s-.8.1-1.2.3h0s0,0,0,0ZM14.8,15.5l-.6-.4,3.1-1.9s0,0,.1,0,0,0,.1,0l3.2,1.9-.6.4-2.1-1.3c-.4-.2-.8-.2-1.2,0l-2.1,1.3ZM16.3,17.1v.4l-.5.2v-.3l.5-.3ZM19,17.7l-.5-.2v-.4l.5.3v.3ZM17,19.7l.5-.2.5.2-.5.3-.5-.3Z" shape-rendering="crispEdges"/>
        </g>
      </g>
    </g>
  </g>
</svg>