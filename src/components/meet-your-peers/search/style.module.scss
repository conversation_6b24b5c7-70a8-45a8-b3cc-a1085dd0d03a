@use 'mixins' as *;

.input {
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
}

.searchWrapper {
  width: 100%;
  @include forMarginPadding(margin, size(0), size(10), size(0), size(0));
}

.searchBtn {
  position: absolute;
  @include rightToLeft(size(10));
  @include forMarginPadding(margin, auto, auto, auto, auto);
  top: 0;
  bottom: 0;
  width: size(40);
  height: size(40);
  background: none;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  @include borderRadius(50%);
  cursor: pointer;

  &:before {
    position: absolute;
    top: 0;
    bottom: 0;
    @include rightToLeft(0);
    @include leftToRight(0);
    width: 100%;
    height: 100%;
    @include transitions(0.5s);
    background: var(--grey-900);
    @include scale(0);
    @include borderRadius(50%);
    content: '';
  }

  i {
    width: size(20);
    height: size(20);
    position: relative;
    z-index: 2;

    svg {
      fill: var(--grey-900);
      fill-rule: evenodd;
      width: 100%;
      height: 100%;
      @include transitions(0.5s);
    }
  }
}

@include hover() {
  .searchBtn {
    &:hover {
      &:before {
        @include scale(1);
      }

      i {
        svg {
          fill: var(--white);
        }
      }
    }
  }
}

@include for-dark-theme() {
  .search {
    background: linear-gradient(
      18deg,
      rgb(30, 30, 30) 0%,
      rgb(30, 30, 30) 50%,
      rgb(32, 36, 41) 82%,
      rgb(39 48 47) 88%,
      rgb(39 46 45) 94%,
      rgb(45, 55, 52) 100%
    );
  }
}

@include for-all-phone() {
  .search {
    @include forMarginPadding(padding, size(20), size(24), size(20), size(24));
    min-height: initial;
    position: relative;
    @include leftToRight(size(-10));
    width: calc(100% + size(20));
    -webkit-border-radius: 0;
    -webkit-border-bottom-right-radius: var(--radius);
    -webkit-border-bottom-left-radius: var(--radius);
    -moz-border-radius: 0;
    -moz-border-radius-bottomright: var(--radius);
    -moz-border-radius-bottomleft: var(--radius);
    border-radius: 0;
    border-bottom-right-radius: var(--radius);
    border-bottom-left-radius: var(--radius);

    h2 {
      font-size: size(28);
    }
  }
}
