@use 'mixins' as *;

.entryWrapper {
  align-items: center;
  display: flex;
  flex-direction: column;
}

.chatInput {
  border: 0;
  border-radius: size(24) size(24) 0 0;
  box-shadow: size(8) 0 size(10) rgba(0, 0, 0, 0.08);
  font-size: size(16);
  height: size(100);
  padding: size(24);
  width: 100%;

  &:focus {
    outline: none;
  }

  &::placeholder {
    color: var(--grey-700);
  }
}

@include for-dark-theme {
  .chatInput {
    background-color: var(--grey-300);
    border-top: 1px solid var(--grey-400);
  }
}

.promptCta {
  background-color: var(--grey-900);
  border: 0;
  border-radius: size(24);
  cursor: pointer;
  height: size(40);
  right: size(24);
  padding: size(10);
  position: absolute;
  top: size(24);
  transition: 0.2s all ease-in-out;
  width: size(40);

  @include rtl {
    left: size(24);
    right: auto;
  }

  svg {
    fill: var(--white);
    height: size(20);
    width: size(20);

    @include rtl {
      transform: rotate(180deg);
    }
  }

  &:disabled {
    background-color: var(--disabled);
    cursor: not-allowed;
    transition: 0.2s all ease-in-out;
  }
}

@include for-dark-theme {
  .promptCta {
    &:disabled {
      svg {
        fill: var(--grey-500);
      }
    }
  }
}

.suggestionsWrapper {
  display: flex;
  gap: size(12);
  overflow: auto;
  height: size(0);
  transition: 0.5s all cubic-bezier(0.77, 0.23, 0.23, 0.81);
  padding: size(16) 0;
  position: relative;
  width: 100%;
  white-space: nowrap;

  svg[class*='spinner'] {
    bottom: size(50);
    left: 50%;
    position: absolute;
  }

  > div[class*='swiper'] {
    margin-left: 0;
  }
}

.suggestionSlide {
  width: auto;
}

.suggestion {
  align-self: flex-end;
  background-color: var(--white);
  border: 0;
  border-radius: size(12);
  box-shadow: 0 size(4) size(12) rgba(0, 0, 0, 0.07);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  gap: size(12);
  padding: size(20) size(13);
  text-align: left;
  transition: 0.2s all ease-in-out;

  &:hover {
    background-color: var(--cool-grey-400);
    text-decoration: underline;
    transition: 0.2s all ease-in-out;
  }
}

@include for-dark-theme {
  .suggestion {
    background-color: var(--grey-300);

    &:hover {
      background-color: var(--white);
    }
  }
}

.suggestionLabel {
  font-size: size(16);
  font-weight: 700;
}

.suggestionTitle {
  color: var(--grey-700);
  font-size: size(16);
}

.suggestionsExpanded {
  max-width: size(900);
  transition: 0.4s all cubic-bezier(0.77, 0.23, 0.23, 0.81);
  width: size(900);
}

.suggestionsVisible {
  height: size(130);
}

.promptWrapper {
  position: relative;
  transition: 0.5s all cubic-bezier(0.77, 0.23, 0.23, 0.81);
  width: 100%;
}

.promptExpanded {
  max-width: size(900);
  transition: 0.4s all cubic-bezier(0.77, 0.23, 0.23, 0.81);
  width: size(900);
}
