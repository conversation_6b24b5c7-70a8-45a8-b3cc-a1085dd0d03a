import ICourse from '@/types/domain/learning/course';
import styles from './style.module.scss';
import LinearProgress from '@/ui/progress/linear';
import Typography from '@/ui/typography';
import { FC, useMemo } from 'react';

interface IProgress {
  course: ICourse;
  className?: string;
}

const Progress: FC<IProgress> = (props) => {
  // Deconstructing props
  const { course, className } = props;

  // Getting progress status
  const isInprogress = useMemo(() => {
    // Checking if course is in progress
    return course?.isenrolled;
  }, [course?.isenrolled]);

  const hasProgress = !!(course.progress && course.progress !== 0);

  // Return JSX
  return (
    isInprogress &&
    hasProgress && (
      <div className={styles.progressBar + ` ${className}`}>
        <LinearProgress progress={course?.progress || 0} />
        <Typography as="span">{course?.progress}%</Typography>
      </div>
    )
  );
};

export default Progress;
