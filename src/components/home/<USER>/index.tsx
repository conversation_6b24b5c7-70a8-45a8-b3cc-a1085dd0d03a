import { useEffect } from 'react';
import Paper from '@/ui/paper';
import Typography from '@/ui/typography';
import useAuthStore from '@/stores/auth';
import { Bell, CalendarRounded } from '@/ui/icons';
import Media from '@/ui/media';
import Avatar from '@/ui/image-fetcher/avatar';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';
import { Link } from '@/i18n/routing';
import CONFIG from '@/config';
import useLearningNotificationsStore from '@/stores/learning/notifications';
// Styles
import styles from './style.module.scss';

const MobileHeader = () => {
  // Getting auth user
  const user = useAuthStore((x) => x.user);

  const onFetch = useLearningNotificationsStore((x) => x.fetch);
  const unread_notifications = useLearningNotificationsStore(
    (x) => x.unread_count,
  );

  const t = useTranslations();

  useEffect(() => {
    // Fetch the notifications
    onFetch();
  }, [onFetch]);

  // Fetch all notifications every 10 mins
  useEffect(() => {
    const duration = 1000 * 60 * 10;
    const interval = setInterval(() => {
      onFetch();
    }, duration);

    return () => clearInterval(interval);
  }, []); // eslint-disable-line

  return (
    <Media mobile={true} tablet={false} desktop={false}>
      <Paper>
        <div className={styles.box}>
          <div className={styles.icon}>
            <Link href={CONFIG.routes.profile}>
              <Avatar
                src={user?.picture}
                name={user?.fullname}
                className="img-fluid"
              />
            </Link>
          </div>
          <nav className={styles.right}>
            <ul>
              <li>
                <Link
                  href={CONFIG.routes.calendar}
                  aria-label="Calendar"
                  className={styles.calendar}
                >
                  <i className={styles.calendarIcon}>
                    <CalendarRounded />
                  </i>
                </Link>
              </li>
              <li>
                <Link
                  href={CONFIG.routes.notifications.list}
                  aria-label="Notifications"
                  className={styles.calendar}
                >
                  <Typography as="span" className={styles.noti}>
                    {unread_notifications > 9 ? '9+' : unread_notifications}
                  </Typography>
                  <i className={styles.calendarIcon}>
                    <Bell />
                  </i>
                </Link>
              </li>
            </ul>
          </nav>
          <Typography as="h4">
            {t(dictionary.hi)}, <strong>{user?.firstName}</strong>
          </Typography>
          <Typography
            as="p"
            dictionary={dictionary.thisIsTheSpaceWhereYouCanLearn}
          />
        </div>
      </Paper>
    </Media>
  );
};

export default MobileHeader;
