<svg width="1554" height="1142" viewBox="0 0 1554 1142" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M772.5 0.177246V385.5C772.5 425.5 795.1 468.5 865.5 468.5C935.9 468.5 1304.67 468.5 1476 468.5C1510 468.5 1552.5 492.3 1552.5 561.5C1552.5 630.7 1552.5 745 1552.5 793.5C1552.5 830 1544.3 896 1465.5 900C1386.7 900 523 900 101 900C62 900 15 921.3 15 988.5C15 1055.7 15 1083.83 15 1140.5" stroke="#C9C0BB" stroke-width="2"/>
<g filter="url(#filter0_b_1310_135)">
<circle cx="885" cy="468" r="14" fill="url(#paint0_radial_1310_135)" fill-opacity="0.5"/>
<circle cx="885" cy="468" r="14.5" stroke="url(#paint1_radial_1310_135)" stroke-opacity="0.8"/>
</g>
<g filter="url(#filter1_b_1310_135)">
<circle cx="885" cy="468" r="5" fill="#7CA1C3"/>
<circle cx="885" cy="468" r="5.5" stroke="url(#paint2_radial_1310_135)" stroke-opacity="0.8"/>
</g>
<g filter="url(#filter2_b_1310_135)">
<circle cx="225" cy="900" r="14" fill="url(#paint3_radial_1310_135)" fill-opacity="0.5"/>
<circle cx="225" cy="900" r="14.5" stroke="url(#paint4_radial_1310_135)" stroke-opacity="0.8"/>
</g>
<g filter="url(#filter3_b_1310_135)">
<circle cx="225" cy="900" r="5" fill="#4976C5"/>
<circle cx="225" cy="900" r="5.5" stroke="url(#paint5_radial_1310_135)" stroke-opacity="0.8"/>
</g>
<g filter="url(#filter4_b_1310_135)">
<circle cx="885" cy="900" r="14" fill="url(#paint6_radial_1310_135)" fill-opacity="0.5"/>
<circle cx="885" cy="900" r="14.5" stroke="url(#paint7_radial_1310_135)" stroke-opacity="0.8"/>
</g>
<g filter="url(#filter5_b_1310_135)">
<circle cx="885" cy="900" r="5" fill="#4976C5"/>
<circle cx="885" cy="900" r="5.5" stroke="url(#paint8_radial_1310_135)" stroke-opacity="0.8"/>
</g>
<g filter="url(#filter6_b_1310_135)">
<circle cx="15" cy="1127" r="14" fill="url(#paint9_radial_1310_135)" fill-opacity="0.5"/>
<circle cx="15" cy="1127" r="14.5" stroke="url(#paint10_radial_1310_135)" stroke-opacity="0.8"/>
</g>
<g filter="url(#filter7_b_1310_135)">
<circle cx="15" cy="1127" r="5" fill="#4976C5"/>
<circle cx="15" cy="1127" r="5.5" stroke="url(#paint11_radial_1310_135)" stroke-opacity="0.8"/>
</g>
<g filter="url(#filter8_b_1310_135)">
<circle cx="773" cy="84.1772" r="14" fill="url(#paint12_radial_1310_135)" fill-opacity="0.5"/>
<circle cx="773" cy="84.1772" r="14.5" stroke="url(#paint13_radial_1310_135)" stroke-opacity="0.8"/>
</g>
<g filter="url(#filter9_b_1310_135)">
<circle cx="773" cy="84.1772" r="5" fill="#7CA1C3"/>
<circle cx="773" cy="84.1772" r="5.5" stroke="url(#paint14_radial_1310_135)" stroke-opacity="0.8"/>
</g>
<defs>
<filter id="filter0_b_1310_135" x="830" y="413" width="110" height="110" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1310_135"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1310_135" result="shape"/>
</filter>
<filter id="filter1_b_1310_135" x="839" y="422" width="92" height="92" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1310_135"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1310_135" result="shape"/>
</filter>
<filter id="filter2_b_1310_135" x="170" y="845" width="110" height="110" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1310_135"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1310_135" result="shape"/>
</filter>
<filter id="filter3_b_1310_135" x="179" y="854" width="92" height="92" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1310_135"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1310_135" result="shape"/>
</filter>
<filter id="filter4_b_1310_135" x="830" y="845" width="110" height="110" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1310_135"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1310_135" result="shape"/>
</filter>
<filter id="filter5_b_1310_135" x="839" y="854" width="92" height="92" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1310_135"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1310_135" result="shape"/>
</filter>
<filter id="filter6_b_1310_135" x="-40" y="1072" width="110" height="110" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1310_135"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1310_135" result="shape"/>
</filter>
<filter id="filter7_b_1310_135" x="-31" y="1081" width="92" height="92" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1310_135"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1310_135" result="shape"/>
</filter>
<filter id="filter8_b_1310_135" x="718" y="29.1772" width="110" height="110" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1310_135"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1310_135" result="shape"/>
</filter>
<filter id="filter9_b_1310_135" x="727" y="38.1772" width="92" height="92" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1310_135"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1310_135" result="shape"/>
</filter>
<radialGradient id="paint0_radial_1310_135" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(871 454) rotate(45) scale(39.598 27.5142)">
<stop stop-color="#8A8A8A" stop-opacity="0.56"/>
<stop offset="1" stop-color="#DDDDDD" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint1_radial_1310_135" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(871 454) rotate(45) scale(39.598 27.5142)">
<stop stop-color="white" stop-opacity="0.56"/>
<stop offset="1" stop-color="#E9E9E9" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint2_radial_1310_135" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(880 463) rotate(45) scale(14.1421 9.82651)">
<stop stop-color="white" stop-opacity="0.56"/>
<stop offset="1" stop-color="#E9E9E9" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint3_radial_1310_135" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(211 886) rotate(45) scale(39.598 27.5142)">
<stop stop-color="#8A8A8A" stop-opacity="0.56"/>
<stop offset="1" stop-color="#DDDDDD" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint4_radial_1310_135" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(211 886) rotate(45) scale(39.598 27.5142)">
<stop stop-color="white" stop-opacity="0.56"/>
<stop offset="1" stop-color="#E9E9E9" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint5_radial_1310_135" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(220 895) rotate(45) scale(14.1421 9.82651)">
<stop stop-color="white" stop-opacity="0.56"/>
<stop offset="1" stop-color="#E9E9E9" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint6_radial_1310_135" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(871 886) rotate(45) scale(39.598 27.5142)">
<stop stop-color="#8A8A8A" stop-opacity="0.56"/>
<stop offset="1" stop-color="#DDDDDD" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint7_radial_1310_135" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(871 886) rotate(45) scale(39.598 27.5142)">
<stop stop-color="white" stop-opacity="0.56"/>
<stop offset="1" stop-color="#E9E9E9" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint8_radial_1310_135" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(880 895) rotate(45) scale(14.1421 9.82651)">
<stop stop-color="white" stop-opacity="0.56"/>
<stop offset="1" stop-color="#E9E9E9" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint9_radial_1310_135" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1 1113) rotate(45) scale(39.598 27.5142)">
<stop stop-color="#8A8A8A" stop-opacity="0.56"/>
<stop offset="1" stop-color="#DDDDDD" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint10_radial_1310_135" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1 1113) rotate(45) scale(39.598 27.5142)">
<stop stop-color="white" stop-opacity="0.56"/>
<stop offset="1" stop-color="#E9E9E9" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint11_radial_1310_135" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10 1122) rotate(45) scale(14.1421 9.82651)">
<stop stop-color="white" stop-opacity="0.56"/>
<stop offset="1" stop-color="#E9E9E9" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint12_radial_1310_135" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(759 70.1772) rotate(45) scale(39.598 27.5142)">
<stop stop-color="#8A8A8A" stop-opacity="0.56"/>
<stop offset="1" stop-color="#DDDDDD" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint13_radial_1310_135" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(759 70.1772) rotate(45) scale(39.598 27.5142)">
<stop stop-color="white" stop-opacity="0.56"/>
<stop offset="1" stop-color="#E9E9E9" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint14_radial_1310_135" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(768 79.1772) rotate(45) scale(14.1421 9.82651)">
<stop stop-color="white" stop-opacity="0.56"/>
<stop offset="1" stop-color="#E9E9E9" stop-opacity="0.67"/>
</radialGradient>
</defs>
</svg>
