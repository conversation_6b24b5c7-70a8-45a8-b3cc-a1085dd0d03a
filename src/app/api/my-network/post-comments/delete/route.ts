import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraDeletePostCommentService } from '@/services/totara/my-network';

/**
 * Route to delete the Post comment
 */
export async function GET(request: NextRequest) {
  try {
    const commentId = request.nextUrl.searchParams.get('commentId');

    // Fetching booked sessions
    const response = await TotaraDeletePostCommentService(commentId || '');

    // Return the response
    return NextResponse.json({
      success: response.data.success,
      comments: response.data.comments,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
