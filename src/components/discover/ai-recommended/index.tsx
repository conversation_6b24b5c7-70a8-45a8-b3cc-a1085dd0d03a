import { useCallback, useEffect, useState } from 'react';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';
import Paper from '@/ui/paper';
import Grid from '@/ui/grid';
import EmptyMessage from '@/ui/empty-message';
import Course from '@/components/course';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';
import useChatStore from '@/stores/ai/chat';
import { IChatCourseResponse } from '@/types/domain/ai/chatResponse';
import { recommendedCourses } from '@/hooks/ai/chat/constants';
import createAiUserProfile from '@/hooks/ai/profile/useAiUserProfile';
import createChatPayload from '@/hooks/ai/chat/useChatPayload';
import useAuthStore from '@/stores/auth';
import { getRandom10 } from '@/utils/helpers';

const AIRecommendedCoursesList = () => {
  const [AIList, setAIList] = useState<IChatCourseResponse[]>([]);

  const { fetch, isFetching } = useChatStore();
  const { user } = useAuthStore();

  const t = useTranslations();

  const fetchAItopPicks = useCallback(async () => {
    if (user) {
      const promptData = recommendedCourses.prompt + createAiUserProfile(user);
      const payload = createChatPayload(promptData, 50);

      const response = await fetch(payload);
      if (response?.data.success) {
        const randomCourses = getRandom10(
          response.data.data.search_results['gov-academy-courses'],
        );
        setAIList(randomCourses);
      }
    }
  }, [fetch, user]);

  useEffect(() => {
    fetchAItopPicks();
  }, [fetchAItopPicks]);

  return (
    <WithLoader loading={isFetching} loader={<Loading />}>
      <Paper>
        {!isFetching && AIList?.length > 0 ? (
          <Grid xs={1} sm={3} md={4} gap="lg">
            {AIList?.map((item) => {
              const course = {
                id: parseInt(item.CourseURL.split('id=')[1]),
                title: item.Name,
                duration: item.Duration,
                image: item.ImageURL,
                description: item.Summary,
                course_type: item.Type,
              };
              return <Course key={course.id} course={course} />;
            })}
          </Grid>
        ) : (
          <EmptyMessage
            icon="search"
            title={t(dictionary.noSearchResultsFound)}
            description={t(
              dictionary.pleaseCheckThatAllWordsAreSpelledCorrectly,
            )}
          />
        )}
      </Paper>
    </WithLoader>
  );
};

export default AIRecommendedCoursesList;
