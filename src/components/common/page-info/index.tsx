import { FC, useCallback, useMemo } from 'react';
import Paper from '@/ui/paper';
import Typography from '@/ui/typography';
import { useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';
import styles from './style.module.scss';

interface IPageInfo {
  title: string;
  description?: string;
  className?: string;
}

const PageInfo: FC<IPageInfo> = (props) => {
  const { title, description, className } = props;

  // Getting translations
  const t = useTranslations();

  // Check if translation exists
  const hasTranslation = useCallback((key?: string) => {
    // if key is not provided
    if (!key) return false;
    // Check if key exists in dictionary
    return key in dictionary;
  }, []);

  // Translating title
  const _title = useMemo(() => {
    // If translation exists
    if (hasTranslation(title)) {
      return t(title);
    }
    // Return label
    return title;
  }, [title, hasTranslation, t]);

  // Translating link title
  const _description = useMemo(() => {
    // If translation exists
    if (hasTranslation(description)) {
      return t(description);
    }
    // Return label
    return description;
  }, [description, hasTranslation, t]);

  return (
    <Paper className={className}>
      <Typography as="h1" className={styles.pageInfoTitle + ` mt-0 mb-1`}>
        {_title}
      </Typography>
      <Typography as="p" className={styles.pageInfoDescription + ` mb-0`}>
        {_description}
      </Typography>
    </Paper>
  );
};

export default PageInfo;
