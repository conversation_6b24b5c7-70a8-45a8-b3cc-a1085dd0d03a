import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchNewCoursesService } from '@/services/totara/courses';

/**
 * Route to get the courses
 */
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const categoryID = request.nextUrl.searchParams.get('categoryID') || null;
    const page = request.nextUrl.searchParams.get('page') || '1';
    const perPage = request.nextUrl.searchParams.get('perPage') || '10';

    // Fetching records counts
    const response = await TotaraFetchNewCoursesService({
      categoryID: categoryID,
      page: parseInt(page),
      perPage: parseInt(perPage),
    });

    // Return the response
    return NextResponse.json({
      list: response?.data?.records || [],
      total: response?.data?.total || 0,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
