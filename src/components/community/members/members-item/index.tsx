import { ICommunityMemberDetails } from '@/types/domain/my-network/communityDetails';
import Typography from '@/ui/typography';
import { getFullNameInitials } from '@/utils/helpers';
import styles from './style.module.scss';
import { useEffect, useState } from 'react';
import dictionary from '@/dictionaries';

interface IMembersItemProps {
  member: ICommunityMemberDetails;
}
export const avatarColors = [
  '#e9f4f3',
  '#f1eff9',
  '#f0efe9',
  '#ecf7ff',
  '#fef3e7',
  '#fcf0ed',
  '#e9f4f3',
  '#f1eff9',
];

const MembersItem: React.FC<IMembersItemProps> = ({ member }) => {
  const fullNameInitials = getFullNameInitials(member.fullname);

  const [backgroundColor, setBackgroundColor] = useState('transparent');

  useEffect(() => {
    setBackgroundColor(
      avatarColors[Math.floor(Math.random() * avatarColors.length)],
    );
  }, []);

  return (
    <div className={styles.memberItem}>
      <div
        className={styles.avatar}
        style={{ backgroundColor, color: '#1E1E1E' }}
      >
        <Typography>{fullNameInitials}</Typography>
      </div>
      <Typography>{member.fullname}</Typography>
      {member.owner && (
        <Typography
          className={styles.ownerLabel}
          dictionary={dictionary.owner}
        />
      )}
    </div>
  );
};

export default MembersItem;
