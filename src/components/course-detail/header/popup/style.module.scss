@use 'mixins' as *;

.confirmationDialog {
  display: none;
}

.confirmationDialogOpened {
  border: 0;
  border-radius: size(24);
  display: flex;
  flex-direction: column;
  margin: auto;
  min-height: size(262);
  @include forMarginPadding(padding, size(40), size(24), size(32), size(24));
  width: size(430);
}

.confirmationDialogTitle {
  font-size: size(28);
  font-weight: 700;
}

.confirmationDialogMessage {
  font-size: size(18);
}

.confirmationDialogActions {
  display: flex;
  margin-top: auto;

  .confirmationDialogClose {
    width: 100%;
  }
}

.iconWrapper {
  width: size(48);
  height: size(48);
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--success-900);
  margin-bottom: size(12);
  @include borderRadius(50%);

  & svg {
    width: size(36);
    height: size(36);
    fill: var(--white);
  }
}

.slot {
  background-color: var(--bg-shade-4);
  border-radius: size(12);
  padding: size(10);
  @include forMarginPadding(margin, size(16), size(0), size(16), size(0));

  .slotItem {
    align-items: center;
    display: flex;
    gap: size(8);
  }

  .dateIconWrapper {
    align-items: center;
    display: flex;
    height: size(32);
    justify-content: center;
    padding: size(5);
    width: size(32);

    svg {
      fill: var(--grey-900);
      height: size(17);
      width: size(17);
    }
  }
}

@include for-dark-theme() {
  .slot {
    background-color: var(--bg-card-1);
  }
}
