# Totara LMS API Functions Reference (wsfunction values)

## Complete List of All Available Web Service Functions

**Base URL:** `https://adsgstaging.elearning.ae/webservice/rest/server.php`
**Total Functions:** 50+
**Last Updated:** January 30, 2025

---

## 🔐 **Authentication Functions**

| Function                         | Description                               | Common Parameters |
| -------------------------------- | ----------------------------------------- | ----------------- |
| `auth_userkey_request_login_url` | Request login URL for user authentication | User credentials  |

---

## 👤 **User Profile & Onboarding Functions**

| Function                      | Description                              | Common Parameters        |
| ----------------------------- | ---------------------------------------- | ------------------------ |
| `wstotara_get_profile`        | Fetch user profile information           | `userid`                 |
| `wstotara_user_profileupdate` | Update user profile data                 | `userid`, profile fields |
| `wstotara_get_onboardinginfo` | Get onboarding information for new users | User context             |

---

## 🎯 **Interest & Preference Functions**

| Function                               | Description                                   | Common Parameters |
| -------------------------------------- | --------------------------------------------- | ----------------- |
| `wstotara_get_interests`               | Get available interest categories             | None              |
| `wstotara_get_interests_by_categories` | Get interests filtered by specific categories | `categoryid`      |

---

## 📚 **Course Discovery Functions**

| Function                              | Description                                 | Common Parameters                                        |
| ------------------------------------- | ------------------------------------------- | -------------------------------------------------------- |
| `wstotara_get_onboardingcourses`      | Get recommended courses for user onboarding | User preferences                                         |
| `wstotara_course_recommendations`     | Get personalized course suggestions         | User profile                                             |
| `wstotara_search_learningdata`        | **Main search function for courses**        | `keyword`, `category`, `learningtype`, `page`, `perpage` |
| `wstotara_get_coursecategories`       | Get all available course categories         | None                                                     |
| `wstotara_get_top_picks`              | Get top-rated/featured courses              | None                                                     |
| `wstotara_get_recent_courses`         | Get newly added courses                     | None                                                     |
| `wstotara_get_trending_courses`       | Get currently trending courses              | None                                                     |
| `wstotara_get_duration_based_courses` | Get courses filtered by duration            | `courseduration`                                         |

---

## 📖 **Course Management Functions**

| Function                       | Description                                 | Common Parameters    |
| ------------------------------ | ------------------------------------------- | -------------------- |
| `wstotara_course_details`      | **Get detailed course information**         | `courseid`           |
| `wstotara_get_coursedetails`   | Alternative course details function         | `courseid`           |
| `wstotara_start_user_learning` | Start/enroll user in a course               | `courseid`, `userid` |
| `wstotara_save_forlater`       | Save course to user's "save for later" list | `courseid`           |
| `wstotara_save_course_rating`  | Submit course rating and review             | `courseid`, `rating` |

---

## 🔍 **Course Search & Filter Functions**

| Function                         | Description                          | Common Parameters |
| -------------------------------- | ------------------------------------ | ----------------- |
| `wstotara_get_course_filters`    | **Get all available search filters** | None              |
| `wstotara_search_popular_recent` | Get popular and recent search terms  | `type`            |
| `wstotara_clear_recent_searches` | Clear user's recent search history   | `userid`          |

---

## 📈 **Learning Progress Functions**

| Function                              | Description                                           | Common Parameters |
| ------------------------------------- | ----------------------------------------------------- | ----------------- |
| `wstotara_fetch_mylearningdata`       | **Get user's learning progress and enrolled courses** | `userid`, filters |
| `wstotara_get_user_learning_pathways` | Get user's assigned learning pathways                 | `userid`          |
| `wstotara_get_completed_courses`      | Get list of user's completed courses                  | `userid`          |

---

## 🏫 **Physical & Multi-session Course Functions**

| Function                      | Description                                | Common Parameters         |
| ----------------------------- | ------------------------------------------ | ------------------------- |
| `wstotara_check_linemanager`  | Check if line manager approval is required | `courseid`                |
| `wstotara_book_slot`          | Book a slot for physical/scheduled courses | `courseid`, slot details  |
| `wstotara_cancel_booked_slot` | Cancel a previously booked course slot     | `bookingid`               |
| `wstotara_book_a_seminar`     | Book slot for multi-session seminars       | `seminarid`, slot details |

---

## 🎓 **Program Management Functions**

| Function                                          | Description                                  | Common Parameters     |
| ------------------------------------------------- | -------------------------------------------- | --------------------- |
| `wstotara_get_required_and_recommended_learnings` | Get program requirements and recommendations | `programid`           |
| `wstotara_program_details`                        | Get detailed program information             | `programid`           |
| `wstotara_program_enrollment`                     | Enroll user in a learning program            | `programid`, `userid` |

---

## 🏆 **Badges & Certificates Functions**

| Function                                 | Description                                   | Common Parameters |
| ---------------------------------------- | --------------------------------------------- | ----------------- |
| `wstotara_get_available_and_user_badges` | Get available badges and user's earned badges | `userid`          |
| `wstotara_get_users_certificates`        | Get user's earned certificates                | `userid`          |

---

## 👨‍🏫 **Coach Management Functions**

| Function                            | Description                         | Common Parameters |
| ----------------------------------- | ----------------------------------- | ----------------- |
| `wstotara_get_coach_list`           | Get list of available coaches       | Filters           |
| `wstotara_get_coach_detail`         | Get detailed coach information      | `coachid`         |
| `wstotara_get_coach_specialization` | Get available coach specializations | None              |

---

## 📅 **Coach Booking Functions**

| Function                       | Description                         | Common Parameters   |
| ------------------------------ | ----------------------------------- | ------------------- |
| `wstotara_get_coach_slots`     | Get available coaching slots        | `coachid`, `date`   |
| `wstotara_book_coach_slot`     | Book a coaching session             | `coachid`, `slotid` |
| `wstotara_get_booked_sessions` | Get user's booked coaching sessions | `userid`            |
| `wstotara_rate_coach`          | Rate and review a coach             | `coachid`, `rating` |

---

## 🌐 **Social Learning & Community Functions**

| Function                              | Description                            | Common Parameters |
| ------------------------------------- | -------------------------------------- | ----------------- |
| `wstotara_get_my_networks`            | Get user's joined communities/networks | `userid`          |
| `wstotara_get_network_details`        | Get detailed community information     | `networkid`       |
| `wstotara_get_community_library_list` | Get community's shared resources       | `networkid`       |

---

## 🤝 **Community Enrollment Functions**

| Function                            | Description                         | Common Parameters |
| ----------------------------------- | ----------------------------------- | ----------------- |
| `wstotara_network_enrollment`       | Enroll in a community/network       | `networkid`       |
| `wstotara_leave_community`          | Leave a community                   | `networkid`       |
| `wstotara_request_to_join_network`  | Request to join a private community | `networkid`       |
| `wstotara_cancel_requested_network` | Cancel pending join request         | `networkid`       |

---

## 💬 **Posts & Discussion Functions**

| Function                          | Description                    | Common Parameters      |
| --------------------------------- | ------------------------------ | ---------------------- |
| `wstotara_post_discussions`       | Create a new post in community | `networkid`, `content` |
| `wstotara_delete_discussions`     | Delete a community post        | `postid`               |
| `wstotara_upd_discussion_content` | Update/edit a community post   | `postid`, `content`    |

---

## 💭 **Comments & Interaction Functions**

| Function                           | Description                                 | Common Parameters         |
| ---------------------------------- | ------------------------------------------- | ------------------------- |
| `wstotara_get_discussion_comments` | Get comments for a post                     | `discussionid`            |
| `wstotara_add_comments`            | Add comment to a post                       | `discussionid`, `content` |
| `wstotara_delete_comment`          | Delete a comment                            | `commentid`               |
| `wstotara_add_replies`             | Reply to a comment                          | `commentid`, `content`    |
| `wstotara_post_reaction`           | Add reaction (like/dislike) to post/comment | `targetid`, `reaction`    |

---

## 📅 **Calendar & Events Functions**

| Function                       | Description                              | Common Parameters    |
| ------------------------------ | ---------------------------------------- | -------------------- |
| `wstotara_get_upcoming_events` | Get user's upcoming events and deadlines | `userid`, date range |

---

## 🔥 **Streak & Gamification Functions**

| Function                             | Description                 | Common Parameters         |
| ------------------------------------ | --------------------------- | ------------------------- |
| `wstotara_get_user_streaks`          | Get user's learning streaks | `userid`                  |
| `wstotara_get_monthly_streak_record` | Get monthly streak records  | `userid`, `month`, `year` |

---

## 📋 **Personal List Functions**

| Function                              | Description                      | Common Parameters    |
| ------------------------------------- | -------------------------------- | -------------------- |
| `wstotara_get_user_personallist`      | Get user's personal course lists | `userid`             |
| `wstotara_create_personallist`        | Create a new personal list       | `name`               |
| `wstotara_add_courses_inpersonallist` | Add courses to personal list     | `listid`, course IDs |
| `wstotara_delete_personallist`        | Delete a personal list           | `listid`             |
| `wstotara_get_personallist_courses`   | Get courses in a personal list   | `listid`             |

---

## 📢 **Announcement & Notification Functions**

| Function                         | Description                             | Common Parameters |
| -------------------------------- | --------------------------------------- | ----------------- |
| `wstotara_get_forum_discussions` | Get announcements and forum discussions | Forum filters     |
| `wstotara_notification_lists`    | Get user's notifications                | `userid`          |
| `wstotara_read_notification`     | Mark notification as read               | `notificationid`  |

---

## 🎨 **Banner & Promotion Functions**

| Function                     | Description             | Common Parameters |
| ---------------------------- | ----------------------- | ----------------- |
| `wstotara_get_static_banner` | Get promotional banners | `bannertype`      |

---

## 🔧 **Usage Template**

```bash
curl -X POST "https://adsgstaging.elearning.ae/webservice/rest/server.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "wstoken=YOUR_WS_TOKEN" \
  -d "moodlewsrestformat=json" \
  -d "wsfunction=FUNCTION_NAME_FROM_ABOVE" \
  -d "language=en" \
  -d "additional_parameters=values"
```

---

## 📊 **Function Categories Summary**

| Category              | Count | Most Used Functions                                                   |
| --------------------- | ----- | --------------------------------------------------------------------- |
| **Course Management** | 15+   | `wstotara_search_learningdata`, `wstotara_course_details`             |
| **Social Learning**   | 12+   | `wstotara_get_my_networks`, `wstotara_post_discussions`               |
| **User Management**   | 8+    | `wstotara_get_profile`, `wstotara_fetch_mylearningdata`               |
| **Coach System**      | 6+    | `wstotara_get_coach_list`, `wstotara_book_coach_slot`                 |
| **Personal Lists**    | 5+    | `wstotara_create_personallist`, `wstotara_add_courses_inpersonallist` |
| **Gamification**      | 4+    | `wstotara_get_user_streaks`, `wstotara_get_available_and_user_badges` |
| **Programs**          | 3+    | `wstotara_program_details`, `wstotara_program_enrollment`             |

---

## ⭐ **Most Commonly Used Functions**

1. **`wstotara_search_learningdata`** - Main course search function
2. **`wstotara_course_details`** - Get detailed course information
3. **`wstotara_get_onboardingcourses`** - Get recommended courses
4. **`wstotara_fetch_mylearningdata`** - Get user's learning progress
5. **`wstotara_get_profile`** - Get user profile information
6. **`wstotara_get_coursecategories`** - Get course categories
7. **`wstotara_get_trending_courses`** - Get trending courses
8. **`wstotara_start_user_learning`** - Start/enroll in a course

---

## 📝 **Standard Parameters**

### **Required for All Functions:**

- `wstoken` - Web service authentication token
- `moodlewsrestformat` - Response format (always "json")
- `wsfunction` - Function name from the list above

### **Common Optional Parameters:**

- `language` - Response language ("en" or "ar")
- `page` - Page number for pagination
- `perpage` - Items per page (default: 20, max: 100)
- `userid` - User identifier
- `courseid` - Course identifier

This reference provides all available wsfunction values for the Totara LMS API integration.
