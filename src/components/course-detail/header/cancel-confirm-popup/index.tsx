import { FC, useRef, useEffect } from 'react';
import styles from './style.module.scss';
import Typography from '@/ui/typography';
import Button from '@/ui/form/button';
import usePhysicalCourseStore from '@/stores/learning/courses/physical';
import dictionary from '@/dictionaries';
import useMultisessionCourseStore from '@/stores/learning/courses/multisession';

interface ICancelConfirmPopup {
  isOpened: boolean;
  isAssignedSlot?: boolean;
  onCancel: () => void;
  onAccept: () => void;
  isCancelingCohort?: boolean;
}

const CancelConfirmPopup: FC<ICancelConfirmPopup> = ({
  isOpened,
  isAssignedSlot,
  onCancel,
  onAccept,
  isCancelingCohort = false,
}) => {
  const dialogRef = useRef<HTMLDialogElement>(null);

  const isMultisessionCanceling = useMultisessionCourseStore(
    (x) => x.isCanceling,
  );
  const isPhysicalCanceling = usePhysicalCourseStore((x) => x.isCanceling);

  const isCanceling = isCancelingCohort
    ? isMultisessionCanceling
    : isPhysicalCanceling;

  useEffect(() => {
    if (isOpened) {
      dialogRef.current?.showModal();
    } else {
      dialogRef.current?.close();
    }
  }, [isOpened]);

  // Return JSX
  return (
    <dialog
      ref={dialogRef}
      className={
        styles.confirmationDialog +
        ` ${isOpened ? styles.confirmationDialogOpened : ''}`
      }
    >
      <div className={styles.confirmationDialogTop}>
        <Typography
          dictionary={
            isAssignedSlot
              ? dictionary.areYouSureCancelSlot
              : dictionary.areYouSureCancelRequest
          }
          className={styles.confirmationDialogTitle}
        />
        <Typography
          dictionary={
            isAssignedSlot
              ? dictionary.sureWantToProceedSlot
              : dictionary.sureWantToProceed
          }
          className={styles.confirmationDialogMessage}
        />
      </div>
      <div className={styles.confirmationDialogActions}>
        <Button
          dictionary={
            isAssignedSlot ? dictionary.cancelSlot : dictionary.cancelRequest
          }
          className={styles.confirmationDialogCancel}
          onClick={onAccept}
          loading={isCanceling}
        />
        <Button
          dictionary={
            isAssignedSlot ? dictionary.keepSlot : dictionary.keepRequest
          }
          className={styles.confirmationDialogAccept}
          onClick={onCancel}
          loading={isCanceling}
        />
      </div>
    </dialog>
  );
};

export default CancelConfirmPopup;
