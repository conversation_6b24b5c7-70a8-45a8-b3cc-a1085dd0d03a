import Typography from '@/ui/typography';
import Button from '@/ui/form/button';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '../list/loading';
import { useState } from 'react';
import useCoachesStore from '@/stores/learning/coaches/list';
import Item from '../list/item';

import styles from './style.module.scss';
import { RefreshSquared } from '@/ui/icons';
import { coachExperience } from '../mock-data';

const FindPerfectCoachWidget = () => {
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [isFetched, setIsFetched] = useState<boolean>(false);

  const { list, isFetching: isFetchingCoaches } = useCoachesStore();

  const findePerfectCoach = () => {
    setIsFetching(true);

    setTimeout(() => {
      setIsFetching(false);
      setIsFetched(true);
    }, 2000);
  };

  const openCoachDetails = () => {
    console.log('coach details');
  };

  return (
    <div className={styles.findPerfectCoachWrapper}>
      <WithLoader
        loader={<Loading />}
        loading={isFetching || isFetchingCoaches}
      >
        {isFetched ? (
          <div className={styles.matchedCoaches}>
            <div className={styles.header}>
              <div className={styles.titleWrapper}>
                <Typography className={styles.title}>
                  Your Perfect Coaches
                </Typography>
                <Typography as="span" className={styles.subText}>
                  Based on your profile, we think these coaches would be a great
                  match.
                </Typography>
              </div>
              <Button
                type="button"
                color="black"
                className={styles.regerate}
                onClick={findePerfectCoach}
                startIcon={<RefreshSquared />}
              >
                <Typography as="span">Regenerate</Typography>
              </Button>
            </div>
            <div className={styles.coachesList}>
              {list.slice(0, 3)?.map((coach) => (
                <Item
                  key={coach?.id}
                  coachId={coach?.id}
                  photo={coach?.picture}
                  title={coach?.firstname?.concat(' ', coach?.lastname || '')}
                  designation={coach?.designation}
                  rating={parseFloat(coach?.rating || '')}
                  specialization={coach.my_specilization}
                  coachExperience={coachExperience}
                  onClick={openCoachDetails}
                />
              ))}
            </div>
          </div>
        ) : (
          <div className={styles.findPerfectCoach}>
            <div className={styles.descriptionWrapper}>
              <Typography className={styles.title}>
                Find Your Perfect Coach
              </Typography>
              <div className={styles.description}>
                <Typography as="span">{`Interested in coaching, but don't know where to start?`}</Typography>
                <Typography as="span">
                  Let our smart matching tool find the perfect coach for you.
                </Typography>
              </div>
              <Button
                type="button"
                color="black"
                className={styles.findCoachBtn}
                onClick={findePerfectCoach}
              >
                <Typography as="span">Match Me with a Coach</Typography>
              </Button>
            </div>
          </div>
        )}
      </WithLoader>
    </div>
  );
};

export default FindPerfectCoachWidget;
