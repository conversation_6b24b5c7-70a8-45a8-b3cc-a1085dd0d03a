import Image from 'next/image';
import { FC } from 'react';
import Typography from '@/ui/typography';
import { ICoachExperience } from '@/types/domain/learning/coach/experience';
import styles from './style.module.scss';

interface ICoachExperienceList {
  experience?: ICoachExperience[];
  displayCount?: number;
  customStyles?: { [key: string]: string };
}

const CoachExperienceList: FC<ICoachExperienceList> = ({
  experience,
  displayCount = 3,
  customStyles,
}) => {
  if (experience) {
    const experienceCount = experience.length;
    const first3Companies = experience.slice(0, displayCount);

    const _styles = customStyles ? customStyles : styles;

    return (
      <div className={_styles.coachExperience}>
        <div className={_styles.experienceLogos}>
          {first3Companies.map((item, index) => {
            return (
              <Image
                key={item.company}
                src={item.logo}
                alt="No Record Found"
                className={index > 0 ? _styles.logoImgOverlap : ''}
              />
            );
          })}
        </div>
        <Typography className={_styles.experienceText}>
          Worked with {first3Companies.slice(-1)[0].company}{' '}
          {experienceCount > 1 ? `+${experienceCount - 1}` : ''}
        </Typography>
      </div>
    );
  } else {
    return null;
  }
};

export default CoachExperienceList;
