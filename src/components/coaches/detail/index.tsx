import { FC, useCallback, useState, useRef, useEffect } from 'react';
import BackOverly from '@/ui/back-overly';
import Description from './description';
import Booking from './booking';
import BookingSuccess from './sucess';
import Rate from './rate';
import useCoacheDetailStore from '@/stores/learning/coaches/detail';
import { AnimatePresence, motion } from 'motion/react';
import Skeleton from '@/ui/skeleton';
import styles from './style.module.scss';
import useRateCoachStore from '@/stores/learning/coaches/rate-coach';
import { useLocale } from 'next-intl';
import { coachExperience } from '../mock-data';
import useCoachBookingStore from '@/stores/learning/coaches/booking';

const CoachingDetail: FC = () => {
  // State to manage the step
  const [step, setStep] = useState(1);
  const dialogRef = useRef<HTMLDivElement>(null);
  const locale = useLocale();

  // Getting store state
  const {
    isFetching,
    fetch: fetchDetail,
    detail: coach,
    visible: isVisible,
    setVisible,
  } = useCoacheDetailStore();

  const { setViewAllBookedSession } = useCoachBookingStore();

  const { resetRateStatus, setRateMessage } = useRateCoachStore();

  const handleClickOutside = (event: Event) => {
    if (
      dialogRef.current &&
      !dialogRef.current.contains(event.target as HTMLDivElement)
    ) {
      setVisible(false);
      setStep(1);
    }
  };

  useEffect(() => {
    document.addEventListener('click', handleClickOutside, true);
    return () => {
      document.removeEventListener('click', handleClickOutside, true);
    };
  }, []); // eslint-disable-line

  /**
   * Method to handle the close
   * event
   */
  const onClose = useCallback(() => {
    setVisible(false);
    setStep(1);
    resetRateStatus();
    document.removeEventListener('click', handleClickOutside, true);
  }, [setVisible, setStep, resetRateStatus]); // eslint-disable-line

  /**
   * Method to handle the next
   */
  const handleNext = useCallback(() => {
    setStep((prev) => prev + 1);
  }, [setStep]);

  const handleRate = useCallback(() => {
    setStep(4);
  }, [setStep]);

  /**
   * Method to handle the back
   */
  const handleBack = useCallback(() => {
    setStep((prev) => prev - 1);
  }, [setStep]);

  const handleViewAllBookings = useCallback(() => {
    setViewAllBookedSession(true);
  }, [setViewAllBookedSession]);

  const handleRateBack = useCallback(() => {
    setStep(1);
  }, [setStep]);

  const handleRateSubmit = useCallback(() => {
    fetchDetail(coach.id);
    resetRateStatus();
    setRateMessage();
    setStep(1);
  }, [setStep, coach.id, fetchDetail, resetRateStatus, setRateMessage]);

  // Return JSX
  return (
    <>
      <AnimatePresence mode="wait">
        {isVisible && (
          <motion.div
            initial={{
              right: locale != 'ar' ? -580 : 'auto',
              left: locale == 'ar' ? -580 : 'auto',
            }}
            animate={{
              right: locale != 'ar' ? 0 : 'auto',
              left: locale == 'ar' ? 0 : 'auto',
            }}
            exit={{
              right: locale != 'ar' ? -580 : 'auto',
              left: locale == 'ar' ? -580 : 'auto',
            }}
            className={styles.popup}
          >
            <BackOverly />
            <div className={styles.inner} ref={dialogRef}>
              {isFetching && <Skeleton />}
              {step === 1 && (
                <Description
                  coachId={coach?.id}
                  image={coach?.picture}
                  title={coach?.firstname?.concat(' ', coach?.lastname || '')}
                  designation={coach?.designation}
                  department={coach?.department}
                  experience={coachExperience}
                  skills={coach?.my_specilization?.split(',') || []}
                  content={coach?.description}
                  slots={parseInt(coach?.slot || '0')}
                  rating={coach?.rating}
                  users={coach?.user_count}
                  onNext={handleNext}
                  onRate={handleRate}
                  onBack={onClose}
                />
              )}
              {step === 2 && (
                <Booking
                  coachId={coach?.id}
                  onBack={handleBack}
                  onSucess={handleNext}
                />
              )}
              {step === 3 && (
                <BookingSuccess
                  onClose={onClose}
                  coachName={coach?.firstname?.concat(
                    ' ',
                    coach?.lastname || '',
                  )}
                  coachImage={coach?.picture}
                  designation={coach?.designation}
                  viewAllBookings={handleViewAllBookings}
                />
              )}
              {step === 4 && (
                <Rate
                  coachId={coach?.id}
                  myRate={coach?.my_rate}
                  onBack={handleRateBack}
                  onSubmitted={handleRateSubmit}
                />
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default CoachingDetail;
