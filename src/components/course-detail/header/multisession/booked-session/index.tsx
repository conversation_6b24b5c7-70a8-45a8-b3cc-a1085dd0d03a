import { FC, useCallback, useMemo, useState } from 'react';
import styles from './style.module.scss';
import { CalendarTimeSlot, BookedTimeSlot, Circle } from '@/ui/icons';
import Typography from '@/ui/typography';
import dictionary from '@/dictionaries';
import moment from 'moment';
import 'moment/locale/ar';
import { useLocale, useTranslations } from 'next-intl';
import { AvailableSlotMultiday } from '@/types/domain/learning/course';
import ClockFrame from '@/ui/icons/clock-frame';
import useMultisessionCourseStore from '@/stores/learning/courses/multisession';
import useCourseDetailStore from '@/stores/learning/courses/detail';
import CancelConfirmPopup from '../../cancel-confirm-popup';
import KababMenu from '@/ui/menu/kabab';

interface IBookedSession {
  assignedMultidaySlot?: AvailableSlotMultiday;
}

const BookedSession: FC<IBookedSession> = ({ assignedMultidaySlot }) => {
  const [showCancelPopup, setShowCancelPopup] = useState(false);

  const { seminar_date, name, events = [] } = assignedMultidaySlot || {};
  // seminarId will now be derived within handleCancel for each event
  const seminarDate = seminar_date || '';
  const locale = useLocale();
  const t = useTranslations();

  const formatedSessionDate = useMemo(() => {
    if (!seminarDate) {
      return '';
    }

    let outputLocale = 'en';
    if (locale && ['ar', 'en'].includes(locale.toLowerCase().substring(0, 2))) {
      outputLocale = locale.toLowerCase().substring(0, 2);
    }

    const [startDateString, endDateString] = seminarDate.split(' - ');

    if (!startDateString || !endDateString) {
      console.warn(
        'Could not split seminar_date into start and end dates:',
        seminarDate,
      );
      return seminarDate; // Fallback
    }

    const inputFormat = 'DD MMM YY';
    const parsingLocale = 'en'; // Default parsing locale

    const startMoment = moment(
      startDateString.trim(),
      inputFormat,
      parsingLocale,
    );
    const endMoment = moment(endDateString.trim(), inputFormat, parsingLocale);

    if (!startMoment.isValid() || !endMoment.isValid()) {
      console.warn(
        'Invalid date format for session parts. Original:',
        seminarDate,
        'Start part:',
        startDateString,
        'End part:',
        endDateString,
      );
      return seminarDate; // Fallback to original string
    }

    const outputFormat = 'D MMM';

    const formattedStart = startMoment
      .locale(outputLocale)
      .format(outputFormat);
    const formattedEnd = endMoment.locale(outputLocale).format(outputFormat);

    return `${formattedStart} - ${formattedEnd}`;
  }, [seminarDate, locale]);

  const cancelBookedSlot = useMultisessionCourseStore(
    (x) => x.cancelBookedSlot,
  );
  const courseDetail = useCourseDetailStore((x) => x.detail);
  const fetchCourseDetail = useCourseDetailStore((x) => x.fetch);

  const handleCancel = useCallback(async () => {
    if (!courseDetail?.courseId) {
      console.warn('Course ID is missing, cannot cancel slots.');
      setShowCancelPopup(false);
      return;
    }

    // Iterate over each event and attempt to cancel it
    for (let i = 0; i < events.length; i++) {
      const event = events[i];
      const isLastEvent = i === events.length - 1;

      if (event?.id) {
        const res = await cancelBookedSlot(
          courseDetail.courseId,
          Number(event.id),
          isLastEvent,
        );
        if (res.success) {
          console.log(`Successfully cancelled seminar ID: ${event.id}`);
        } else {
          console.error(`Failed to cancel seminar ID: ${event.id}`);
        }
      }
    }

    setShowCancelPopup(false);
    fetchCourseDetail(String(courseDetail.courseId));
  }, [cancelBookedSlot, courseDetail?.courseId, fetchCourseDetail, events]);

  return (
    <div className={styles.bookedSessionWrapper}>
      <div className={styles.sessionHeader}>
        <div className={styles.iconWrapper}>
          <BookedTimeSlot />
        </div>
        <Typography as="span">
          {assignedMultidaySlot?.booked.toLowerCase() === 'booked'
            ? t(dictionary.yourCohortDetails)
            : t(dictionary.requestedCohort)}
          :
        </Typography>
      </div>
      <div className={styles.sessionInfo}>
        <div className={styles.sessionInfoRow}>
          <ClockFrame />
          <Typography as="span">{name}</Typography>
        </div>
        <div className={styles.sessionInfoRow}>
          <CalendarTimeSlot />
          <Typography as="span">{formatedSessionDate}</Typography>
        </div>
      </div>
      <div className={styles.menuWrapper}>
        <KababMenu>
          <nav>
            <ul className={styles.menuList}>
              <li>
                <button
                  type="button"
                  className={styles.menuItem}
                  onClick={() => setShowCancelPopup(true)}
                >
                  <i>
                    <Circle />
                  </i>
                  <Typography as="span" dictionary={dictionary.cancelRequest} />
                </button>
              </li>
            </ul>
          </nav>
        </KababMenu>
      </div>
      {showCancelPopup && (
        <CancelConfirmPopup
          isOpened={showCancelPopup}
          onAccept={handleCancel}
          onCancel={() => setShowCancelPopup(false)}
          isCancelingCohort={true}
        />
      )}
    </div>
  );
};

export default BookedSession;
