@use 'mixins' as *;

.search {
  position: relative;
}

.input {
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
}

.searchBtn {
  position: absolute;
  @include rightToLeft(size(10));
  @include forMarginPadding(margin, auto, auto, auto, auto);
  top: 0;
  bottom: 0;
  width: size(40);
  height: size(40);
  background: none;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  @include borderRadius(50%);
  cursor: pointer;

  &:before {
    position: absolute;
    top: 0;
    bottom: 0;
    @include rightToLeft(0);
    @include leftToRight(0);
    width: 100%;
    height: 100%;
    @include transitions(0.5s);
    background: var(--grey-900);
    @include scale(0);
    @include borderRadius(50%);
    content: '';
  }

  i {
    width: size(20);
    height: size(20);
    position: relative;
    z-index: 2;

    svg {
      fill: var(--grey-900);
      fill-rule: evenodd;
      width: 100%;
      height: 100%;
      @include transitions(0.5s);
    }
  }
}

@include hover() {
  .searchBtn {
    &:hover {
      &:before {
        @include scale(1);
      }

      i {
        svg {
          fill: var(--white);
        }
      }
    }
  }
}
