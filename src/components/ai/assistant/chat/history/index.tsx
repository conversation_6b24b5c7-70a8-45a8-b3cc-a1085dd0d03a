import { FC, useState, ChangeEvent, useEffect, useCallback } from 'react';
import styles from './style.module.scss';
import ISessionStorage from '@/types/domain/ai/sessionStorage';
import useChatStore from '@/stores/ai/chat';
import useSessionStorageStore from '@/stores/ai/session-storage';
import useGenerativeStore from '@/stores/ai/generative';
import groupSessions from '@/hooks/ai/group-sessions/useGroupSessions';
import { sessionGroupsLabels } from '@/hooks/ai/group-sessions/constants';
import Trash from '@/ui/icons/trash';
import { ChatBubble, CloseThin, Eye, PlusThin, SearchIcon } from '@/ui/icons';
import Media from '@/ui/media';
import { useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';
import mapTranslations from '@/hooks/ai/group-sessions/map-translations';

interface IHistory {
  isExpanded?: boolean;
  onCloseAction?: (isClosed: boolean) => void;
}

const History: FC<IHistory> = ({ onCloseAction }) => {
  const sessions = localStorage.getItem('CHAT_SESSION_LIST') || [];
  const parsedSessions = sessions.length ? JSON.parse(sessions as string) : [];
  const [sortedSessions, setSortedSessions] = useState(parsedSessions);
  const setCurrentSession = useChatStore((x) => x.setCurrentSession);
  const setAiContext = useChatStore((x) => x.setAiContext);
  const resetTypedText = useChatStore((x) => x.resetTypedText);
  const { setActiveHistory, setStoredSession, storedSession, setActiveDate } =
    useSessionStorageStore();
  const { isGenerating } = useGenerativeStore();
  const t = useTranslations();

  const handleSession = (id: number, group: string) => {
    storeSession();
    const selectedSession = parsedSessions.find(
      (session: ISessionStorage) => session.id === id,
    );
    resetTypedText(true);
    setActiveHistory(true);
    setStoredSession(selectedSession);
    setCurrentSession(selectedSession.messages);
    setAiContext([]);
    setAiContext(selectedSession.context);
    setActiveDate(group);
    if (onCloseAction) {
      onCloseAction(true);
    }
  };

  const handleNewSession = () => {
    storeSession();
    setActiveHistory(false);
    setStoredSession(null);
    setCurrentSession([]);
    setAiContext([]);
    if (onCloseAction) {
      onCloseAction(true);
    }
  };

  const storeSession = () => {
    if (typeof window !== 'undefined' && storedSession?.id) {
      let isNewSession = true;
      let updatedSessions;
      updatedSessions = parsedSessions.map((parsedSession: ISessionStorage) => {
        if (parsedSession.id === storedSession?.id) {
          isNewSession = false;
          return storedSession;
        }
        return parsedSession;
      });

      if (isNewSession) {
        updatedSessions = [...parsedSessions, storedSession];
      }

      localStorage.setItem(
        'CHAT_SESSION_LIST',
        JSON.stringify(updatedSessions),
      );
    }
  };

  const handleDelete = (id: number, isActive: boolean) => {
    const updatedSessions = parsedSessions
      .map((parsedSession: ISessionStorage) => {
        return parsedSession.id !== id ? parsedSession : null;
      })
      .filter((parsedSession: ISessionStorage) => parsedSession !== null);

    localStorage.setItem('CHAT_SESSION_LIST', JSON.stringify(updatedSessions));
    setSortedSessions(groupSessions(updatedSessions));

    if (isActive) {
      setActiveHistory(false);
      setStoredSession(null);
      setCurrentSession([]);
      setAiContext([]);
    }
  };

  const handleClearAll = () => {
    localStorage.setItem('CHAT_SESSION_LIST', JSON.stringify([]));
    setSortedSessions(groupSessions([]));
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const filtered = parsedSessions.filter((session: ISessionStorage) =>
      session.title
        ?.toLowerCase()
        .includes(e.currentTarget.value.toLowerCase()),
    );

    setSortedSessions(groupSessions(filtered));
  };

  useEffect(() => {
    if (sessions.length) {
      setSortedSessions(groupSessions(parsedSessions));
    }
  }, [sessions]); //eslint-disable-line

  const onClickAction = useCallback(
    (isClosed: boolean) => {
      if (onCloseAction) {
        onCloseAction(isClosed);
      }
    },
    [onCloseAction],
  );

  return (
    <div className={styles.chatHistory}>
      <h2 className={styles.chatTitle}>{t(dictionary.aiAssistant)}</h2>
      <Media mobile={true} tablet={false} desktop={false}>
        <button className={styles.closeCta} onClick={() => onClickAction(true)}>
          <CloseThin />
        </button>
      </Media>
      <div className={styles.searchWrapper}>
        <SearchIcon />
        <input
          className={styles.searchHistory}
          onChange={handleChange}
          placeholder={t(dictionary.searchHistory)}
        />
      </div>
      <button
        onClick={() => handleNewSession()}
        className={styles.newCourseCta}
        disabled={isGenerating}
      >
        <PlusThin />
        {t(dictionary.newChat)}
      </button>
      <div className={styles.sessionWrapper}>
        {sessionGroupsLabels.map((sessionGroupLabel) => {
          return sortedSessions[sessionGroupLabel]?.length ? (
            <div key={sessionGroupLabel}>
              <h4 className={styles.groupTitle}>
                {t(mapTranslations(sessionGroupLabel))}
              </h4>
              <div className={styles.sessionGroupWrapper}>
                {sortedSessions[sessionGroupLabel]?.map(
                  (session: ISessionStorage, index: number) => {
                    return (
                      <div key={index} className={styles.sessionItem}>
                        <button
                          className={
                            styles.sessionCta +
                            ` ${session.id === storedSession?.id ? styles.activeSessionCta : ''}`
                          }
                          onClick={() =>
                            handleSession(session.id, sessionGroupLabel)
                          }
                          disabled={isGenerating}
                        >
                          {session.id !== storedSession?.id ? (
                            <ChatBubble />
                          ) : (
                            <Eye />
                          )}
                          {session.title}
                        </button>
                        <button
                          className={styles.deleteSession}
                          onClick={() =>
                            handleDelete(
                              session.id,
                              session.id === storedSession?.id ? true : false,
                            )
                          }
                          disabled={isGenerating}
                        >
                          <Trash />
                        </button>
                      </div>
                    );
                  },
                )}
              </div>
            </div>
          ) : null;
        })}
      </div>
      <button className={styles.clearAllCta} onClick={() => handleClearAll()}>
        <Trash />
        {t(dictionary.clearConversations)}
      </button>
    </div>
  );
};

export default History;
