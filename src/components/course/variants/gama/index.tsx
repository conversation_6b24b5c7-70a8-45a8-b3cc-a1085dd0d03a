import { FC } from 'react';
import styles from './style.module.scss';
import ICourse from '@/types/domain/learning/course';
import cn from 'classnames';
import Title from '../../title';
import Provider from '../../provider';
import Type from '../../type';
import Favorite from '../../favorite';
import Image from '../../image';
import InfoList from '../../info-list';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';
import Link from '../../link';

interface IGama {
  course: ICourse;
  inProgress?: boolean;
}

const Gama: FC<IGama> = (props) => {
  // Deconstructing props
  const { course, inProgress } = props;

  // Creating classname for card
  const classNames = cn({
    [styles.card]: true,
  });

  const t = useTranslations();

  const continueCourseLabel =
    t(dictionary.continue) + ' ' + t(dictionary.course);
  const continueProgramLabel =
    t(dictionary.continue) + ' ' + t(dictionary.program);
  const continueLabel =
    course.type === 'course' ? continueCourseLabel : continueProgramLabel;

  // Return JSX
  return (
    <div className={classNames}>
      <div className={styles.imageWrapper}>
        <Image
          course={course}
          className={styles.bg}
          hasLink
          alt={course?.title}
        />
      </div>
      <div className={styles.content}>
        <div className={styles.top}>
          <Type course={course} />
          <Favorite course={course} />
        </div>
        <div className={styles.bottom}>
          <Provider course={course} />
          <Title course={course} className={styles.heading} />
          {!inProgress ? (
            <InfoList course={course} hasRating={false} />
          ) : (
            <Link course={course}>
              <p className={styles.continueLabelGamma}>{continueLabel}</p>
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};

export default Gama;
