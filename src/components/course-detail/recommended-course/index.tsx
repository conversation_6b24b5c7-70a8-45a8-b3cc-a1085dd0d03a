import { useEffect } from 'react';
import { Link } from '@/i18n/routing';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';
import Typography from '@/ui/typography';
import useRecommendedCoursesStore from '@/stores/learning/courses/recommended';
import Item from './item';

// Styles
import styles from './style.module.scss';
import dictionary from '@/dictionaries';

const RecommendedCourse = () => {
  // Getting loading state
  const isFetching = useRecommendedCoursesStore((x) => x.isFetching);

  // Getting fetch action
  const onFetch = useRecommendedCoursesStore((x) => x.fetch);

  // Geting the recommended courses
  const list = useRecommendedCoursesStore((x) => x.list);

  // Effect to fetch the recommended courses
  useEffect(() => {
    // Fetch the recommended courses
    onFetch();
  }, [onFetch]);

  // Return JSX
  return (
    <div className={styles.recommended}>
      <Typography as="h2" dictionary={dictionary.recommendedLearning} />
      <WithLoader loading={isFetching} loader={<Loading />}>
        {list?.length > 0 ? (
          <>
            {list?.slice(0, 5).map((item) => (
              <Item
                key={item.id}
                image={item.image}
                title={item.title}
                linkImg={item.providerlogo}
                linkText={item.providername}
                course={item}
              />
            ))}
            <div className={styles.viewAll}>
              <Link href="/my-courses/recommended">
                <Typography as="span" dictionary={dictionary.viewAll} />
              </Link>
            </div>
          </>
        ) : (
          <Typography
            as="p"
            className="lead"
            dictionary={dictionary.noRecommendedLearningAvailable}
          />
        )}
      </WithLoader>
    </div>
  );
};

export default RecommendedCourse;
