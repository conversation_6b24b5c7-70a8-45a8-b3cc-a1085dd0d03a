import Avatar from '@/ui/image-fetcher/avatar';
import styles from './style.module.scss';
import profileImage from '@public/images/job-profile-image.jpg';
import loaderImage from '@public/images/image-loader.gif';
import { Corporate, PlusThin, WorkHistory } from '@/ui/icons';
import Typography from '@/ui/typography';
import { useRef } from 'react';

const GeneralInfo = () => {
  const isFetchingImage = false;
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const onClickAction = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0]; // Get the selected file
    if (file) {
      if (file.size >= 2097152) {
        event.target.value = '';
        return;
      }
      const fileType = file.type;
      const validImageTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
      ];

      if (!validImageTypes.includes(fileType)) {
        event.target.value = '';
        return;
      }
      try {
        console.log('Upload new photo logic');
      } catch (error) {
        console.log(error);
        return;
      }
    }
  };

  return (
    <div className={styles.profileGeneralInfoContainer}>
      <div className={styles.profileImageContainer}>
        {isFetchingImage ? (
          <img src={loaderImage.src} alt="Loading..." />
        ) : (
          <>
            <Avatar
              src={profileImage.src}
              name={'Abdullah Ahmed'}
              className={styles.profileInfoImage + ` img-fluid`}
            />
            <button
              type="button"
              onClick={() => onClickAction()}
              className={styles.profileUpdatePhotoButton}
            >
              <input
                type="file"
                ref={fileInputRef}
                accept="image/*"
                style={{ display: 'none' }}
                onChange={handleFileChange}
              />
              <PlusThin />
            </button>
          </>
        )}
      </div>
      <Typography as="span" className={styles.profileInfoName}>
        Abdullah Ahmed (You)
      </Typography>

      <Typography as="p" className={styles.profileInfoIcon}>
        <WorkHistory />
        <span>Director General</span>
      </Typography>
      <Typography as="p" className={styles.profileInfoIcon}>
        <Corporate />
        <span>Dept. of Government Enablement</span>
      </Typography>
    </div>
  );
};
export default GeneralInfo;
