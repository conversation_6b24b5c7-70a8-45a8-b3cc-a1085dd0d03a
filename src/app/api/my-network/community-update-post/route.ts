import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraCommunityDetailsUpdatePostService } from '@/services/totara/my-network';

/**
 * Route to update POST in community
 */
export async function POST(request: NextRequest) {
  try {
    // Getting request params
    const { postType, content, draftid, discussionId } = await request.json();

    // call API to update community POST
    const response = await TotaraCommunityDetailsUpdatePostService(
      postType,
      discussionId,
      content,
      draftid,
    );
    // Return the response
    return NextResponse.json({
      success: response.data.success,
      message: response.data.message,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error);
  }
}
