@use 'mixins' as *;

.filtersHead {
  position: relative;
  display: flex;
  align-items: center;
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));
}

.filterBtns {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
  white-space: nowrap;
  overflow-x: auto;

  &::-webkit-scrollbar {
    display: none;
  }
  ul {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
    display: flex;
    flex-direction: row;
    gap: size(8);
    flex-wrap: nowrap;

    li {
      list-style: none;

      button {
        display: block;
        text-align: center;
        border: solid 1px var(--grey-500);
        color: var(--grey-900);
        @include forMarginPadding(
          padding,
          size(10),
          size(16),
          size(10),
          size(16)
        );
        min-height: size(40);
        font-weight: 400;
        font-size: size(14);
        background: none;
        @include borderRadius(24px);
        cursor: pointer;
        min-width: auto;
        @include transitions(0.5s);
        display: flex;
        justify-content: center;
        align-items: center;

        span:nth-child(2) {
          display: flex;
          align-items: center;
          justify-content: center;
          width: size(21);
          height: size(21);
          border-radius: 50%;
          background: var(--grey-900);
          color: var(--white);
          font-size: size(14);
          margin-left: size(8);
        }

        &.active {
          background: var(--grey-900);
          color: var(--white);
          border-color: var(--grey-900);

          span:nth-child(2) {
            background: var(--white);
            color: var(--grey-900);
            border-color: var(--white);
          }
        }

        @include rtl {
          span:nth-child(2) {
            margin-right: size(8);
          }
        }
      }
    }
  }
  .wrapUl {
    flex-wrap: wrap;
  }
}

.scrollArrow {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  position: absolute;
  z-index: 1;
  display: flex;
  justify-content: center;
  height: size(34);
  width: size(34);
  border: 1px solid var(--grey-500);
  border-radius: 50%;
  background: var(--white);

  &:hover {
    opacity: 1;
  }

  &:first-child {
    left: 0;
  }

  &:last-child {
    right: 0;
  }

  svg {
    fill: var(--grey-900);
    height: size(14);
    width: size(14);
  }
}

.scrollArrow {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  position: absolute;
  z-index: 1;
  display: flex;
  justify-content: center;
  height: size(34);
  width: size(34);
  border: 1px solid var(--grey-500);
  border-radius: 50%;
  background: var(--white);

  &:hover {
    opacity: 1;
  }

  &:first-child {
    left: 0;
  }

  &:last-child {
    right: 0;
  }

  svg {
    fill: var(--grey-900);
    height: size(14);
    width: size(14);
  }
}

@include for-all-phone() {
  .filtersHead {
    height: fit-content;
    -webkit-overflow-scrolling: touch;
    overflow-y: hidden;
    overflow-x: scroll;
    overflow: auto;
    white-space: nowrap;
    display: flex;
    flex-wrap: nowrap;
  }

  .filterBtns {
    ul {
      li {
        button {
          font-size: size(14);
        }
      }
    }
  }
}
