import Typography from '@/ui/typography';
import { FC, useCallback, useMemo, useState } from 'react';
import Image, { StaticImageData } from 'next/image';
import star from '@public/images/yellow-star.svg';

import styles from './style.module.scss';
import { ICoachExperience } from '@/types/domain/learning/coach/experience';
import CoachExperienceList from '@/components/coaches/experience';
import { BookmarkFilled, BookmarkOutline, WorkOutline } from '@/ui/icons';

interface IBookedCoachItem {
  photo?: StaticImageData | string;
  title?: string;
  designation?: string;
  rating?: number;
  coachId?: string;
  experience: ICoachExperience[];
  customStyles?: { [key: string]: string };
  experienceDisplayCount?: number;
  showSkills?: boolean;
  specialization?: string;
  showWorkIcon?: boolean;
}

const BookedCoachItem: FC<IBookedCoachItem> = ({
  photo,
  title,
  designation,
  rating,
  experience,
  customStyles,
  experienceDisplayCount = 3,
  showSkills = false,
  specialization,
  showWorkIcon = false,
}) => {
  const [bookmark, setBookmark] = useState<boolean>(true);

  const _styles = customStyles ? customStyles : styles;

  const skills = useMemo(() => {
    if (specialization?.length) {
      const skillsList = specialization?.split(',');
      const restCount = skillsList?.length > 3 ? skillsList?.length - 3 : 0;
      return skillsList?.map((item, index) => {
        if (index < 4) {
          return (
            <div className={_styles.skillItem} key={item}>
              <Typography as="span" className={_styles.skill}>
                {index < 3 ? item : `+${restCount}`}
              </Typography>
            </div>
          );
        }
      });
    }
  }, [specialization, _styles.skill, _styles.skillItem]);

  const bookmarkCoachToggle = useCallback(() => {
    // setBookmark((prev) => !prev);
    setBookmark(true);
  }, []);

  return (
    <div className={_styles.bookedCoachItem}>
      <div className={_styles.mainContent}>
        <div>
          <img
            src={photo as string}
            alt="coach photo"
            className={`img-fluid ${_styles.photo}`}
          />
        </div>
        <div className={_styles.description}>
          <div className={_styles.nameAndRating}>
            <Typography className={_styles.coachName}>{title}</Typography>
            {rating != 0 && rating ? (
              <div className={_styles.rating}>
                <i>
                  <Image
                    src={star}
                    className="img-fluid"
                    alt=""
                    width={14}
                    height={14}
                  />
                </i>
                <Typography as="span">{rating || '0'}</Typography>
              </div>
            ) : null}
          </div>
          {designation ? (
            <div className={_styles.designationWrapper}>
              {showWorkIcon && <WorkOutline />}
              <Typography className={_styles.designation}>
                {designation}
              </Typography>
            </div>
          ) : (
            ''
          )}
          <CoachExperienceList
            experience={experience}
            displayCount={experienceDisplayCount}
            customStyles={_styles}
          />
        </div>
      </div>
      {showSkills && (
        <div className={_styles.skillsAndBookmark}>
          <div className={_styles.skills}>{skills}</div>
          <span className={_styles.bookmark} onClick={bookmarkCoachToggle}>
            {bookmark ? <BookmarkFilled /> : <BookmarkOutline />}
          </span>
        </div>
      )}
    </div>
  );
};

export default BookedCoachItem;
