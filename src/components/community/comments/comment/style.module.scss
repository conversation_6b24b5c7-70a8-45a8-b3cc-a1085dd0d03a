@use 'mixins' as *;

.commentWrapper {
  display: flex;
  flex-direction: row;
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));
}

.avatar {
  width: size(40);
  height: size(40);
  @include borderRadius(50%);
  @include forMarginPadding(margin, size(0), size(12), size(0), size(0));
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  @include borderRadius(50%);
  background: rgba(160, 146, 200, 0.15);

  p {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    font-weight: 500;
    font-size: size(16);
  }
}

.commentSection {
  width: 100%;
}

.contentSection {
  display: flex;
}

.containerComment {
  background-color: var(--bg-shade-15);
  border-radius: 2px 12px 12px 12px;
  @include forMarginPadding(padding, size(13), size(60), size(18), size(18));
  position: relative;
}

.headerComment {
  @include forMarginPadding(margin, size(0), size(0), size(8), size(0));
  display: flex;
  align-items: center;
  span[class*='dot-breaker'] {
    font-size: size(16);

    color: var(--grey-700);
    @include forMarginPadding(margin, size(4), size(8), size(4), size(8));
  }
}

@include for-all-phone() {
  .headerComment {
    flex-direction: column;
    align-items: flex-start;
    span {
      @include forMarginPadding(margin, size(0), size(0), size(8), size(0));
    }
    span[class*='dot-breaker'] {
      display: none;
    }
  }
}

.menuWrapper {
  @include forMarginPadding(padding, size(9), size(9), size(9), size(9));
  display: flex;
  gap: size(16);
  position: absolute;
  top: 0;
  right: 0;

  @include rtl {
    right: auto;
    left: 0;
  }

  div[class*='menu'] {
    @include forMarginPadding(padding, size(4), size(16), size(4), size(16));
    background-color: var(--tag);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }
}

.menuList {
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));

  li {
    list-style: none;
    border-bottom: solid 1px var(--cool-grey-700);

    &:last-child {
      border: none;
    }

    button {
      border: none;
      background: none;
      @include forMarginPadding(
        padding,
        size(12),
        size(12),
        size(12),
        size(12)
      );
      line-height: 1;
      display: flex;
      align-items: center;
      width: 100%;
      cursor: pointer;

      i {
        width: size(24);
        height: size(24);
        @include forMarginPadding(margin, size(0), size(10), size(0), size(0));

        svg {
          fill: var(--grey-900);
          fill-rule: evenodd;
        }

        &.plus {
          background: var(--grey-900);
          fill: var(--white);
          @include borderRadius(50%);
          display: flex;
          justify-content: center;
          align-items: center;

          svg {
            width: size(11);
            height: size(11);
          }
        }
      }

      span {
        font-size: size(14);
        display: inline;
        padding-bottom: 0;
        transition: all 0.5s linear;
        background: linear-gradient(
          to bottom,
          var(--grey-900) 0%,
          var(--grey-900) 98%
        );
        background-size: 0 1px;
        background-repeat: no-repeat;
        background-position: left 100%;

        @include rtl {
          background-position: right 100%;
        }
      }
    }
  }
}

.title {
  font-size: size(20);
  line-height: 1;
  font-weight: 500;
}

.time {
  font-size: size(16);
  line-height: 1;
  color: var(--grey-800);
}

.postImage {
  @include forMarginPadding(margin, size(16), size(0), size(8), size(0));

  height: size(150);
  width: size(150);
  overflow: hidden;

  img {
    object-fit: cover;
    object-position: center;
    height: 100%;
    width: auto;
  }
}

.containerLike {
  @include forMarginPadding(padding, size(24), size(0), size(0), size(0));
  display: flex;
}

.message {
  overflow-wrap: break-word;
  word-wrap: break-word;
  white-space: normal;
  word-break: break-all;
  p {
    font-size: size(20);
  }
}

.ftButton {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: size(14);
  @include forMarginPadding(margin, size(0), size(30), size(0), size(0));

  &:last-child {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }

  i {
    width: size(17);
    height: size(17);
    @include forMarginPadding(margin, size(0), size(10), size(0), size(0));

    svg {
      fill: var(--grey-900);
    }
  }

  span {
    display: inline;
    padding-bottom: 0;
    transition: all 0.5s linear;
    background: linear-gradient(
      to bottom,
      var(--grey-900) 0%,
      var(--grey-900) 98%
    );
    background-size: 0 1px;
    background-repeat: no-repeat;
    background-position: left 100%;

    @include rtl {
      background-position: right 100%;
    }
  }

  .iconLike {
    fill: var(--grey-900);
    svg {
      fill: var(--grey-900);
    }
    path {
      fill: var(--grey-900);
    }
  }
}

.replyWrapper {
  @include forMarginPadding(margin, size(24), size(0), size(0), size(0));
}

.lightBoxOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99;
}

.lightBoxContent {
  position: relative;
  background: white;
  padding: 20px;
  border-radius: 8px;
  max-width: 90%;
  max-height: 90%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.lightBoxImage {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  display: block;
}
