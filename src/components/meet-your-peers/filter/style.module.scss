@use 'mixins' as *;

.filterBtn {
  background-color: var(--grey-300);
  border: none;
  display: flex;
  align-items: center;
  color: var(--grey-900);
  @include forMarginPadding(padding, size(12), size(12), size(12), size(12));
  cursor: pointer;

  i {
    width: size(24);
    height: size(24);
    display: flex;

    svg {
      fill: var(--grey-900);
      fill-rule: evenodd;
      width: 100%;
    }
  }
}

.filterPopup {
  position: fixed;
  @include leftToRight(0);
  top: 0;
  width: 100%;
  height: 100%;
  @include forMarginPadding(margin, size(0), size(0), size(30), size(0));
  z-index: 99;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.wrapper {
  @include forMarginPadding(padding, size(0), size(24), size(0), size(0));
}

.applyFilterButton {
  position: sticky;
  bottom: size(24);
  width: 100%;
  @include forMarginPadding(margin, auto, size(0), size(0), size(0));
}

.head {
  display: flex;
  flex-direction: column;
  position: relative;

  .top {
    display: flex;
    justify-content: flex-end;
    @include forMarginPadding(margin, size(0), size(0), size(24), size(0));
  }

  .bottom {
    display: flex;
    justify-content: space-between;
    @include forMarginPadding(margin, size(0), size(0), size(30), size(0));
  }
}

.resetBtn {
  background: none;
  color: var(--grey-900);
  font-size: size(16);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  i {
    width: size(20);
    height: size(20);
    display: block;
    @include forMarginPadding(margin, size(0), size(10), size(0), size(0));

    svg {
      fill: var(--grey-900);
      fill-rule: evenodd;
      width: 100%;
    }
  }

  span {
    display: inline;
    padding-bottom: 0;
    transition: all 0.5s linear;
    background: linear-gradient(
      to bottom,
      var(--grey-900) 0%,
      var(--grey-900) 98%
    );
    background-size: 0 1px;
    background-repeat: no-repeat;
    background-position: left 100%;

    @include rtl {
      background-position: right 100%;
    }
  }
}

.filter {
  @include forMarginPadding(padding, size(20), size(0), size(0), size(0));
  border-top: 1px solid var(--grey-400);

  + .filter {
    @include forMarginPadding(margin, size(20), size(0), size(0), size(0));
  }

  h4 {
    @include forMarginPadding(margin, size(0), size(0), size(10), size(0));
    @include forMarginPadding(padding, size(0), size(0), size(10), size(0));
  }

  ul {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
    display: flex;
    flex-wrap: wrap;

    li {
      list-style: none;
      @include forMarginPadding(margin, size(0), size(10), size(10), size(0));
    }
  }
}

.closeFilter {
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  background: none;
  cursor: pointer;
  width: size(30);
  height: size(30);
  border: solid 1px var(--grey-700);
  fill: var(--grey-700);
  color: var(--grey-700);
  @include borderRadius(50%);
  display: flex;
  justify-content: center;
  align-items: center;
  @include transitions(0.5s);

  i {
    display: block;
    width: size(10);
    height: size(10);

    svg {
      fill-rule: evenodd;
      width: 100%;
      vertical-align: top;
    }
  }
}

.searchInput {
  position: relative;

  div[class*='formGroup'] {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }

  svg {
    fill: var(--grey-900);
  }

  .searchButton {
    align-items: center;
    background-color: transparent;
    border: 0;
    cursor: pointer;
    position: absolute;
    right: 0;
    bottom: 0;
    height: size(56);
    width: size(56);

    @include rtl {
      left: 0;
      right: auto;
    }

    i {
      height: size(24);
      padding: size(2);
      width: size(24);
    }

    svg {
      height: size(20);
      width: size(20);
      fill: var(--grey-900);
    }
  }
}

.suggestions {
  display: flex;
  flex-direction: column;
}

.selection {
  border-top: 1px solid var(--cool-grey-800);
  display: flex;
  gap: size(12);
  flex-wrap: wrap;
  padding-top: size(24);
}

@include hover {
  .resetBtn {
    &:hover {
      span {
        background-size: 100% 1px;
      }
    }
  }

  .closeFilter {
    &:hover {
      border-color: var(--grey-900);
      fill: var(--grey-900);
      color: var(--grey-900);
    }
  }
}

@include for-all-phone() {
  .wrapper {
    width: 100%;
  }
}
