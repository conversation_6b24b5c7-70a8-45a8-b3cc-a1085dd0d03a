import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchCommunityDetailsLibraryService } from '@/services/totara/my-network';

/**
 * Route to get Community details
 */
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const urlSplit = request.url.split('/');
    const communityId = urlSplit.filter((x) => !isNaN(parseInt(x)))[0];
    // Fetching community details
    const response = await TotaraFetchCommunityDetailsLibraryService(
      Number(communityId),
    );
    // Return the response
    return NextResponse.json({
      success: response.data.success,
      records: response.data.records,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error);
  }
}
