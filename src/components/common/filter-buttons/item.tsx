import { FC, useCallback, useMemo } from 'react';
import Typography from '@/ui/typography';

// Styles
import styles from './style.module.scss';

interface IFilterItemProps {
  id: string;
  active: string;
  title: string;
  dictionary: string;
  numberOfItems?: number;
  onClick: (id: string) => void;
}

const FilterItem: FC<IFilterItemProps> = (props) => {
  const { id, active, title, dictionary, numberOfItems, onClick } = props;

  const showNumberOfItems = !!numberOfItems;

  const noItems = numberOfItems === 0;

  // Check if the button is active
  const activeClassName = useMemo(() => {
    return active === id ? styles.active : '';
  }, [active, id]);

  /**
   * Method to handle click event
   */
  const handleClick = useCallback(() => {
    onClick(id);
  }, [id, onClick]);

  // Return JSX
  return (
    <button
      id={id}
      type="button"
      className={activeClassName}
      onClick={handleClick}
    >
      <Typography as="span" dictionary={dictionary}>
        {title}
      </Typography>
      {(showNumberOfItems || noItems) && (
        <Typography as="span">{numberOfItems}</Typography>
      )}
    </button>
  );
};

// Return Component
export default FilterItem;
