@use 'mixins' as *;

.pic {
  width: size(80);
  height: size(80);
  overflow: hidden;
  @include borderRadius(50%);
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  text-align: center;

  [data-percent] {
    width: 100%;
    height: 100%;
    position: absolute;
    @include leftToRight(0);
    top: 0;
  }

  a {
    width: size(64);
    height: size(64);

    img {
      position: relative;
      z-index: 9;
      width: size(64);
      height: size(64);
      @include borderRadius(50%);
    }
  }
}

.name {
  font-size: size(28);
  text-decoration: none;
  color: var(--grey-900);
  font-weight: 700;
  overflow: hidden;
  display: block;
  text-transform: capitalize;
  word-wrap: break-word;
  @include forMarginPadding(margin, size(10), size(0), size(5), size(0));
  letter-spacing: -1px;

  @include rtl {
    letter-spacing: initial;
  }
}

.title {
  font-size: size(16);
  font-weight: 500;
  text-decoration: none;
  display: inline-block;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
  text-transform: capitalize;
  @include forMarginPadding(margin, size(0), size(0), size(10), size(0));
}

.profileComp {
  display: block;
  font-size: size(14);
  color: var(--text-link-blue);
  font-weight: 400;
}

.workInfo {
  ul {
    margin: 0px;
    padding: 0px;

    li {
      list-style: none;
      @include forMarginPadding(margin, size(0), size(0), size(10), size(0));
      font-size: size(14);
      color: var(--grey-900);
      display: flex;
      align-items: center;
      text-decoration: none;

      i {
        width: size(20);
        height: size(20);
        @include forMarginPadding(margin, size(0), size(10), size(0), size(0));

        svg {
          fill: var(--grey-900);
          width: 100%;
          height: 100%;
        }
      }

      &:last-child {
        @include forMarginPadding(margin, size(0), size(0), size(10), size(0));
      }
    }
  }
}

.greetingProgress {
  @include forMarginPadding(margin, size(30), size(0), size(0), size(0));
}

@include for-dark-theme() {
  .workInfo {
    ul {
      li {
        color: var(--grey-800);

        i {
          svg {
            fill: var(--grey-700);
          }
        }
      }
    }
  }
}

.sideLoading {
  min-height: size(330);
}
