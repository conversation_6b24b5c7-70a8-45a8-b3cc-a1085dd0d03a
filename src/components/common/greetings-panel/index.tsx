'use client';

import { Link } from '@/i18n/routing';
import CONF<PERSON> from '@/config';
import WithLoader from '@/ui/skeleton/with-loader';
import CircularProgress from '@/ui/progress/circular';
import Paper from '@/ui/paper';
import Typography from '@/ui/typography';
import Loading from './loading';
import useAuthStore from '@/stores/auth';

import { Experience, Corporate } from '@/ui/icons';
import IUserWorkExperience from '@/types/domain/external/user/work-experience/IUserWorkExperience';

// Styles
import styles from './style.module.scss';
import Avatar from '@/ui/image-fetcher/avatar';
import dictionary from '@/dictionaries';
import { useLocale, useTranslations } from 'next-intl';
import {
  formatExperience,
  getLocalizedValue,
  usernameFormatter,
} from '@/utils/helpers';

const GreetingsPanel = () => {
  // Getting auth user
  const isLoading = useAuthStore((x) => x.isFetching);
  // Getting auth user
  const user = useAuthStore((x) => x.user);
  // Getting user profile progress
  const progress = useAuthStore((x) => x.progress);
  const t = useTranslations();
  const locale = useLocale();

  const getCurrentOrLatestEmployerName = (
    workExperience: IUserWorkExperience[],
  ) => {
    if (workExperience?.length) {
      const currentJob = workExperience.find(
        (job) => job.currentlyWorking?.toString() === 'true',
      );
      const allJobsHaveDate = workExperience.every(
        (job) => job.workExperienceStartDate,
      );

      if (currentJob) {
        return currentJob.department;
      }

      if (allJobsHaveDate) {
        return workExperience.reduce((latest, current) => {
          return new Date(current.workExperienceStartDate as string) >
            new Date(latest.workExperienceStartDate as string)
            ? current
            : latest;
        }).organizationName;
      }

      return '';
    }

    return '';
  };

  // Return JSX
  return (
    <WithLoader loader={<Loading />} loading={isLoading}>
      <Paper variant="sidePanel">
        <div className={styles.pic}>
          <CircularProgress
            id="greeting"
            percentage={progress}
            radius={40}
            circleWidth={80}
            strokWidth="8"
          />
          <Link href={CONFIG.routes.profile}>
            <Avatar
              src={user?.picture}
              name={getLocalizedValue(user, 'firstName', locale)}
              className="img-fluid"
            />
          </Link>
        </div>
        <Link href={CONFIG?.routes?.profile} className={styles.name}>
          <Typography as="span">
            {usernameFormatter(getLocalizedValue(user, 'fullname', locale))}
          </Typography>
        </Link>
        {user?.jobTitle && (
          <Typography as="p" className={styles.title}>
            {user?.jobTitle}
          </Typography>
        )}
        {progress ? (
          <p className={styles.profileComp}>
            <Typography as="b">{`${progress?.toString()}% `}</Typography>
            <Typography as="span" dictionary={dictionary.profileCompleted} />
          </p>
        ) : null}
        {user && user?.workExperience && user?.workExperience?.length > 0 && (
          <nav className={styles.workInfo}>
            <ul>
              {user?.totalYearsOfExperience && (
                <li>
                  <i>
                    <Experience />
                  </i>
                  <Typography as="span">
                    {formatExperience(user?.totalYearsOfExperience)}
                    {` ${t(dictionary.yearsExperience)}`}
                  </Typography>
                </li>
              )}
              <li>
                <i>
                  <Corporate />
                </i>
                <Typography as="span">
                  {getCurrentOrLatestEmployerName(user.workExperience)}
                </Typography>
              </li>
            </ul>
          </nav>
        )}
      </Paper>
    </WithLoader>
  );
};

export default GreetingsPanel;
