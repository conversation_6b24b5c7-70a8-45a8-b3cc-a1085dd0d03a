import { FC, useEffect, useState, useRef } from 'react';
import styles from './style.module.scss';
import useAuthStore from '@/stores/auth';
import useChatStore from '@/stores/ai/chat';
import useGenerativeStore from '@/stores/ai/generative';
import useCompletionStore from '@/stores/ai/completion';
import aiLoop from '@public/images/ai-loop.gif';
import Image from 'next/image';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import CarouselItem from './carousel-item';
import ISuggestionCourse from '@/types/domain/ai/suggestionCourse';
import Avatar from '@/ui/image-fetcher/avatar';
import { motion, cubicBezier, AnimatePresence } from 'motion/react';
import useSessionStorageStore from '@/stores/ai/session-storage';
import DotsLoader from '@/ui/dots-loader';
import { useLocale, useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';
import mapTranslations from '@/hooks/ai/group-sessions/map-translations';

interface IBody {
  onCloseChat?: (isActive: boolean) => void;
  isExpanded?: boolean;
}

const Body: FC<IBody> = ({ isExpanded }) => {
  const user = useAuthStore((x) => x.user);
  const currentSession = useChatStore((x) => x.currentSession);
  const typedText = useChatStore((x) => x.typedText);
  const isGenerating = useGenerativeStore((x) => x.isGenerating);
  const setIsGenerating = useGenerativeStore((x) => x.setIsGenerating);
  const resetTypedText = useChatStore((x) => x.resetTypedText);
  const hasChatError = useChatStore((x) => x.hasChatError);
  const hasCompletionError = useCompletionStore((x) => x.hasCompletionError);
  const { activeHistory, activeDate } = useSessionStorageStore();
  const [typingAnswer, setTypingAnswer] = useState<string>('');
  const [isTyping, setIsTyping] = useState<boolean>(true);
  const messagesEndRef = useRef<null | HTMLDivElement>(null);
  const locale = useLocale();
  const activeAnswer = currentSession?.findLast(
    (element) => element.role === 'assistant',
  );
  const activeAnswerIndex = currentSession?.findLastIndex(
    (element) => element.role === 'assistant',
  );
  const activeSuggestionIndex = currentSession?.findLastIndex(
    (element) => element.role === 'suggestions',
  );
  const activeUserIndex = currentSession?.findLastIndex(
    (element) => element.role === 'user',
  );

  const t = useTranslations();

  useEffect(() => {
    if (typedText) {
      setTypingAnswer('');
      resetTypedText(false);
    }
  }, [typedText, resetTypedText]);

  const charOffset = activeHistory ? 5000 : 15;

  useEffect(() => {
    if (
      activeAnswer?.content &&
      typingAnswer.length < activeAnswer.content.length
    ) {
      setIsTyping(true);
      const timeout = setTimeout(() => {
        const nextIndex = Math.min(
          typingAnswer?.length + charOffset,
          activeAnswer?.content?.length as number,
        );
        setTypingAnswer(
          (activeAnswer?.content as string)?.substring(0, nextIndex) as string,
        );
      }, 50);
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollTop = messagesEndRef.current.scrollHeight;
      }
      return () => clearTimeout(timeout);
    } else {
      setTimeout(() => {
        if (messagesEndRef.current) {
          messagesEndRef.current.scrollTop =
            messagesEndRef.current.scrollHeight;
        }
      }, 100);

      setIsTyping(false);
    }
  }, [currentSession, typingAnswer, activeAnswer?.content, charOffset]);

  useEffect(() => {
    if (hasChatError || hasCompletionError) {
      setIsGenerating(false);
    }
  }, [hasChatError, hasCompletionError, setIsGenerating]);

  return (
    <div
      className={
        styles.bodyWrapper + ` ${!isExpanded ? styles.bodyCollapsed : ''}`
      }
      ref={messagesEndRef}
    >
      <div className={styles.introArea}>
        <AnimatePresence mode="wait">
          {!currentSession?.length ? (
            <motion.div
              initial={{
                height: 0,
                position: 'relative',
              }}
              animate={{
                height: 'auto',
              }}
              exit={{
                height: 0,
              }}
              transition={{
                ease: cubicBezier(0.71, 0.23, 0.31, 0.86),
                duration: 0.4,
              }}
              className={styles.greetingArea}
            >
              <Image
                width={200}
                height={200}
                src={aiLoop}
                className={
                  styles.aiLoop + ` ${isExpanded ? styles.expandedImage : ''}`
                }
                alt="ai loop"
              />
              <div
                className={
                  styles.profileMessage +
                  ` ${!isExpanded ? styles.smallFont : ''}`
                }
              >
                <p
                  className={
                    styles.greeting + ` ${!isExpanded ? styles.smallFont : ''}`
                  }
                >
                  {t(dictionary.hi)}{' '}
                  {locale === 'en' ? user?.firstName : user?.firstNameAR},
                </p>
                <p
                  className={
                    styles.message + ` ${!isExpanded ? styles.smallFont : ''}`
                  }
                >
                  {t(dictionary.howCanIHelpYouToday)}
                </p>
              </div>
            </motion.div>
          ) : null}
        </AnimatePresence>
      </div>

      <div
        className={
          styles.askingArea +
          ` ${currentSession?.length ? styles.activeArea : ''}`
        }
      >
        <div className={styles.topDate}>
          {activeDate && t(mapTranslations(activeDate as string))}
        </div>
        {currentSession?.map((message, index) => {
          if (message.role === 'user') {
            return (
              <div key={index} className={styles.generatingLoader}>
                <div className={styles.userWrapper}>
                  <Avatar src={user?.picture} className={styles.userAvatar} />
                  <div className={styles.userQuestion}>
                    {message.content as string}
                  </div>
                </div>
                {isGenerating &&
                !hasChatError &&
                !hasCompletionError &&
                index === activeUserIndex ? (
                  <div className={styles.answerWrapper}>
                    <Image
                      width={40}
                      height={40}
                      src={aiLoop}
                      className={
                        styles.assistantAvatar + ` ${styles.placeholderAvatar}`
                      }
                      alt="ai loop"
                    />
                    <div
                      className={
                        styles.assistantAnswer + ` ${styles.placeholderAnswer}`
                      }
                    >
                      <DotsLoader />
                      <p>{t(dictionary.generatingResultForYou)}</p>
                    </div>
                  </div>
                ) : null}
              </div>
            );
          } else if (message.role === 'assistant') {
            return (
              <div key={index} className={styles.answerWrapper}>
                <Image
                  width={40}
                  height={40}
                  src={aiLoop}
                  className={styles.assistantAvatar}
                  alt="ai loop"
                />
                <div
                  className={styles.assistantAnswer}
                  dangerouslySetInnerHTML={{
                    __html:
                      index === activeAnswerIndex && !activeHistory
                        ? typingAnswer
                        : (message.content as string | TrustedHTML),
                  }}
                />
              </div>
            );
          } else if (
            message.role === 'suggestions' &&
            message.content &&
            index !== activeSuggestionIndex
          ) {
            return (
              <div key={index} className={styles.suggestionSlider}>
                <h4 className={styles.recommendedLabel}>
                  {t(dictionary.recommendedCourses)}
                </h4>
                <Swiper
                  spaceBetween={16}
                  slidesPerView={isExpanded ? 2.6 : 1.6}
                  navigation={true}
                  modules={[Navigation]}
                  breakpoints={{
                    1024: {
                      slidesPerView: isExpanded ? 2.6 : 1.6,
                      spaceBetween: 16,
                    },
                    768: {
                      slidesPerView: isExpanded ? 1.6 : 1.2,
                      spaceBetween: 16,
                    },
                  }}
                >
                  {(message.content as [])?.map((course: ISuggestionCourse) => {
                    return (
                      <SwiperSlide
                        className={styles.courseSlide}
                        key={course.Id}
                      >
                        <CarouselItem item={course} />
                      </SwiperSlide>
                    );
                  })}
                </Swiper>
              </div>
            );
          } else if (
            message.role === 'suggestions' &&
            message.content &&
            index === activeSuggestionIndex &&
            (!isTyping || activeHistory)
          ) {
            return (
              <div key={index} className={styles.suggestionSlider}>
                <h4 className={styles.recommendedLabel}>
                  {t(dictionary.recommendedCourses)}
                </h4>
                <Swiper
                  spaceBetween={16}
                  slidesPerView={isExpanded ? 2.6 : 1.6}
                  navigation={true}
                  modules={[Navigation]}
                  breakpoints={{
                    1024: {
                      slidesPerView: isExpanded ? 2.6 : 1.6,
                      spaceBetween: 16,
                    },
                    768: {
                      slidesPerView: isExpanded ? 1.6 : 1.2,
                      spaceBetween: 16,
                    },
                  }}
                >
                  {(message.content as [])?.map((course: ISuggestionCourse) => {
                    return (
                      <SwiperSlide
                        className={styles.courseSlide}
                        key={course.Id}
                      >
                        <CarouselItem item={course} />
                      </SwiperSlide>
                    );
                  })}
                </Swiper>
              </div>
            );
          }
        })}
      </div>
      <div ref={messagesEndRef}></div>
    </div>
  );
};

export default Body;
