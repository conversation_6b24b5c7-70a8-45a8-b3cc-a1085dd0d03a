import { FC, useCallback, useMemo, useState } from 'react';
import Typography from '@/ui/typography';
import star from '@public/images/yellow-star.svg';
import userImage from '@public/images/no-user.webp';

// Styles
import styles from '../style.module.scss';
import Image, { StaticImageData } from 'next/image';
import { WorkOutline, BookmarkFilled, BookmarkOutline } from '@/ui/icons';
import { ICoachExperience } from '@/types/domain/learning/coach/experience';

interface IItem {
  photo?: StaticImageData | string;
  title?: string;
  designation?: string;
  rating?: number;
  coachId?: string;
  specialization?: string;
  coachExperience?: ICoachExperience[];
  onClick?: (id: string) => void;
}

/**
 * Component to handle item
 */
const Item: FC<IItem> = (props) => {
  // Deconstruct props
  const {
    photo = userImage,
    title,
    designation,
    rating,
    coachId,
    specialization,
    coachExperience,
    onClick,
  } = props;
  const [bookmark, setBookmark] = useState<boolean>(false);

  const openCoachDetails = useCallback(() => {
    // Check if onClick is defined
    if (onClick) {
      // Call onClick
      onClick(coachId || '');
    }
  }, [onClick, coachId]);

  const bookmarkCoachToggle = useCallback(() => {
    setBookmark((prev) => !prev);
  }, []);

  const skills = useMemo(() => {
    if (specialization?.length) {
      const skillsList = specialization?.split(',');
      const restCount = skillsList?.length > 2 ? skillsList?.length - 2 : 0;
      return skillsList?.map((item, index) => {
        if (index < 3) {
          return (
            <div className={styles.skillItem} key={item}>
              <Typography as="span" className={styles.skill}>
                {index < 2 ? item : `+${restCount}`}
              </Typography>
            </div>
          );
        }
      });
    }
  }, [specialization]);

  const displayCoachExperience = useMemo(() => {
    if (coachExperience) {
      const experienceCount = coachExperience.length;
      const first3Companies = coachExperience.slice(0, 3);
      return (
        <>
          <div className={styles.experienceLogos}>
            {first3Companies.map((item, index) => {
              return (
                <Image
                  key={item.company}
                  src={item.logo}
                  alt="No Record Found"
                  className={index > 0 ? styles.logoImgOverlap : ''}
                />
              );
            })}
          </div>
          <Typography className={styles.experienceText}>
            Worked with {first3Companies.slice(-1)[0].company}{' '}
            {experienceCount > 1 ? `+${experienceCount - 1}` : ''}
          </Typography>
        </>
      );
    }
  }, [coachExperience]);

  // Return JSX
  return (
    <div className={styles.card}>
      <div className={styles.photo}>
        <img
          src={photo as string}
          className="img-fluid"
          alt="coach photo"
          onClick={openCoachDetails}
        />
        <span className={styles.bookmark} onClick={bookmarkCoachToggle}>
          {bookmark ? <BookmarkFilled /> : <BookmarkOutline />}
        </span>
      </div>
      <div className={styles.data}>
        <div className={styles.dataHeader}>
          <Typography
            as="span"
            className={styles.title}
            onClick={openCoachDetails}
          >
            {title}
          </Typography>
          {rating != 0 && rating ? (
            <div className={styles.rating}>
              <i>
                <Image
                  src={star}
                  className="img-fluid"
                  alt=""
                  width={16}
                  height={16}
                />
              </i>
              <Typography as="span">{rating || '0'}</Typography>
            </div>
          ) : null}
        </div>
        {designation && (
          <div className={styles.designation}>
            <WorkOutline />
            <Typography as="span">{designation}</Typography>
          </div>
        )}
        <div className={styles.coachExperience}>{displayCoachExperience}</div>
        <div className={styles.skills}>{skills}</div>
      </div>
    </div>
  );
};

export default Item;
