import styles from './style.module.scss';
import Paper from '@/ui/paper';
import dictionary from '@/dictionaries';
import CONFIG from '@/config';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '../home-banner/loading';
import EmptyMessage from '@/ui/empty-message';
import Item from './item/item';

const MeetYourPeers = () => {
  const isFetching = false;
  const peersList = [
    {
      id: 123456,
      name: '<PERSON><PERSON><PERSON> kdssdkd dsdks sfkdfds skdfldsk dkflskf kflskf',
      role: 'Director General',
    },
    {
      id: 2346789,
      name: '<PERSON>',
      role: 'Senior Project Manager',
    },
    {
      id: 5679876,
      name: '<PERSON>',
      role: 'Trainer ADSG',
    },
  ];

  const handleClick = () => {
    throw new Error('Function not implemented.');
  };

  return (
    <Paper
      title={dictionary.meetYourPeers}
      linkTitle={dictionary.viewAll}
      link={CONFIG.routes.meetYourPeers}
    >
      <WithLoader loader={<Loading />} loading={isFetching}>
        {peersList.length === 0 && !isFetching ? (
          <EmptyMessage
            icon="search"
            title={dictionary.noInformationAvailable}
          />
        ) : (
          <nav className={styles.list}>
            <ul>
              {peersList?.slice(0, 3)?.map((peer) => (
                <li key={peer.id}>
                  <Item
                    peerId={peer.id}
                    name={peer.name}
                    title={peer.role}
                    onClick={handleClick}
                  />
                </li>
              ))}
            </ul>
          </nav>
        )}
      </WithLoader>
    </Paper>
  );
};

export default MeetYourPeers;
