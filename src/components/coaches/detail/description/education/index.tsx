import Typography from '@/ui/typography';
import styles from './style.module.scss';
import { FC } from 'react';
import { Book } from '@/ui/icons';

interface IEducation {
  school: string;
  degree_name: string;
  logo: string;
  date_from: string;
  date_to: string;
  location: string;
}

const Education: FC<IEducation> = ({
  school,
  degree_name,
  logo,
  date_from,
  date_to,
  location,
}) => {
  return (
    <div className={styles.coachEducation}>
      <div className={styles.schoolLogo}>
        {logo ? (
          <img
            src={logo as string}
            alt="school logo"
            className={`img-fluid ${styles.photo}`}
          />
        ) : (
          <i>
            <Book />
          </i>
        )}
      </div>
      <div className={styles.educationDetails}>
        <Typography className={styles.schoolName}>{school}</Typography>
        <Typography className={styles.degreeName}>{degree_name}</Typography>
        <div className={styles.datesAndLocation}>
          <Typography as="span">{date_from}</Typography>-
          <Typography as="span">{date_to}</Typography>·
          <Typography as="span">{location}</Typography>
        </div>
      </div>
    </div>
  );
};

export default Education;
