@use 'mixins' as *;

.popup {
  width: size(745);
  height: size(595);
  background: var(--white);
  @include borderRadius(var(--radius));
  @include forMarginPadding(padding, size(24), size(24), size(24), size(24));
  position: fixed;
  top: 0;
  bottom: 0;
  @include leftToRight(0);
  @include rightToLeft(0);
  @include forMarginPadding(margin, auto, auto, auto, auto);
  z-index: 99;
  display: flex;
  flex-direction: column;
}

.header {
  .title {
    display: flex;
    align-items: center;
    width: 100%;
    @include forMarginPadding(margin, size(0), size(0), size(24), size(0));
    @include forMarginPadding(padding, size(0), size(50), size(0), size(0));
    min-height: size(48);

    h6 {
      @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
      font-size: size(20);
    }
  }
}

.close {
  position: absolute;
  @include rightToLeft(size(24));
  top: size(30);
  width: size(36);
  height: size(36);
  background: none;
  border: solid 1px var(--grey-700);
  display: flex;
  justify-content: center;
  align-items: center;
  @include borderRadius(50%);
  cursor: pointer;
  @include transitions(0.5s);
  fill: var(--grey-700);

  i {
    width: size(14);
    height: size(14);
    vertical-align: top;

    svg {
      vertical-align: top;
    }
  }
}

.photo {
  width: size(48);
  height: size(48);
  @include borderRadius(6px);
  @include forMarginPadding(margin, size(0), size(8), size(0), size(0));
  overflow: hidden;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.body {
  position: relative;
  height: size(418);
  @include forMarginPadding(padding, size(24), size(0), size(24), size(0));
  overflow-y: auto;
}

.postType {
  display: flex;
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
}

.type {
  border: none;
  font-size: size(14);
  display: inline-flex;
  justify-content: center;
  align-items: center;

  @include borderRadius(8px);
  @include forMarginPadding(padding, size(12), size(15), size(12), size(0));
  @include forMarginPadding(margin, size(0), size(6), size(16), size(0));
  color: var(--grey-900);

  &:last-child {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }

  .avatar {
    width: size(28);
    height: size(28);
    @include borderRadius(50%);
    @include forMarginPadding(margin, size(0), size(15), size(0), size(0));
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    justify-content: center;
    text-transform: uppercase;
    @include borderRadius(50%);
    background: var(--bg-shade-10);

    p {
      @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
      font-weight: 500;
      font-size: size(12);
    }

    &.question {
      background: var(--bg-shade-11);
    }

    &.popper {
      background: var(--bg-shade-12);
    }
  }

  i {
    width: size(12);
    height: size(12);

    svg {
      fill: var(--grey-900);
    }
  }
}

.textarea {
  font-size: size(16);
  border: none;
  background: none;
  resize: none;
  width: 100%;
  min-height: size(100);
  color: var(--grey-900);
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));

  &:focus {
    outline: none;
  }

  :placeholder {
    color: var(--grey-700);
  }
}

.media {
  overflow: hidden;
  position: relative;

  .close {
    @include rightToLeft(size(10));
    top: size(10);
    width: size(36);
    height: size(36);
    border-color: var(--white);
    fill: var(--white);
  }
}

.error {
  color: var(--danger-900);
  font-size: size(14);
  margin: 0;
  display: block;
  font-weight: 400;
}

.footer {
  .addPhoto {
    display: flex;
    justify-content: flex-start;
    @include leftToRight(0);
    @include forMarginPadding(margin, size(12), size(0), size(12), size(0));

    label {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: var(--grey-700);
      font-size: size(14);

      i {
        width: size(48);
        height: size(48);
        @include borderRadius(50%);
        display: flex;
        justify-content: center;
        align-items: center;
        background: var(--bg-shade-10);
        fill: var(--grey-900);
        @include forMarginPadding(margin, size(0), size(12), size(0), size(0));

        svg {
          width: size(24);
        }
      }
    }

    button {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: var(--grey-700);
      font-size: size(14);
      border: none;
      background-color: transparent;

      i {
        width: size(48);
        height: size(48);
        @include borderRadius(50%);
        display: flex;
        justify-content: center;
        align-items: center;
        background: var(--bg-shade-10);
        @include forMarginPadding(margin, size(0), size(12), size(0), size(0));
        fill: var(--grey-900);

        svg {
          width: size(24);
        }
      }
    }
  }

  .buttonWrapper {
    display: flex;
    justify-content: flex-end;
    @include forMarginPadding(margin, auto, size(0), size(0), size(0));
    @include forMarginPadding(padding, size(24), size(0), size(0), size(0));
    border-top: solid 1px var(--grey-400);
    min-height: size(81);

    button {
      width: auto;
    }
  }

  button[class*='dialog-form-action'] {
    &[disabled] {
      background: var(--grey-500) !important;
      color: var(--grey-700) !important;
    }
  }
}

@include hover() {
  .close {
    &:hover {
      background: var(--grey-900);
      border-color: var(--grey-900);
      fill: var(--white);
    }
  }

  .media {
    .close {
      &:hover {
        background: var(--white);
        border-color: var(--white);
        fill: var(--grey-900);
      }
    }
  }
}

@include for-all-phone() {
  .popup {
    width: 92%;
    height: size(595);
    @include forMarginPadding(padding, size(16), size(16), size(16), size(16));
  }

  .close {
    width: size(26);
    height: size(26);
    @include rightToLeft(size(16));
    top: size(16);

    i {
      width: size(10);
      height: size(10);
    }
  }

  .media {
    width: 100%;

    img {
      width: 100%;
    }

    .close {
      width: size(26);
      height: size(26);
      @include rightToLeft(size(16));
      top: size(16);
    }
  }
}
