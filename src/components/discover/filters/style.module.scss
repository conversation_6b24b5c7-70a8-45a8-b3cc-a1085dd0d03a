@use 'mixins' as *;

.filterBtn {
  background: none;
  border: none;
  display: block;
  color: var(--grey-900);
  @include forMarginPadding(margin, size(0), size(0), size(0), auto);
  cursor: pointer;

  i {
    width: size(30);
    height: size(30);
    display: block;

    svg {
      fill: var(--grey-900);
      fill-rule: evenodd;
      width: 100%;
    }
  }
}

.filterPopup {
  position: fixed;
  @include leftToRight(0);
  top: 0;
  width: 100%;
  height: 100%;
  @include forMarginPadding(margin, size(0), size(0), size(30), size(0));
  z-index: 99;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.wrapper {
  position: relative;
  width: size(579);
  height: 100%;
  overflow-y: auto;
  background: var(--white);
  @include forMarginPadding(padding, size(24), size(24), size(25), size(24));
  z-index: 999;
  display: flex;
  flex-direction: column;
}

.applyFilterButton {
  position: sticky;
  bottom: size(24);
  width: 100%;
  @include forMarginPadding(margin, auto, size(0), size(0), size(0));
}

.head {
  display: flex;
  flex-direction: column;
  position: relative;

  .top {
    display: flex;
    justify-content: flex-end;
    @include forMarginPadding(margin, size(0), size(0), size(24), size(0));
  }

  .bottom {
    display: flex;
    justify-content: space-between;
    @include forMarginPadding(margin, size(0), size(0), size(30), size(0));
  }
}

.resetBtn {
  background: none;
  color: var(--grey-900);
  font-size: size(16);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  i {
    width: size(20);
    height: size(20);
    display: block;
    @include forMarginPadding(margin, size(0), size(10), size(0), size(0));

    svg {
      fill: var(--grey-900);
      fill-rule: evenodd;
      width: 100%;
    }
  }

  span {
    display: inline;
    padding-bottom: 0;
    transition: all 0.5s linear;
    background: linear-gradient(
      to bottom,
      var(--grey-900) 0%,
      var(--grey-900) 98%
    );
    background-size: 0 1px;
    background-repeat: no-repeat;
    background-position: left 100%;

    @include rtl {
      background-position: right 100%;
    }
  }
}

.sep {
  opacity: 0.1;
  border-bottom: solid 1px var(--grey-900);
  height: 1px;
  width: 100%;
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));
}

.filter {
  + .filter {
    @include forMarginPadding(margin, size(20), size(0), size(0), size(0));
  }

  h4 {
    @include forMarginPadding(margin, size(0), size(0), size(20), size(0));
    @include forMarginPadding(padding, size(0), size(0), size(10), size(0));
  }

  ul {
    @include forMarginPadding(margin, size(0), size(0), size(24), size(0));
    @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
    display: flex;
    flex-wrap: wrap;
    animation: fadeIn 0.3s ease-in-out;

    li {
      list-style: none;
    }
  }
}

.closeFilter {
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  background: none;
  cursor: pointer;
  width: size(30);
  height: size(30);
  border: solid 1px var(--grey-700);
  fill: var(--grey-700);
  color: var(--grey-700);
  @include borderRadius(50%);
  display: flex;
  justify-content: center;
  align-items: center;
  @include transitions(0.5s);

  i {
    display: block;
    width: size(10);
    height: size(10);

    svg {
      fill-rule: evenodd;
      width: 100%;
      vertical-align: top;
    }
  }
}

@include hover {
  .resetBtn {
    &:hover {
      span {
        background-size: 100% 1px;
      }
    }
  }

  .closeFilter {
    &:hover {
      border-color: var(--grey-900);
      fill: var(--grey-900);
      color: var(--grey-900);
    }
  }
}

@include for-all-phone() {
  .wrapper {
    width: 100%;

    .filter {
      ul li {
        @include forMarginPadding(margin, size(0), size(8), size(8), size(0));
        button {
          margin-inline-end: 0;
          margin-bottom: 0;
        }
      }
    }
  }
}

@include for-dark-theme() {
  .modalSearch {
    border: size(1) solid rgba(255, 255, 255, 0.3490196078);
    input {
      background-color: transparent;
      outline: none;
    }
  }

  .modalItem.modalItem.notChecked {
    .checkmark {
      border: size(1) solid #ffffff66;
    }
  }
}

.filter {
  border-bottom: size(1) solid var(--grey-400);
}

.filterHeader {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: size(24) size(0) size(24);
  position: relative;

  .accordionAction {
    width: size(32);
    height: size(32);
    border: size(1) solid var(--grey-400);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out;

    svg {
      width: size(8);
      height: size(14);
      transform: rotate(-90deg);
      fill: var(--grey-900);
      transition: fill 0.3s ease-in-out;
    }

    &:hover {
      background-color: var(--grey-900);

      svg {
        fill: var(--white);
      }
    }
  }

  .accordionAction.openState {
    svg {
      transform: rotate(90deg);
    }
  }

  .accordionAction.closeState {
    svg {
      transform: rotate(-90deg);
    }
  }
}

.filterList {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: size(132);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.loadingContainer {
  height: size(40);

  & > div {
    min-height: size(20) !important;
    height: size(20) !important;
    max-height: size(20) !important;
    padding: 0 !important;
  }
}
