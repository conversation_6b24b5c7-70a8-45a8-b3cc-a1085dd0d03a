<svg width="1353" height="1256" viewBox="0 0 1353 1256" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_354_608)">
<ellipse cx="319.057" cy="488.755" rx="319.057" ry="488.755" transform="matrix(-0.641712 -0.766946 0.788243 -0.615364 495.948 1173.46)" fill="url(#paint0_linear_354_608)"/>
</g>
<defs>
<filter id="filter0_f_354_608" x="0.09375" y="0.254883" width="1352.74" height="1255.49" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="120" result="effect1_foregroundBlur_354_608"/>
</filter>
<linearGradient id="paint0_linear_354_608" x1="318.003" y1="530.69" x2="-26.4672" y2="955.838" gradientUnits="userSpaceOnUse">
<stop stop-color="#7DA1C4"/>
<stop offset="1" stop-color="#D5C8BE"/>
</linearGradient>
</defs>
</svg>
