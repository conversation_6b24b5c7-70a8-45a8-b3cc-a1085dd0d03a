import { AvailableSlots } from '@/types/domain/learning/course';
import Typography from '@/ui/typography';
import { FC } from 'react';
import styles from './style.module.scss';
import { useLocale } from 'next-intl';
import { format } from '@/utils/date';
import { CalendarRounded, Clock, Pin } from '@/ui/icons';
import moment from 'moment';
import { formatLocalTime } from '@/utils/time';
import Button from '@/ui/form/button';
import dictionary from '@/dictionaries';

interface ISlotProps {
  slot: AvailableSlots;
}

const Slot: FC<ISlotProps> = ({ slot }) => {
  const locale = useLocale();

  const formattedDate =
    slot.date && moment(slot.date, 'YYYY-MM-DD').isValid()
      ? format(slot.date as string, locale, 'YYYY-MM-DD', 'dddd DD MMM YYYY')
      : 'DD MMM YYYY';

  const localStartDate = formatLocalTime(slot.startdatetime);
  const localEndDate = formatLocalTime(slot.enddatetime);

  return (
    <div className={styles.item}>
      <Typography as="h5" className={styles.slotName}>
        {slot.slot}
      </Typography>
      <div className={styles.row}>
        <CalendarRounded />
        <Typography as="span">{formattedDate}</Typography>
      </div>
      <div className={styles.row}>
        <Clock />
        <Typography as="span">
          {localStartDate
            .locale(locale === 'ar' ? 'ar' : 'en')
            .format('h:mm a')}{' '}
          -{' '}
          {localEndDate.locale(locale === 'ar' ? 'ar' : 'en').format('h:mm a')}
        </Typography>
      </div>
      {slot.location && !Array.isArray(slot.location) && slot.location.room && (
        <div className={styles.row}>
          <Pin />
          <Typography as="span">{slot.location.room}</Typography>
        </div>
      )}
      {slot.location &&
        !Array.isArray(slot.location) &&
        slot.location.address && (
          <Button
            href={slot.location.address}
            fullLink
            dictionary={dictionary.getDirections}
            target="_blank"
            size="md"
          />
        )}
    </div>
  );
};

export default Slot;
