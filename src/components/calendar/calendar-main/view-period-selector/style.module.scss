@use 'mixins' as *;

.timePeriodContainer {
  position: relative;
}

.timePeriodButton {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  @include forMarginPadding(padding, size(7), size(13), size(7), size(13));
  gap: size(21);
  cursor: pointer;
  background-color: transparent;
  border: 1px solid #c5c5c580;
  border-radius: size(8);
  font-size: size(16);
  color: var(--gray-900);

  svg {
    width: size(24);
    height: size(24);
    path {
      fill: var(--grey-900);
    }
  }
}

.timePeriodButton:hover {
  border-color: var(--grey-800);
}

.timePeriodButton:focus {
  outline: none;
  border-color: var(--grey-800);
}

.optionsContainer {
  position: absolute;
  right: 0;
  border-radius: size(10);
  border: 1px solid var(--grey-600);
  background-color: var(--white);
  z-index: 2;
  outline: none;
  top: 100%;
  @include forMarginPadding(margin, size(16), size(0), size(0), size(0));
}

.menuItem {
  display: flex;
  align-items: center;
  cursor: pointer;
  background: transparent;
  border: none;
  font-size: size(16);
  color: var(--gray-900);
  @include forMarginPadding(
    padding,
    size(20.5),
    size(40),
    size(20.5),
    size(24)
  );

  &:not(:last-of-type) {
    border-bottom: 1px solid var(--grey-300);
  }
}

.selectedMenuItem {
  color: var(--blue-900);
}
