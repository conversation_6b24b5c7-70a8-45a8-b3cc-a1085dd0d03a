import { FC, useCallback } from 'react';
import Typography from '@/ui/typography';
import ChipButton from '@/ui/form/chip-button';
import IPopularSearches from '@/types/domain/learning/filters/popular';
import IRecentSearches from '@/types/domain/learning/filters/recent';
import { Circle } from '@/ui/icons';

// Styles
import styles from './style.module.scss';
import dictionary from '@/dictionaries';

interface ISection {
  title: string;
  list: IPopularSearches[] | IRecentSearches[];
  onClick: (keyword: string) => void;
  onClear?: () => void;
}

const Section: FC<ISection> = (props) => {
  const { title, list, onClick, onClear } = props;

  /**
   * Method to handle click on chip
   */
  const hanldeClick = useCallback(
    (keyword?: string) => {
      if (keyword) {
        onClick(keyword);
      }
    },
    [onClick],
  );

  return (
    list?.length > 0 && (
      <div className={styles.popularSearch}>
        <Typography as="h6">{title}</Typography>
        <nav className={styles.popularSearch}>
          <ul>
            {list.map((x, i) => (
              <li key={'popular-' + i}>
                <ChipButton
                  variant="rounded"
                  onClick={() => hanldeClick(x.keyword)}
                >
                  {x?.keyword}
                </ChipButton>
              </li>
            ))}
            {onClear && (
              <li>
                <button
                  type="button"
                  className={styles.clearBtn}
                  onClick={onClear}
                >
                  <i>
                    <Circle />
                  </i>
                  <Typography as="span" dictionary={dictionary.clear} />
                </button>
              </li>
            )}
          </ul>
        </nav>
      </div>
    )
  );
};

export default Section;
