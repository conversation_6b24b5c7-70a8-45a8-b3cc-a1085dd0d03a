import Image from 'next/image';
import Button from '@/ui/form/button';
import ICommunityDetails from '@/types/domain/my-network/communityDetails';
import useNetworkActionsStore from '@/stores/my-network/network-actions';
import useCommunityDetailsStore from '@/stores/my-network/community-details';
import Toast from '@/ui/toast';
import { useTranslations } from 'next-intl';

// Styles
import styles from './style.module.scss';
import dictionary from '@/dictionaries';
import useMyCommunitiesStore from '@/stores/my-network/my-communities';
interface IBannerProps {
  imageUrl: string | undefined;
  community: ICommunityDetails | null;
}
const Banner: React.FC<IBannerProps> = ({ imageUrl, community }) => {
  const isHidden = community?.visible != 1 ? true : false;
  const isRequested = community?.is_workspace_requested;
  const {
    fetchNetworkEnrollment,
    fetchLeaveCommunity,
    fetchCancelRequestedNetwork,
    fetchRequestToJoinNetwork,
    isFetching,
    toast,
    clearToast,
    setMessage,
    message,
  } = useNetworkActionsStore();
  const getCommunityDetails = useCommunityDetailsStore(
    (store) => store.getCommunityDetails,
  );
  const fetchCommunities = useMyCommunitiesStore((x) => x.fetchCommunities);
  const t = useTranslations();

  const joinLeaveAction = async () => {
    if (community?.is_member) {
      await fetchLeaveCommunity(community?.networkid as number);
      setMessage(t(dictionary.youHaveSuccessfullyLeftTheGroup));
    } else {
      await fetchNetworkEnrollment(community?.networkid as number);
      setMessage(t(dictionary.youHaveSuccessfullyJoinedTheGroup));
    }
    getCommunityDetails(community?.networkid as number);
    fetchCommunities();
  };

  const requestCancelAction = async () => {
    if (community?.is_workspace_requested) {
      await fetchCancelRequestedNetwork(community?.networkid as number);
      setMessage(t(dictionary.requestedNetworkCancelledSuccessfully));
    } else {
      await fetchRequestToJoinNetwork(community?.networkid as number);
      setMessage(t(dictionary.networkRequestedSuccessfully));
    }
    getCommunityDetails(community?.networkid as number);
  };

  const JoinLeaveButton = () => {
    return (
      <Button
        onClick={joinLeaveAction}
        loading={isFetching}
        dictionary={community?.is_member ? dictionary.leave : dictionary.join}
      />
    );
  };

  const RequestCancelButton = () => {
    return (
      <Button
        onClick={
          community?.private == 0 ? joinLeaveAction : requestCancelAction
        }
        loading={isFetching}
        dictionary={
          isRequested
            ? dictionary.cancelTheRequest
            : community?.private == 0
              ? dictionary.join
              : dictionary.requestToJoin
        }
      />
    );
  };

  const clearMessage = () => {
    clearToast();
  };

  return (
    <>
      <div className={styles.banner}>
        {imageUrl && (
          <Image
            src={imageUrl as string}
            alt=""
            width={0}
            height={0}
            sizes="100vw"
            style={{ width: '100%', height: 'auto' }}
          />
        )}
        <div className={styles.btnWrap}>
          {community?.is_member ? (
            <JoinLeaveButton />
          ) : (
            !isHidden && <RequestCancelButton />
          )}
        </div>
      </div>
      {toast && (
        <Toast
          text={toast.type === 'success' ? (message as string) : toast.message}
          type={toast.type}
          onClose={clearMessage}
        />
      )}
    </>
  );
};

export default Banner;
