import { FC, useMemo } from 'react';
import Typography from '@/ui/typography';
import Button from '@/ui/form/button';
import styles from './style.module.scss';
import Slots from './slots';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';
import SelectedInfo from './selectedInfo';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';
import usePhysicalCourseStore from '@/stores/learning/courses/physical';
import { AvailableSlots } from '@/types/domain/learning/course';

interface IBooking {
  list: AvailableSlots[];
  handleBooking: () => void;
  managerApprovalRequired?: boolean;
}

// React component to handle coach booking
const Booking: FC<IBooking> = (props) => {
  // Deconstructing props
  const { list, handleBooking, managerApprovalRequired } = props;

  const lineManagerCheckLoading = usePhysicalCourseStore((x) => x.isFetching);
  const selected = usePhysicalCourseStore((x) => x.selected);
  const isBooking = usePhysicalCourseStore((x) => x.isBooking);

  const t = useTranslations();
  // Method to handle the days
  const days = useMemo(() => {
    // Property to hold the unique days
    const unique: string[] = [];
    // Filtring the unique days
    list.map((slot) => {
      if (slot?.date && !unique.includes(slot.date)) {
        unique.push(slot.date);
      }
    });
    // Returning the unique days
    return unique;
  }, [list]);

  // Return JSX
  return (
    <>
      <div className={styles.body}>
        <WithLoader loader={<Loading />} loading={lineManagerCheckLoading}>
          <nav className={styles.calendar}>
            <ul>
              <li>
                <div className={styles.left}>
                  <Typography as="h5" dictionary={dictionary.date} />
                </div>
                <div className={styles.data}>
                  <Typography
                    as="h5"
                    dictionary={dictionary.availableTimeSlot}
                  />
                </div>
              </li>
              {days.map((x) => (
                <Slots key={x} date={x} list={list} />
              ))}
            </ul>
          </nav>
          {selected && selected.id && <SelectedInfo />}
        </WithLoader>
      </div>
      <div className={styles.footer}>
        <Button
          type="button"
          color="black"
          disabled={!selected?.id}
          loading={isBooking}
          onClick={handleBooking}
          className={styles.innerBookCta}
        >
          {managerApprovalRequired
            ? t(dictionary.requestSlot)
            : t(dictionary.confirmSlot)}
        </Button>
      </div>
    </>
  );
};

export default Booking;
