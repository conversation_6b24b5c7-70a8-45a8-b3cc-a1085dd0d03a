@use 'mixins' as *;

.agendaItemsWrapper {
  //margin-top: size(16);
  max-height: calc(size(1051 - 220));
  overflow-y: auto;

  @media (min-width: 576px) {
    &::-webkit-scrollbar-track {
      background-color: var(--white);
    }

    &::-webkit-scrollbar {
      width: size(6);
      background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: size(6);
      background-color: var(--grey-900);
    }
  }
}

.title {
  color: var(--grey-900);
  font-weight: 700;
  padding-bottom: size(30);
}

.groupWrapper {
}

.groupHeader {
  margin-bottom: size(12);
}

.groupDay {
  color: var(--grey-800);
  font-weight: 700;
  @include forMarginPadding(padding, size(0), size(4), size(0), size(0));
  text-transform: uppercase;
}

.eventWrapper {
  display: flex;
  gap: size(8);
  border-bottom: 1px solid var(--grey-400);
  margin-bottom: size(15);
  padding-bottom: size(15);
  margin-top: size(15);
  .eventImage {
    min-height: size(64);
    max-height: size(64);
    min-width: size(64);
    max-width: size(64);

    img {
      height: 100%;
      width: 100%;
      border-radius: size(6);
      object-fit: cover;
    }
  }
  .eventInfo {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }
  &:last-child {
    margin-bottom: size(0);
  }
}

.eventBullet {
  &:before {
    background-color: var(--text-link-blue);
    content: '';
    display: block;
    border-radius: 50%;
    height: size(12);
    width: size(12);
  }
}

.dateRange {
  display: flex;
}

.dateRangeValue {
  color: var(--grey-700);
  font-size: size(12);
}

.eventName {
  font-size: size(16);
  font-weight: 400;
  margin-bottom: size(10);
}
.eventDate {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  gap: size(4);

  .date {
    font-weight: 400;
    font-size: size(12);
    color: var(--grey-600);
  }
  svg {
    width: size(16);
    height: size(16);
    path {
      color: var(--grey-600);
      stroke: var(--grey-600);
    }

    margin-right: size(4);
  }
}
