import React, { useCallback } from 'react';
import useTimelineView from '@/hooks/calendar/useTimelineView';
import IFormatedEvent from '@/types/domain/calendar/formated-event';
import moment from 'moment';
import { Pin, CalendarTimeSlot, ClockFrame } from '@/ui/icons';
import { useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';
import styles from './style.module.scss';
import Typography from '@/ui/typography'; // Import styles here

type TimePeriod = 'daily' | 'weekly' | 'monthly';

interface TimelineViewProps {
  selectedDate: Date;
  events: IFormatedEvent[];
  selectedTimePeriod: TimePeriod;
  onDateSelect: (date: Date) => void;
  onShowEventDetails: (event: IFormatedEvent) => void;
  isMyCalendar?: boolean;
}

const TimelineView: React.FC<TimelineViewProps> = ({
  selectedDate,
  events,
  selectedTimePeriod,
  onDateSelect,
  onShowEventDetails,
  isMyCalendar,
}) => {
  const t = useTranslations();

  const renderCalendarEvent = useCallback(
    (event: IFormatedEvent, viewPeriod: string) => {
      const eventMoment = moment(event.startDateTime);
      const isPastEvent = eventMoment.isBefore(moment());
      return (
        <div
          className="calendar-list-event-item"
          onClick={
            viewPeriod === 'monthly'
              ? () => onShowEventDetails(event)
              : undefined
          }
        >
          <div className="event-details-wrapper">
            <div className="event-image">
              {event.image ? <img src={event.image} alt={event.text} /> : ''}
            </div>
            <div className="event-content">
              <h6 className="event-name">
                {event.coursename}
                <span> - {t(dictionary.course)}</span>
                {!isMyCalendar && viewPeriod === 'monthly' && (
                  <div className="booking-status ">
                    <div className={`tag`}>
                      {t(dictionary.REGISTRATIONOPEN)}
                    </div>
                  </div>
                )}
              </h6>
              <div className="event-details">
                <span className="event-date">
                  <CalendarTimeSlot />
                  {moment(event.startDateTime).format('D MMMM, YYYY')}
                </span>
                <span className="event-time">
                  <ClockFrame />
                  {event.startTime} - {event.endTime}
                </span>
                {event.eventlocation?.address && (
                  <span className="event-location">
                    {isMyCalendar ? (
                      <>
                        <Pin />
                        {event.eventlocation.address}
                      </>
                    ) : (
                      'Organization'
                    )}
                  </span>
                )}
              </div>
              {!isMyCalendar && viewPeriod !== 'monthly' && !isPastEvent && (
                <div className="booking-status ">
                  <div className={`tag`}>{t(dictionary.REGISTRATIONOPEN)}</div>
                </div>
              )}
            </div>
          </div>
          {viewPeriod !== 'monthly' && !isPastEvent && (
            <button
              className={`event-actions ${isMyCalendar ? 'in-my-calendar' : event.status && event.status.toLowerCase() === 'booked' ? 'enrolled' : 'open-to-enroll'}`}
              onClick={() => {
                onShowEventDetails(event);
              }}
            >
              {isMyCalendar
                ? t(dictionary.viewDetails)
                : event.status && event.status.toLowerCase() === 'booked'
                  ? t(dictionary.enrolled)
                  : t(dictionary.enroll)}
            </button>
          )}
        </div>
      );
    },
    [isMyCalendar, onShowEventDetails, t],
  );

  const listContent = useTimelineView({
    month: selectedDate.getMonth(),
    year: selectedDate.getFullYear(),
    day: selectedDate.getDate(),
    selectedDate: selectedDate,
    events: events,
    activePeriod: selectedTimePeriod,
    onDateSelect: onDateSelect,
    renderEvent: renderCalendarEvent,
  });
  const timePeriodClass = `${selectedTimePeriod}View`;

  return (
    <>
      {!isMyCalendar && (
        <Typography as="h4" dictionary={dictionary.discoverEvents} />
      )}

      <div
        className={`${styles.timeline} ${styles[timePeriodClass]}`} // Apply classes here
        id={selectedTimePeriod} // Keep id here
      >
        {listContent}
      </div>
    </>
  );
};

export default TimelineView;
