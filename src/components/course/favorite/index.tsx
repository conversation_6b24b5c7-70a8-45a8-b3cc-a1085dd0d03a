import { FC, useCallback, useMemo, useState } from 'react';
import cn from 'classnames';
import ICourse from '@/types/domain/learning/course';
import styles from './style.module.scss';
import useFavoriteCoursesStore from '@/stores/learning/courses/favorite';
import useFavoritesCoursesStore from '@/stores/learning/pathways/favorites';
import { HeartFill, HeartOutline } from '@/ui/icons';
import Spinner from '@/ui/spinner';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';

interface IFavorite {
  course: ICourse;
  className?: string;
}

// React component to handle course favorite action
const Favorite: FC<IFavorite> = (props) => {
  const [isActiveButton, setIsActiveButton] = useState<boolean>(false);
  // Deconstructing props
  const { course, className } = props;

  // Getting favorite courses
  const favorites = useFavoriteCoursesStore((x) => x.list);

  // Getting method to toggle favorite courses
  const onToggleFavorite = useFavoriteCoursesStore((x) => x.toggle);
  const onFetchFavorites = useFavoritesCoursesStore((x) => x.fetchFavorites);

  const t = useTranslations();

  // Getting course type
  const type = useMemo(() => {
    return course?.component_type?.toString() || course?.type?.toString();
  }, [course?.component_type, course?.type]);

  // Getting course id
  const id = useMemo(() => {
    return (
      course?.id ||
      course?.courseid ||
      course?.courseId ||
      0
    )?.toString();
  }, [course?.id, course?.courseid, course?.courseId]);

  // Active state
  const isActive = useMemo(() => {
    return favorites.findLastIndex((x) => x.courseid === parseInt(id)) > -1;
  }, [favorites, id]);

  // Method to handle onClick event
  const onClick = useCallback(async () => {
    setIsActiveButton(true);
    // Toggle favorite
    await onToggleFavorite({
      id: id as string,
      action: isActive ? 'remove' : 'add',
    });
    onFetchFavorites({ type: 'personal' });
    setIsActiveButton(false);
  }, [id, isActive, onToggleFavorite, setIsActiveButton, onFetchFavorites]);

  // Creating classnames
  const classNames = cn({
    [styles.button]: true,
    [className || '']: className,
    [styles.active]: isActive,
  });

  // Return null if type is Program
  if (type === 'Program' || type === 'program') {
    return null;
  }

  // Return JSX
  return (
    <>
      {!isActiveButton ? (
        <button
          type="button"
          className={classNames}
          title={isActive ? t(dictionary.remove) : t(dictionary.Add)}
          onClick={onClick}
        >
          <i>{isActive ? <HeartFill /> : <HeartOutline />}</i>
        </button>
      ) : (
        <Spinner />
      )}
    </>
  );
};

// Exporting Favorite component
export default Favorite;
