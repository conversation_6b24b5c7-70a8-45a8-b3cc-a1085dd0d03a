@use 'mixins' as *;

.list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: size(24);
  counter-reset: section;
}

.card {
  border: solid 1px var(--grey-400);
  @include forMarginPadding(padding, size(14), size(14), size(14), size(14));
  position: relative;
  @include borderRadius(var(--radius));
}

.data {
  @include forMarginPadding(padding, size(0), size(50), size(0), size(0));

  .title {
    @include forMarginPadding(margin, size(0), size(0), size(10), size(0));
    font-weight: 700;
    display: block;
  }

  .content {
    font-size: size(14);
    display: block;
    color: var(--grey-800);
  }
}

.externalLink {
  background: none;
  border: 0;
  cursor: pointer;
  position: absolute;
  @include rightToLeft(size(16));
  top: size(16);
  @include forMarginPadding(margin, auto, auto, auto, auto);
  width: size(15);
  height: size(15);
  text-decoration: none;

  i {
    width: 100%;
    height: 100%;

    svg {
      fill: var(--grey-900);
      width: 100%;
      height: 100%;
    }
  }
}

.lessonCta {
  font-size: size(16);
  @include forMarginPadding(margin, size(16), size(0), size(0), size(0));
}

@include for-all-phone() {
  .list {
    display: block;

    .card {
      + .card {
        @include forMarginPadding(margin, size(16), size(0), size(0), size(0));
      }
    }
  }
}
