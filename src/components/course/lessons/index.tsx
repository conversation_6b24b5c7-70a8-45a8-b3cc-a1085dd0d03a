import { FC, ReactNode, useMemo } from 'react';
import ICourse from '@/types/domain/learning/course';
import Typography from '@/ui/typography';
import { Lesson } from '@/ui/icons';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';

interface ILessons {
  course: ICourse;
  children?: ReactNode;
  icon?: boolean;
}

// React component to handle course link
const Lessons: FC<ILessons> = (props) => {
  // Deconstructing props
  const { course, icon = true } = props;

  const t = useTranslations();

  // Getting course type
  const lesson = useMemo(() => {
    return (
      course?.total_lesson ||
      course?.total_lessons ||
      course?.total_course ||
      course?.lessons ||
      course?.activity_count ||
      ''
    );
  }, [
    course?.total_lesson,
    course?.total_lessons,
    course?.total_course,
    course?.lessons,
    course?.activity_count,
  ]);

  // Getting course type
  const activity = useMemo(() => {
    return Number(course?.activity_count) || Number(course?.total_course) || '';
  }, [course?.activity_count, course?.total_course]);

  // Getting course type
  const content = useMemo(() => {
    if (
      (course.component_type?.toLowerCase() === 'course' ||
        course.type?.toLowerCase() === 'course') &&
      Number(lesson)
    ) {
      return (
        lesson +
        ` ${Number(lesson) <= 1 ? t(dictionary.activity) : t(dictionary.activities)}`
      );
    }

    if (
      (course.component_type?.toLowerCase() === 'program' ||
        course.type?.toLowerCase() === 'program') &&
      Number(activity)
    ) {
      return (
        activity +
        ` ${Number(activity) <= 1 ? t(dictionary.course) : t(dictionary.courses)}`
      );
    }

    if (course.rating && course.rating > 0) {
      return '-';
    }

    return '';
  }, [course.component_type, course.type, course.rating, lesson, activity, t]);

  // Return JSX
  return (
    <>
      {icon &&
        (content ? (
          <i>
            <Lesson />
          </i>
        ) : null)}
      {content ? <Typography as="span">{content}</Typography> : null}
    </>
  );
};

// Exporting course component
export default Lessons;
