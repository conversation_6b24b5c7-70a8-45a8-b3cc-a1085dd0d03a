import { NextRequest, NextResponse } from 'next/server';
import { ExternalReferCourseService } from '@/services/external/refer-course';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';

// To handle a GET request to /api
export async function POST(request: NextRequest) {
  try {
    // Getting request params
    const data = await request.json();
    // Fetching Refer Course
    const response = await ExternalReferCourseService(data);
    // Return the response
    return NextResponse.json({
      refer: response?.data,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error as AxiosError);
  }
}
