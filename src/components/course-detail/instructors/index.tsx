import { FC } from 'react';
import { IInstructor } from '@/types/domain/learning/course';
import Typography from '@/ui/typography';
import Item from './item';

// Styles
import styles from './style.module.scss';
import EmptyMessage from '@/ui/empty-message';
import dictionary from '@/dictionaries';

interface IInstructorProps {
  list?: IInstructor[];
}

const Instructors: FC<IInstructorProps> = (props) => {
  const { list = [] } = props;

  return (
    <div className={styles.prerequisites}>
      <Typography as="h4" dictionary={dictionary.instructors} />
      {list && list.length > 0 ? (
        <nav>
          <ul className={styles.list}>
            {list.map((x, i) => (
              <li key={x.name + i}>
                <Item
                  image={x.image}
                  title={x.name}
                  designation={x.designation}
                />
              </li>
            ))}
          </ul>
        </nav>
      ) : (
        <EmptyMessage icon="search" title={dictionary.noInformationAvailable} />
      )}
    </div>
  );
};

export default Instructors;
