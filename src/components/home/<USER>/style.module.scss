@use 'mixins' as *;

.box {
  background: var(--white);
  position: relative;
  @include forMarginPadding(padding, size(80), size(0), size(0), size(0));

  h4 {
    @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
  }

  p {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }
}

.calendar {
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
}

.calendarIcon {
  top: 0;
  width: size(48);
  height: size(48);
  display: block;

  svg {
    fill: var(--grey-900);
  }
}

.icon {
  top: 0;
  width: size(48);
  height: size(48);
  overflow: hidden;
  position: absolute;
  @include borderRadius(50%);
  @include leftToRight(0);
}

.right {
  position: absolute;
  @include rightToLeft(0);
  top: size(10);

  ul {
    @include forMarginPadding(margin, size(8), size(0), size(0), size(0));
    @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
    display: flex;
    align-items: center;

    li {
      list-style: none;
      width: size(24);
      height: size(24);
      @include forMarginPadding(margin, size(0), size(20), size(0), size(0));

      &:last-child {
        @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
      }

      a {
        background: none;
        border: none;
        width: 100%;
        height: 100%;
        position: relative;

        .noti {
          position: absolute;
          @include rightToLeft(size(-8));
          top: size(-18);
          width: size(16);
          height: size(16);
          font-size: size(12);
          font-weight: 400;
          color: var(--white);
          background: var(--danger-700);
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        svg {
          fill: var(--grey-900);
        }
      }
    }
  }
}
