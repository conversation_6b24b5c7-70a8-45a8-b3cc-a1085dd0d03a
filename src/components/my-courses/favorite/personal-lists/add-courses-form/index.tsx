import React from 'react';

import Typography from '@/ui/typography';
import Button from '@/ui/form/button';
import PersonalListFormCard from '../personal-list-form-card';
import usePersonalListsStore from '@/stores/learning/personal-lists';
import { useTranslations } from 'next-intl';
import dictionary from '@/dictionaries';
import useFavoritesCoursesStore from '@/stores/learning/pathways/favorites';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/my-courses/loading';
import styles from './style.module.scss';

const AddCoursesForm = () => {
  const { favoritesList, isFetchingFavorites } = useFavoritesCoursesStore();

  const { addCourses, addedCourses, setAddListFormState, listName, addList } =
    usePersonalListsStore();

  const t = useTranslations();

  const [isSaving, setIsSaving] = React.useState(false);

  const handleSaveListWithCourses = async () => {
    if (addedCourses.length === 0) {
      console.warn('No courses selected. Save button should be disabled.');
      return;
    }

    setIsSaving(true);
    try {
      const newListId = await addList(listName);

      if (newListId) {
        await addCourses(newListId, addedCourses);
        setAddListFormState('inspect-list');
      } else {
        console.error('Failed to get a new list ID after creating the list.');
      }
    } catch (error) {
      console.error('Error saving list and courses:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const overallLoading = isFetchingFavorites || isSaving;

  return (
    <WithLoader loader={<Loading />} loading={overallLoading}>
      <Typography
        className={styles.dialogDrawerTitle}
        dictionary={dictionary.createNewList}
      />
      <Typography as="h3" dictionary={dictionary.addCourses} />
      <Typography
        as="span"
        className={styles.dialogDrawerSubtitle}
        dictionary={dictionary.chooseListCourses}
      />
      <div className="list">
        {favoritesList.map((course, i) => (
          <PersonalListFormCard
            course={course}
            variant="add"
            key={course.courseId ? `${course.courseId}` : i}
          />
        ))}
      </div>
      <Button
        className="dialog-form-action"
        disabled={!addedCourses.length || overallLoading}
        onClick={handleSaveListWithCourses}
      >
        {t(dictionary.save)}
      </Button>
    </WithLoader>
  );
};

export default AddCoursesForm;
