@use 'mixins' as *;

.assistantTrigger {
  animation: trigger-gradient 10s ease infinite;
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 200% 200%;
  border-radius: size(50);
  border: 0;
  bottom: size(40);
  cursor: pointer;
  filter: blur(2px);
  height: size(40);
  outline: 0px solid var(--white);
  position: fixed;
  transition: 0.2s all ease-in-out;
  right: size(40);
  width: size(40);
  z-index: 1;

  &:hover {
    filter: blur(0px);
    outline: 5px solid var(--white);
  }

  @include for-all-phone() {
    bottom: size(100);
  }

  @include rtl {
    left: size(40);
    right: auto;
  }
}

@include for-dark-theme {
  .assistantTrigger {
    &:hover {
      outline: 5px solid var(--cardBg);
    }
  }
}

@keyframes trigger-gradient {
  0% {
    background-position: 0% 50%;
    transform: rotate(0deg);
  }
  50% {
    background-position: 100% 50%;
    transform: rotate(120deg);
  }
  100% {
    background-position: 0% 50%;
    transform: rotate(0deg);
  }
}
