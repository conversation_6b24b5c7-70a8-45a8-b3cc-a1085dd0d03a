import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraGetPersonalListCoursesService } from '@/services/totara/personal-lists';

export async function GET(request: NextRequest) {
  const id = request.nextUrl.searchParams.get('id') || '';

  try {
    const response = await TotaraGetPersonalListCoursesService(id);

    return NextResponse.json({
      listCourses: response?.data.courses || null,
    });
  } catch (error) {
    return ErrorResponse(error);
  }
}
