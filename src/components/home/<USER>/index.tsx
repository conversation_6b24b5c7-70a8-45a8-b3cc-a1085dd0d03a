import { FC } from 'react';
import GreetingsPanel from '@/components/common/greetings-panel';
import MyStreaks from '@/components/common/my-streaks';

interface ISidebar {
  hasStreaks?: boolean;
  hasGreetings?: boolean;
}

const Sidebar: FC<ISidebar> = ({ hasGreetings = true, hasStreaks = true }) => {
  // Return JSX
  return (
    <>
      {hasGreetings && <GreetingsPanel />}
      {hasStreaks && <MyStreaks />}
    </>
  );
};

export default Sidebar;
