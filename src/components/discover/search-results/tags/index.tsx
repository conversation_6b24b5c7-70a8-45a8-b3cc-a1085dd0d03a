import { useCallback, useLayoutEffect, useMemo, useRef, useState } from 'react';
import useSearchCoursesStore from '@/stores/learning/courses/search';
import Item from './item';
import useSearchCourses from '@/hooks/helpers/search/course/useSearchCourses';
import styles from './style.module.scss';
import { convertBackDurationFilter } from '@/utils/helpers';
import { useLocale, useTranslations } from 'next-intl';
import { LeftArrow, RightArrow } from '@/ui/icons';

const Tags = () => {
  const { removeQueryParam, updateQueryParam } = useSearchCourses();
  const filters = useSearchCoursesStore((x) => x.filters);
  const { learningType, creator, duration } = filters;
  const setFilter = useSearchCoursesStore((x) => x.setFilter);

  const t = useTranslations();
  const locale = useLocale();
  const isRTL = locale === 'ar';

  const tagListRef = useRef<HTMLUListElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(false);

  const removeSimpleFilter = useCallback(
    (filterKey: 'learningType' | 'duration') => {
      setFilter({ key: filterKey, value: null });
      setFilter({ key: 'page', value: 1 });
      removeQueryParam(filterKey);
    },
    [setFilter, removeQueryParam],
  );

  const creatorList = useMemo(() => {
    if (!creator) return [];
    if (Array.isArray(creator)) return creator;
    if (typeof creator === 'string') {
      try {
        const parsed = JSON.parse(creator);
        if (Array.isArray(parsed)) {
          return parsed;
        }
      } catch (e) {
        console.error('Creator filter is a malformed string:', creator, e);
        return [];
      }
    }
    return [];
  }, [creator]);

  const removeCreator = useCallback(
    (creatorToRemove: string) => {
      const newCreators = creatorList.filter(
        (item) => item !== creatorToRemove,
      );
      const newFilters = {
        ...filters,
        creator: newCreators.length > 0 ? newCreators : undefined,
        page: 1,
      };

      setFilter({ key: 'creator', value: newFilters.creator });
      setFilter({ key: 'page', value: 1 });
      if (newCreators?.length === 0) {
        removeQueryParam('creator');
      } else {
        updateQueryParam('creator', JSON.stringify(newFilters.creator ?? []));
      }
    },
    [filters, setFilter, updateQueryParam, removeQueryParam, creatorList],
  );

  const tagsList = useMemo(() => {
    const tags = [];
    if (learningType) {
      tags.push(learningType);
    }
    if (creatorList.length > 0) {
      tags.push(...creatorList);
    }
    if (duration) {
      tags.push(convertBackDurationFilter(duration, t('hour'), t('hours')));
    }
    return tags;
  }, [learningType, creatorList, duration, t]);

  const handleScroll = useCallback(() => {
    if (tagListRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = tagListRef.current;
      const tolerance = 2;

      if (isRTL) {
        setShowRightArrow(scrollLeft < -tolerance);
        setShowLeftArrow(
          Math.abs(scrollLeft) + clientWidth < scrollWidth - tolerance,
        );
      } else {
        setShowLeftArrow(scrollLeft > tolerance);
        setShowRightArrow(scrollLeft + clientWidth < scrollWidth - tolerance);
      }
    }
  }, [isRTL]);

  useLayoutEffect(() => {
    const listElement = tagListRef.current;
    if (!listElement) return;

    handleScroll();

    const resizeObserver = new ResizeObserver(() => {
      handleScroll();
    });

    resizeObserver.observe(listElement);

    return () => {
      resizeObserver.unobserve(listElement);
    };
  }, [tagsList, handleScroll]);

  const scrollLeftHandler = useCallback(() => {
    tagListRef.current?.scrollBy({
      left: -300,
      behavior: 'smooth',
    });
  }, []);

  const scrollRightHandler = useCallback(() => {
    tagListRef.current?.scrollBy({
      left: 300,
      behavior: 'smooth',
    });
  }, []);

  if (tagsList.length === 0) {
    return null;
  }

  return (
    <div className={styles.filterTags}>
      <nav>
        {showLeftArrow && (
          <button
            className={`${styles.scrollArrow} ${styles.scrollArrowLeft}`}
            onClick={scrollLeftHandler}
          >
            <LeftArrow />
          </button>
        )}
        <ul ref={tagListRef} onScroll={handleScroll}>
          {learningType && (
            <Item
              title={learningType}
              onClick={() => removeSimpleFilter('learningType')}
            />
          )}
          {creatorList.map((creatorName) => (
            <Item
              key={creatorName}
              title={creatorName}
              onClick={() => removeCreator(creatorName)}
            />
          ))}
          {duration && (
            <Item
              title={convertBackDurationFilter(duration, t('hour'), t('hours'))}
              onClick={() => removeSimpleFilter('duration')}
            />
          )}
        </ul>
        {showRightArrow && (
          <button
            className={`${styles.scrollArrow} ${styles.scrollArrowRight}`}
            onClick={scrollRightHandler}
          >
            <RightArrow />
          </button>
        )}
      </nav>
    </div>
  );
};

export default Tags;
