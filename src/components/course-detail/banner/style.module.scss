@use 'mixins' as *;

.banner {
  @include borderRadius(var(--radius));
  width: 100%;
  height: size(386);
  overflow: hidden;
  position: relative;
  @include forMarginPadding(margin, size(0), size(0), size(30), size(0));
}

.bg {
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  width: 100%;
  height: 100%;
}

.menuWrap {
  position: absolute;
  @include rightToLeft(size(24));
  top: size(24);
  display: flex;
  align-items: center;
  justify-content: flex-end;

  > div[class*='container'] {
    > button[class*='button'] {
      @include forMarginPadding(margin, size(0), size(0), size(0), size(10));

      &:before {
        position: absolute;
        @include rightToLeft(0);
        @include leftToRight(0);
        @include forMarginPadding(margin, auto, auto, auto, auto);
        top: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        background: var(--white);
        opacity: 0.5;
        @include transitions(0.5s);
        content: '';
        display: block;
      }
    }
  }

  button {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }

  svg[class*='spinner'] {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    width: size(42) !important;
    height: size(42) !important;
    padding: size(10);
  }
}

.menuList {
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));

  li {
    list-style: none;
    border-bottom: solid 1px var(--cool-grey-700);

    &:last-child {
      border: none;
    }

    button {
      border: none;
      background: none;
      @include forMarginPadding(
        padding,
        size(12),
        size(12),
        size(12),
        size(12)
      );
      line-height: 1;
      display: flex;
      align-items: center;
      width: 100%;
      cursor: pointer;

      &:hover {
        background-color: var(--cool-grey-500);
        transition: 0.2s all ease-in-out;
      }

      i {
        width: size(24);
        height: size(24);
        @include forMarginPadding(margin, size(0), size(10), size(0), size(0));

        &.plus {
          background: var(--grey-900);
          @include borderRadius(50%);
          display: flex;
          justify-content: center;
          align-items: center;

          svg {
            width: size(11);
            height: size(11);
            fill: var(--white);
          }
        }

        svg {
          fill: var(--grey-900);
          fill-rule: evenodd;
        }
      }

      span {
        font-size: size(14);
        display: inline;
        padding-bottom: 0;
        transition: all 0.5s linear;
        background: linear-gradient(
          to bottom,
          var(--grey-900) 0%,
          var(--grey-900) 98%
        );
        background-size: 0 1px;
        background-repeat: no-repeat;
        background-position: left 100%;

        @include rtl {
          background-position: right 100%;
        }
      }
    }
  }
}

.favoritebb {
  position: relative;
  @include borderRadius(50%);
  display: flex;
  justify-content: center;
  align-items: center;
  width: size(42) !important;
  height: size(42) !important;
  border: none;
  cursor: pointer;
  @include transitions(0.5s);
  fill: var(--grey-900);
  overflow: hidden;
  background: none;

  &:before {
    position: absolute;
    @include rightToLeft(0);
    @include leftToRight(0);
    @include forMarginPadding(margin, auto, auto, auto, auto);
    top: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: var(--white);
    opacity: 0.5;
    @include transitions(0.5s);
    content: '';
  }

  i {
    width: size(24);
    height: size(24);
    position: relative;
    z-index: 2;

    svg {
      vertical-align: top;
    }
  }
}

div.bannerWrapper {
  min-height: size(386);
}

@include hover() {
  .menuList {
    li {
      button {
        &:hover {
          span {
            background-size: 100% 1px;
          }
        }
      }
    }
  }
}

@include for-all-phone() {
  .banner {
    height: size(245);
    @include forMarginPadding(margin, size(0), size(0), size(16), size(0));
  }

  div.bannerWrapper {
    min-height: size(245);
  }
}

@include for-dark-theme() {
  .menuList {
    background-color: var(--grey-300);

    li {
      border-bottom: solid 1px var(--grey-400);

      &:last-child {
        border: none;
      }

      button {
        &:hover {
          background-color: #141414;
        }
      }
    }
  }
}
