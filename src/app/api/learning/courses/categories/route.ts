import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchCoursesCategoriesService } from '@/services/totara/courses';

/**
 * Route to get the announcements
 */
export async function GET() {
  try {
    // Fetching records counts
    const response = await TotaraFetchCoursesCategoriesService();

    // Return the response
    return NextResponse.json({
      list: response?.data?.categories || [],
      total: response?.data?.total_category || 0,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
