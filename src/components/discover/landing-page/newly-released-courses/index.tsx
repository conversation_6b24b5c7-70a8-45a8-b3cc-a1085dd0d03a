import { useEffect, useMemo } from 'react';
import CardsSection from '@/components/common/cards-section';
import CONFIG from '@/config';
import useNewCoursesStore from '@/stores/learning/courses/new';
import Course from '@/components/course';
import EmptyMessage from '@/ui/empty-message';
import { useTranslations } from 'next-intl';
import dictionaries from '@/dictionaries';
import dictionary from '@/dictionaries';

const NewlyReleasedCourses = () => {
  // Getting loading state
  const isFetching = useNewCoursesStore((x) => x.isFetching);

  // Getting fetch action
  const onFetch = useNewCoursesStore((x) => x.fetch);

  // Getting categories
  const list = useNewCoursesStore((x) => x.list);
  const t = useTranslations();
  // Creating dyanmic array of cards
  const items = useMemo(() => {
    // Mapping list to create cards
    return (
      list?.map((item) => (
        <Course key={item.courseid} course={item} variant="beta" />
      )) || []
    );
  }, [list]);

  // Effect to fetch categories
  useEffect(() => {
    // Fetching categories
    onFetch();
  }, [onFetch]);

  // Return JSX
  return (
    <CardsSection
      title={t(dictionaries.NewlyReleased)}
      listingPageUrl={CONFIG.routes.discover.new}
      items={items}
      limit={3}
      loading={isFetching}
      className="learningSlider"
      slidesConfig={{
        desktop: {
          view: 2.65,
          gap: 18,
        },
        mobile: {
          view: 1,
          gap: 10,
        },
      }}
      noDataMessage={
        <EmptyMessage
          icon="search"
          title={t(dictionary.noSearchResultsFound)}
          description={t(dictionary.pleaseCheckThatAllWordsAreSpelledCorrectly)}
        />
      }
    />
  );
};

export default NewlyReleasedCourses;
