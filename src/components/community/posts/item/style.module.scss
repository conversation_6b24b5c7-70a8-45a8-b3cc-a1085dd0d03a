@use 'mixins' as *;

.postWrapper {
  border: solid 1px #79b3eb;

  &.question {
    border-color: #f6b5bc;
  }

  &.popper {
    border-color: #b6e3b0;
  }
}

.header {
  @include forMarginPadding(padding, size(0), size(0), size(16), size(0));
  border-bottom: solid 1px var(--grey-400);
  display: flex;
  align-items: center;
}

.menuWrapper {
  @include forMarginPadding(padding, size(9), size(9), size(9), size(9));
  display: flex;
  gap: size(16);
  position: absolute;
  top: 0;
  right: size(12);

  @include rtl {
    right: auto;
    left: size(12);
  }

  div[class*='menu'] {
    @include forMarginPadding(padding, size(4), size(16), size(4), size(16));
    background-color: var(--tag);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }
}

.avatar {
  width: size(40);
  height: size(40);
  @include borderRadius(50%);
  @include forMarginPadding(margin, size(0), size(12), size(0), size(0));
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  @include borderRadius(50%);
  background: rgba(160, 146, 200, 0.15);

  p {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    font-weight: 500;
    font-size: size(16);
  }
}

.ownerLabel {
  margin-left: size(10);
  padding: size(10);
  border-radius: 20%;
  background-color: var(--grey-600);
}

.ownerContainer {
  display: flex;
  align-items: baseline;
}

.title {
  font-size: size(16);
  @include forMarginPadding(margin, size(5), size(0), size(2), size(0));
  line-height: 1;
  font-weight: 500;
}

.hoursAgo {
  font-size: size(12);
  @include forMarginPadding(margin, size(5), size(5), size(5), size(0));
  line-height: 1;
  color: var(--grey-800);
}

.body {
  @include forMarginPadding(padding, size(24), size(0), size(16), size(0));

  p {
    &:last-child {
      @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    }

    a {
      color: var(--text-link-blue);
      font-weight: 700;
    }
  }
}

.postImage {
  @include forMarginPadding(margin, size(16), size(0), size(8), size(0));

  height: size(334);
  overflow: hidden;

  img {
    object-fit: contain;
    object-position: center;
    height: 100%;
    width: auto;
  }
}

.moreBtn {
  color: var(--text-link-blue);
  font-size: size(16);
  background: none;
  border: none;
  cursor: pointer;
  font-weight: 500;

  span {
    display: inline;
    padding-bottom: 0;
    transition: all 0.5s linear;
    background: linear-gradient(
      to bottom,
      var(--text-link-blue) 0%,
      var(--text-link-blue) 98%
    );
    background-size: 0 1px;
    background-repeat: no-repeat;
    background-position: left 100%;

    @include rtl {
      background-position: right 100%;
    }
  }
}

.menuList {
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));

  li {
    list-style: none;
    border-bottom: solid 1px var(--cool-grey-700);

    &:last-child {
      border: none;
    }

    button {
      border: none;
      background: none;
      @include forMarginPadding(
        padding,
        size(12),
        size(12),
        size(12),
        size(12)
      );
      line-height: 1;
      display: flex;
      align-items: center;
      width: 100%;
      cursor: pointer;

      i {
        width: size(24);
        height: size(24);
        @include forMarginPadding(margin, size(0), size(10), size(0), size(0));

        svg {
          fill: var(--grey-900);
          fill-rule: evenodd;
        }

        &.plus {
          background: var(--grey-900);
          fill: var(--white);
          @include borderRadius(50%);
          display: flex;
          justify-content: center;
          align-items: center;

          svg {
            width: size(11);
            height: size(11);
          }
        }
      }

      span {
        font-size: size(14);
        display: inline;
        padding-bottom: 0;
        transition: all 0.5s linear;
        background: linear-gradient(
          to bottom,
          var(--grey-900) 0%,
          var(--grey-900) 98%
        );
        background-size: 0 1px;
        background-repeat: no-repeat;
        background-position: left 100%;

        @include rtl {
          background-position: right 100%;
        }
      }
    }
  }
}

.footer {
  @include forMarginPadding(padding, size(24), size(0), size(0), size(0));
  border-top: solid 1px var(--grey-400);
  display: flex;
}

.ftButton {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: size(14);
  @include forMarginPadding(margin, size(0), size(30), size(0), size(0));

  &:disabled {
    cursor: auto;
    span {
      background: none;
    }
  }

  &:last-child {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }

  i {
    width: size(17);
    height: size(17);
    @include forMarginPadding(margin, size(0), size(10), size(0), size(0));

    svg {
      fill: var(--grey-900);
    }
  }

  span {
    display: inline;
    padding-bottom: 0;
    transition: all 0.5s linear;
    background: linear-gradient(
      to bottom,
      var(--grey-900) 0%,
      var(--grey-900) 98%
    );
    background-size: 0 1px;
    background-repeat: no-repeat;
    background-position: left 100%;

    @include rtl {
      background-position: right 100%;
    }
  }

  .iconLike {
    fill: var(--grey-900);
    svg {
      fill: var(--grey-900);
    }
    path {
      fill: var(--grey-900);
    }
  }
}

@include hover() {
  .moreBtn {
    &:hover {
      span {
        background-size: 100% 1px;
      }
    }
  }

  .ftButton {
    &:hover {
      span {
        background-size: 100% 1px;
      }
    }
  }
}

@include for-all-phone() {
  .postImage {
    height: initial;
  }
}

.lightBoxOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99;
}

.lightBoxContent {
  position: relative;
  background: white;
  padding: 20px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.lightBoxImage {
  max-width: 80vw;
  max-height: 80vh;
  width: 100%;
  height: auto;
  object-fit: contain;
  display: block;
}
