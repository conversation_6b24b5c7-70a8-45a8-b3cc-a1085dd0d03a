# External API Dependencies Analysis Report
## Tomouh Government Learning Platform (Next.js Web Application)

**Analysis Date:** January 30, 2025  
**Application Type:** Next.js Web Application  
**Repository:** `/Users/<USER>/work/dge-ga-web-app`  

---

## Executive Summary

This comprehensive analysis identifies and catalogs all external API dependencies in the Tomouh government learning platform. The application integrates with **8 major external service categories** through **100+ API endpoints**, creating a complex multi-service architecture that supports government digital services, learning management, AI capabilities, and user authentication.

### Key Findings
- **Complex Government Integration**: Deep integration with UAE digital government services
- **Multi-layered Authentication**: UAE Pass + Liferay + Firebase authentication stack
- **AI Service Integration**: Core42 AI platform for intelligent features
- **Security Concerns**: Multiple hardcoded credentials and exposed API keys identified
- **Extensive Learning Ecosystem**: Comprehensive Totara LMS integration

---

## Phase 1: Environment Variables & Configuration Analysis

### 1.1 External Service Environment Variables

#### Government Services Configuration
| Variable | Value | Purpose | Security Level |
|----------|-------|---------|----------------|
| `LIFERAY_API` | `https://admin.stg.workspace.dge.gov.ae/o` | Liferay Portal/CMS API base URL | Public |
| `SUPPORT_CENTER_API` | `https://stg.workspace.dge.gov.ae/app/v2` | Support Center CRM system | Public |
| `UAE_PASS_URL` | `https://stg-id.uaepass.ae` | UAE Pass authentication service | Public |
| `UAE_PASS_ID` | `sandbox_stage` | UAE Pass application identifier | Low Risk |
| `UAE_PASS_STATE` | `ShNP22hyl1jUU2RGjTRkpg==` | UAE Pass state parameter | Medium Risk |
| `ILEARN_API` | `https://adsgstaging.elearning.ae/webservice/rest/server.php` | Totara LMS API endpoint | Public |
| `ILEARN_AUTH_BASE_URL` | `https://adsgstaging.elearning.ae` | Totara authentication base URL | Public |

#### AI and Machine Learning Services
| Variable | Value | Purpose | Security Level |
|----------|-------|---------|----------------|
| `OPENAI_BASE_API` | `https://api.core42.ai` | Core42 AI API base URL | Public |
| `AI_API_KEY` | `5f083cef704e45caa7ecf249f7f39650` | **🚨 EXPOSED API KEY** | **HIGH RISK** |
| `AI_X_API_KEY` | `key-abc123` | **🚨 EXPOSED API KEY** | **HIGH RISK** |

#### Firebase and Google Services
| Variable | Value | Purpose | Security Level |
|----------|-------|---------|----------------|
| `NEXT_PUBLIC_FIREBASE_CONFIG_API_KEY` | `AIzaSyCmrM6RqX6P-t0saUx9lsN3LWXbWapwW5g` | Firebase API key | Medium Risk |
| `NEXT_PUBLIC_FIREBASE_CONFIG_AUTH_DOMAIN` | `tomouh-web-development.firebaseapp.com` | Firebase auth domain | Public |
| `NEXT_PUBLIC_FIREBASE_CONFIG_PROJECT_ID` | `tomouh-web-development` | Firebase project ID | Public |
| `NEXT_PUBLIC_FIREBASE_CONFIG_STORAGE_BUCKET` | `tomouh-web-development.firebasestorage.app` | Firebase storage bucket | Public |
| `NEXT_PUBLIC_FIREBASE_CONFIG_MESSAGING_SENDER_ID` | `779958795170` | Firebase messaging sender ID | Public |
| `NEXT_PUBLIC_FIREBASE_CONFIG_APP_ID` | `1:779958795170:web:ec7bfe44ef94f455c3d0fb` | Firebase app ID | Public |
| `NEXT_PUBLIC_FIREBASE_CONFIG_MEASUREMENT_ID` | `G-M3YHKZ0F95` | Firebase/GA measurement ID | Public |
| `GA_MEASUREMENT_ID` | `G-M3YHKZ0F95` | Google Analytics tracking ID | Public |

#### Critical Security Findings
| Variable | Value | Security Issue | Risk Level |
|----------|-------|----------------|------------|
| `SUPPORT_CENTER_EMAIL_ADDRESS` | `<EMAIL>` | **🚨 HARDCODED CREDENTIAL** | **HIGH RISK** |
| `SUPPORT_CENTER_PASSWORD` | `2y16gvJ9j2WQZPlJLneg6ExnEO.U4lCG.vOh.q9k3vU0ufFpdtuqSrbcG` | **🚨 HARDCODED PASSWORD** | **CRITICAL RISK** |
| `SUPPORT_CENTER_JWT_SECRET` | `django-insecure-nd-h*%#%^@(w@msc&^j50-5%vskguqt+j1l!lo8!rcyv^v39v5` | **🚨 EXPOSED JWT SECRET** | **CRITICAL RISK** |
| `FIREBASE_CONFIG_SERVICE_ACCOUNT_KEY_PATH` | `./src/firebase/firebase-admin/tomouh-web-development-firebase-adminsdk-fbsvc-2da2bc1dd2.json` | Service account key file path | High Risk |

### 1.2 Configuration Files Analysis

#### Next.js Configuration (next.config.ts)
- **Image domains whitelist**: Multiple government domains configured for image loading
- **Staging environments**: `adsgstaging.elearning.ae`, `stg.workspace.dge.gov.ae`
- **Production domains**: `workspace.dge.gov.ae`, `learn.adsg.gov.ae`

#### External URL References (src/config/url/index.ts)
- **UAE Pass Signup**: `https://uaepass.ae/signup`
- **Cookie Testing**: `https://3dcomps.com/cookies-test/cookie-test.html`

---

## Phase 2: External Service Categorization

### 2.1 Government & Authentication Services

#### UAE Pass (Government SSO)
- **Purpose**: Primary government authentication system
- **Integration**: OAuth2-based authentication flow
- **Endpoints**: Authentication, token exchange
- **Data Flow**: User identity verification and government ID integration

#### Liferay Portal/CMS
- **Purpose**: Government content management and user services
- **Integration**: REST API with Bearer token authentication
- **Key Features**:
  - User authentication and profile management
  - Document upload and management (OBS integration)
  - Profile search and recommendations
  - Work experience and skills management
  - Course referral system

#### Support Center CRM
- **Purpose**: Customer relationship management and support ticketing
- **Integration**: Django-based REST API with JWT authentication
- **Key Features**:
  - Case management and tracking
  - FAQ system integration
  - File attachment handling
  - Comment and communication system

### 2.2 Learning Management Systems

#### Totara LMS (ILearn Platform)
- **Purpose**: Comprehensive learning management system
- **Integration**: Custom web service API with token-based authentication
- **Key Features**:
  - Course catalog and enrollment management
  - Learning pathways and progress tracking
  - Coaching and session booking system
  - Social learning networks and communities
  - Badges and certification management
  - Personal learning lists and recommendations

### 2.3 AI & Intelligent Services

#### Core42 AI Platform
- **Purpose**: AI-powered chat and generative capabilities
- **Integration**: REST API with API key authentication
- **Key Features**:
  - RAG (Retrieval Augmented Generation) search: `/tamm/rag_search/`
  - Generative AI: `/openai/deployments/gpt-4o/chat/completions`
  - Chat completion services

#### Feature Access Control System
- **Purpose**: Dynamic feature flag and access control management
- **Integration**: REST API with API key authentication
- **Endpoint**: `/features/get-features/`

### 2.4 Firebase Ecosystem

#### Firebase Authentication
- **Purpose**: Client-side authentication and user session management
- **Integration**: Firebase SDK with project-specific configuration

#### Firebase Remote Config
- **Purpose**: Dynamic application configuration management
- **Integration**: Firebase Admin SDK for server-side, Firebase SDK for client-side

#### Firebase Analytics
- **Purpose**: User behavior tracking and analytics
- **Integration**: Google Analytics 4 with Firebase integration

### 2.5 Third-party Utilities

#### Mobile App Store Integration
- **Apple App Store**: `https://apps.apple.com/ae/app/tomouh/id6736364399`
- **Google Play Store**: `https://play.google.com/store/apps/details?id=com.dge.academy`

#### External Utilities
- **Cookie Testing Service**: Third-party cookie support validation
- **UAE Pass Registration**: External signup flow integration

---

## Phase 3: API Endpoint Inventory & Mapping

### 3.1 Liferay Portal API Endpoints

#### Authentication Endpoints
| Endpoint | Method | Purpose | Authentication |
|----------|--------|---------|----------------|
| `/adsg-authentication/v1.0/authenticate` | POST | UAE Pass authentication | Bearer Token |
| `/adsg-authentication/v1.0/logout` | DELETE | User logout | Bearer Token |
| `/oauth2/token` | POST | Email login | Basic Auth |

#### User Management Endpoints
| Endpoint | Method | Purpose | Authentication |
|----------|--------|---------|----------------|
| `/adsg-talent-team/v1.0/profile-progress/{id}` | GET | User progress tracking | Bearer Token |
| `/adsg-profiles-search-results/v1.0/search?userId={id}` | GET | User profile fetch | Bearer Token |
| `/adsg-profiles-search-results/v1.0/updateUser?userId={id}` | PATCH/PUT | User profile update | Bearer Token |
| `/adsg-profiles-search-results/v1.0/profile-search?keyword={keyword}` | GET | Profile search | Bearer Token |

#### Document Management Endpoints
| Endpoint | Method | Purpose | Authentication |
|----------|--------|---------|----------------|
| `/adsg-document-upload/v1.0/obs-document-url/{id}` | GET | Document URL fetch | Bearer Token |
| `/adsg-document-upload/v1.0/obs-document/{folder}` | POST | Document upload | Bearer Token |

#### One-Time Password Endpoints
| Endpoint | Method | Purpose | Authentication |
|----------|--------|---------|----------------|
| `/adsg-smpp-verification/v1.0/generate-code` | POST | OTP generation | Bearer Token |
| `/adsg-smpp-verification/v1.0/verify-code` | POST | OTP verification | Bearer Token |

### 3.2 Totara LMS API Endpoints

#### Course Management
| Function | Purpose | Parameters |
|----------|---------|------------|
| `wstotara_get_onboardingcourses` | Recommended courses | User preferences |
| `wstotara_fetch_mylearningdata` | Learning progress data | User ID, filters |
| `wstotara_get_completed_courses` | Completed courses | User ID |
| `wstotara_course_recommendations` | Course suggestions | User profile |
| `wstotara_search_learningdata` | Course search | Keywords, filters |
| `wstotara_course_details` | Course details | Course ID |
| `wstotara_start_user_learning` | Start learning | Course ID |

#### Learning Pathways
| Function | Purpose | Parameters |
|----------|---------|------------|
| `wstotara_get_user_learning_pathways` | User pathways | User ID |
| `wstotara_get_required_and_recommended_learnings` | Program requirements | Program ID |
| `wstotara_program_details` | Program details | Program ID |
| `wstotara_program_enrollment` | Program enrollment | Program ID, User ID |

#### Social Learning & Communities
| Function | Purpose | Parameters |
|----------|---------|------------|
| `wstotara_get_my_networks` | User communities | User ID |
| `wstotara_get_network_details` | Community details | Network ID |
| `wstotara_post_discussions` | Create post | Content, Network ID |
| `wstotara_get_discussion_comments` | Get comments | Discussion ID |
| `wstotara_add_comments` | Add comment | Discussion ID, Content |

### 3.3 AI Service Endpoints

#### Core42 AI Integration
| Endpoint | Method | Purpose | Authentication |
|----------|--------|---------|----------------|
| `/tamm/rag_search/` | POST | RAG search queries | X-API-KEY |
| `/openai/deployments/gpt-4o/chat/completions` | POST | Generative AI chat | api-key |

#### Feature Access Control
| Endpoint | Method | Purpose | Authentication |
|----------|--------|---------|----------------|
| `/features/get-features/` | POST | Feature access check | X-API-KEY |

### 3.4 Support Center CRM Endpoints

#### Case Management
| Endpoint | Method | Purpose | Authentication |
|----------|--------|---------|----------------|
| `/auth/token/` | POST | Authentication | Credentials |
| `/crm/cases/` | POST | Create case | JWT Token |
| `/crm/cases/{caseNumber}/` | GET | Get case details | JWT Token |
| `/crm/cases/?emirates_id={emiratesId}` | GET | Get user cases | JWT Token |
| `/crm/attachments/` | POST | Add attachment | JWT Token |
| `/crm/comments/` | POST | Add comment | JWT Token |

### 3.5 Firebase Service Endpoints

#### Firebase Configuration
- **Authentication Domain**: `tomouh-web-development.firebaseapp.com`
- **Storage Bucket**: `tomouh-web-development.firebasestorage.app`
- **Remote Config**: Dynamic configuration management
- **Analytics**: Google Analytics 4 integration

---

## Phase 4: Security & Compliance Analysis

### 4.1 Critical Security Vulnerabilities

#### 🚨 CRITICAL RISK - Hardcoded Credentials
1. **Support Center Password**: Full password exposed in environment variables
2. **JWT Secret**: Django JWT secret key exposed in plain text
3. **API Keys**: Multiple AI service API keys hardcoded in environment

#### 🔴 HIGH RISK - Authentication Issues
1. **Basic Authentication**: Hardcoded credentials in login service
   ```typescript
   Authorization: 'Basic <EMAIL>:Liferay@DGE'
   ```
2. **Service Account Keys**: Firebase admin key file path exposed
3. **API Key Exposure**: AI service keys stored in environment variables

#### 🟡 MEDIUM RISK - Configuration Issues
1. **Public Firebase Config**: Client-side Firebase configuration exposed
2. **UAE Pass State**: Authentication state parameter in environment
3. **Development Credentials**: Staging credentials potentially in production

### 4.2 Authentication Security Patterns

#### Multi-layered Authentication Flow
1. **UAE Pass** → Government identity verification
2. **Liferay** → Application-level authentication
3. **Firebase** → Client-side session management
4. **Service-specific** → Individual API authentication

#### Token Management Issues
- **Bearer Tokens**: Proper implementation for Liferay APIs
- **API Keys**: Inconsistent key management across services
- **JWT Tokens**: Exposed secret compromises all JWT security

### 4.3 Compliance Concerns

#### Data Privacy Regulations
- **Government Data**: UAE government user data handling
- **Learning Records**: Educational data privacy requirements
- **Cross-border Data**: Firebase data residency considerations

#### Security Standards
- **Government Security**: UAE government security requirements
- **API Security**: Inconsistent authentication patterns
- **Credential Management**: Critical failures in secret management

### 4.4 Security Recommendations

#### Immediate Actions (Critical)
1. **Rotate all exposed credentials** immediately
2. **Implement proper secret management** (Azure Key Vault, AWS Secrets Manager)
3. **Remove hardcoded credentials** from codebase
4. **Audit all API key usage** and implement rotation policies

#### Short-term Improvements
1. **Implement environment-specific configurations**
2. **Add API key encryption** for stored credentials
3. **Implement proper JWT secret management**
4. **Add security headers** for all external API calls

#### Long-term Security Strategy
1. **Implement OAuth2/OIDC** for all service integrations
2. **Add API gateway** for centralized authentication
3. **Implement zero-trust architecture** for service communications
4. **Add comprehensive security monitoring** and alerting

---

## Phase 5: Data Architecture & Flow Mapping

### 5.1 Service Interconnection Architecture

```mermaid
graph TB
    User[User Browser] --> UAEPass[UAE Pass Authentication]
    User --> Firebase[Firebase Client SDK]
    
    UAEPass --> Liferay[Liferay Portal]
    Liferay --> TotaraLMS[Totara LMS]
    Liferay --> SupportCenter[Support Center CRM]
    
    Firebase --> FirebaseAdmin[Firebase Admin SDK]
    FirebaseAdmin --> RemoteConfig[Remote Configuration]
    
    App[Next.js Application] --> Core42AI[Core42 AI Services]
    App --> FeatureAccess[Feature Access Control]
    App --> GoogleAnalytics[Google Analytics]
    
    TotaraLMS --> LearningData[Learning Management Data]
    SupportCenter --> CRMData[Customer Support Data]
    Liferay --> UserProfiles[User Profile Data]
    
    subgraph "Government Services"
        UAEPass
        Liferay
        SupportCenter
    end
    
    subgraph "Learning Ecosystem"
        TotaraLMS
        LearningData
    end
    
    subgraph "AI & Intelligence"
        Core42AI
        FeatureAccess
    end
    
    subgraph "Firebase Ecosystem"
        Firebase
        FirebaseAdmin
        RemoteConfig
        GoogleAnalytics
    end
```

### 5.2 Authentication Token Flow

#### Primary Authentication Flow
1. **User Login** → UAE Pass authentication
2. **UAE Pass** → Returns authorization code
3. **Application** → Exchanges code for Liferay token
4. **Liferay Token** → Used for all government service APIs
5. **Firebase** → Parallel client-side authentication
6. **Service APIs** → Individual API key authentication

#### Token Lifecycle Management
- **UAE Pass Tokens**: Government-managed lifecycle
- **Liferay Tokens**: Application-managed with refresh capability
- **Firebase Tokens**: SDK-managed with automatic refresh
- **API Keys**: Static keys requiring manual rotation

### 5.3 User Data Synchronization Patterns

#### Profile Data Flow
1. **UAE Pass** → Government identity data
2. **Liferay** → Extended profile information
3. **Totara LMS** → Learning preferences and progress
4. **Firebase** → Client-side user preferences

#### Data Consistency Challenges
- **Profile Updates**: Manual synchronization between services
- **Learning Progress**: Real-time sync with Totara LMS
- **Support Cases**: Independent CRM data management

### 5.4 File Upload and Document Management

#### Document Flow Architecture
1. **Client Upload** → Liferay document service
2. **Liferay** → OBS (Object Storage Service) integration
3. **Document URLs** → Generated for secure access
4. **Support Center** → Independent file attachment system
5. **Totara LMS** → Learning content and user uploads

#### File Storage Patterns
- **Government Documents**: Liferay + OBS integration
- **Learning Content**: Totara LMS internal storage
- **Support Attachments**: Support Center CRM storage
- **User Uploads**: Multiple service endpoints

---

## Phase 6: Package Dependencies Analysis

### 6.1 Firebase and Google Service Packages

#### Core Firebase Dependencies
| Package | Version | Purpose | External Services |
|---------|---------|---------|-------------------|
| `firebase` | `^11.9.1` | Client-side Firebase SDK | Firebase Auth, Remote Config, Analytics |
| `firebase-admin` | `^13.4.0` | Server-side Firebase SDK | Firebase Admin, Remote Config |

#### Google Services Integration
- **Google Analytics**: Integrated through Firebase configuration
- **Google Cloud Services**: Implicit through Firebase Admin SDK

### 6.2 HTTP Client and Networking Packages

#### Primary HTTP Client
| Package | Version | Purpose | Usage |
|---------|---------|---------|-------|
| `axios` | `^1.7.7` | HTTP client library | All external API communications |

#### Networking Features
- **Request/Response Interceptors**: Custom authentication handling
- **Multi-service Support**: Liferay, Totara, Support Center, AI services
- **Error Handling**: Centralized error management across services

### 6.3 Authentication and Security Packages

#### JWT and Authentication
| Package | Version | Purpose | External Integration |
|---------|---------|---------|---------------------|
| `jose` | `^6.0.11` | JWT handling and verification | Firebase token verification |

#### Security Features
- **Token Verification**: Firebase ID token validation
- **JWT Processing**: Support Center JWT handling
- **API Key Management**: Custom implementation for AI services

### 6.4 Utility and Integration Packages

#### Date and Time Management
| Package | Version | Purpose | External Service Usage |
|---------|---------|---------|----------------------|
| `moment` | `^2.30.1` | Date manipulation | Learning schedules, booking systems |
| `moment-timezone` | `^0.5.47` | Timezone handling | Multi-timezone learning sessions |

#### Content Processing
| Package | Version | Purpose | External Integration |
|---------|---------|---------|---------------------|
| `remark` | `^15.0.1` | Markdown processing | Content from external CMSs |
| `remark-html` | `^16.0.1` | HTML conversion | Dynamic content rendering |

#### State Management
| Package | Version | Purpose | External Data |
|---------|---------|---------|---------------|
| `zustand` | `^5.0.1` | State management | External API data caching |

### 6.5 Package Dependency Summary

#### External Service Dependencies
- **Firebase Ecosystem**: 2 packages for complete Firebase integration
- **HTTP Communications**: 1 primary package (axios) for all API calls
- **Authentication**: 1 package (jose) for JWT handling
- **Content Processing**: 2 packages for dynamic content from external sources
- **Utility Support**: 4 packages supporting external service integrations

#### Security Considerations
- **Package Vulnerabilities**: Regular security updates required
- **Dependency Chain**: External service packages introduce additional attack vectors
- **Version Management**: Critical for security patch deployment

---

## Recommendations & Risk Assessment

### Critical Actions Required

#### 🚨 Immediate Security Actions
1. **Rotate all exposed credentials** within 24 hours
2. **Implement proper secret management** system
3. **Remove hardcoded credentials** from all code and configuration
4. **Audit API key usage** across all services

#### 🔴 High Priority Improvements
1. **Implement centralized authentication** gateway
2. **Add comprehensive logging** for all external API calls
3. **Implement rate limiting** and API abuse protection
4. **Add health monitoring** for all external services

#### 🟡 Medium Priority Enhancements
1. **Implement service mesh** for better service communication
2. **Add caching layer** for external API responses
3. **Implement circuit breakers** for external service failures
4. **Add comprehensive testing** for external integrations

### Architecture Recommendations

#### Service Integration Strategy
1. **API Gateway Implementation**: Centralize all external service communications
2. **Authentication Standardization**: Implement OAuth2/OIDC across all services
3. **Data Synchronization**: Implement event-driven data consistency
4. **Monitoring and Observability**: Add comprehensive external service monitoring

#### Security Architecture
1. **Zero Trust Model**: Implement zero-trust architecture for all external communications
2. **Secret Management**: Use enterprise secret management solutions
3. **API Security**: Implement comprehensive API security patterns
4. **Compliance Framework**: Establish government compliance monitoring

---

## Conclusion

The Tomouh government learning platform represents a sophisticated multi-service architecture with extensive external API dependencies. While the integration provides comprehensive functionality, **critical security vulnerabilities require immediate attention**. The exposed credentials and hardcoded API keys present significant security risks that must be addressed urgently.

The platform's architecture demonstrates strong integration capabilities with government services, learning management systems, and AI platforms. However, implementing proper security practices, centralized authentication, and comprehensive monitoring will be essential for maintaining a secure and reliable government digital service.

**Total External Services Identified**: 8 major categories  
**Total API Endpoints**: 100+ endpoints  
**Critical Security Issues**: 5 immediate concerns  
**Recommended Actions**: 12 prioritized improvements  

This analysis provides the foundation for implementing a more secure, maintainable, and scalable external service integration architecture.
