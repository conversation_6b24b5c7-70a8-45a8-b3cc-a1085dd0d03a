import { NextResponse } from 'next/server';
import { TotaraFetchInterestCategoriesService } from '@/services/totara/interests';
import { AxiosError } from 'axios';
import { ErrorResponse } from '@/utils/fetcher';

/**
 * Route to get the interests categories
 */
export async function GET() {
  try {
    // Fetching the interest categories
    const response = await TotaraFetchInterestCategoriesService();
    // Return the response
    return NextResponse.json({ list: response?.data });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error as AxiosError);
  }
}
