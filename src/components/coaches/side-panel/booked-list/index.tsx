import styles from './style.module.scss';
import Typography from '@/ui/typography';
import BookedCoachItem from '../booked-coach/item';
import useCoachesStore from '@/stores/learning/coaches/list';
import { coachExperience } from '../../mock-data';

const BookedCoachesList = () => {
  const { list } = useCoachesStore();

  return (
    <div className={styles.BookedList}>
      <div className={`dialog-heading ${styles.heading}`}>
        <Typography as="h2">My Saved Coaches</Typography>
      </div>
      <div className="dialog-content">
        {list?.map((coach) => (
          <BookedCoachItem
            key={coach?.id}
            coachId={coach?.id}
            photo={coach?.picture}
            title={coach?.firstname?.concat(' ', coach?.lastname || '')}
            designation={coach?.designation}
            rating={parseFloat(coach?.rating || '')}
            experience={coachExperience}
            customStyles={styles}
            showSkills={true}
            specialization={coach.my_specilization}
            showWorkIcon={true}
          />
        ))}
      </div>
    </div>
  );
};

export default BookedCoachesList;
