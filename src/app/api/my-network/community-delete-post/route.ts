import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraCommunityDetailsDeletePostService } from '@/services/totara/my-network';

/**
 * Route to delete POST in community
 */
export async function POST(request: NextRequest) {
  try {
    const { discussionId } = await request.json();

    // call API to delete community POST
    const response =
      await TotaraCommunityDetailsDeletePostService(discussionId);
    // Return the response
    return NextResponse.json({
      success: response.data.success,
      message: response.data.message,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error);
  }
}
