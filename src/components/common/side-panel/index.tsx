import { FC } from 'react';
import LearningProgress from '../learning-progress';
import GreetingsPanel from '../greetings-panel';
import MyStreaks from '../my-streaks';
import SidePanelNavigations from '../side-panel-navs';

interface ISidePanel {
  hasGreetings?: boolean;
  hasProgress?: boolean;
  hasStreaks?: boolean;
  hasNavigations?: boolean;
}

const SidePanel: FC<ISidePanel> = ({
  hasGreetings = true,
  hasProgress = false,
  hasStreaks = false,
  hasNavigations = false,
}) => {
  // Return JSX
  return (
    <>
      {hasGreetings && <GreetingsPanel />}
      {hasProgress && <LearningProgress />}
      {hasStreaks && <MyStreaks />}
      {hasNavigations && <SidePanelNavigations />}
    </>
  );
};

export default SidePanel;
