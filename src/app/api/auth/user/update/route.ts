import { NextRequest, NextResponse } from 'next/server';
import { ExternalUpdateUserService } from '@/services/external/user';
import IUser from '@/types/domain/external/user';
import { ErrorResponse } from '@/utils/fetcher';
import { AxiosError } from 'axios';
import { TotaraFetchProfileService } from '@/services/totara/profile';

// To handle a POST request to /api
export async function POST(request: NextRequest) {
  try {
    // Getting request body
    const data: IUser = await request.json();
    // Updating user profile
    const response = await ExternalUpdateUserService(data);
    // Getting totara user profile
    const totaraProfile = await TotaraFetchProfileService();
    // Return the response
    return NextResponse.json({
      user: response?.data,
      totaraProfile: totaraProfile?.data,
    });
  } catch (error) {
    // Return Error response
    return ErrorResponse(error as AxiosError);
  }
}
