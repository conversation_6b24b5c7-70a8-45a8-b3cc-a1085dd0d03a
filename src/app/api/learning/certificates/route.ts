import { NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchCertificatesService } from '@/services/totara/certificates';

/**
 * Route to get the certificates
 */
export async function GET() {
  try {
    // Fetching certificates
    const response = await TotaraFetchCertificatesService();

    // Return the response
    return NextResponse.json({
      certificates: response?.data?.certificates,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
