import type { CacheOptions, ICache } from './types';

interface Entry<T> {
  value: T;
  expiresAt: number | null;
}

export class Memory<PERSON>ache implements ICache {
  private store = new Map<string, Entry<unknown>>();
  private ns = '';
  private maxEntries?: number;

  constructor(options?: { namespace?: string; maxEntries?: number }) {
    this.ns = options?.namespace || '';
    this.maxEntries = options?.maxEntries;
  }

  private k(key: string) {
    return this.ns ? `${this.ns}:${key}` : key;
  }

  get<T>(key: string): T | null {
    const entry = this.store.get(this.k(key)) as Entry<T> | undefined;
    if (!entry) return null;
    if (entry.expiresAt && Date.now() > entry.expiresAt) {
      this.store.delete(this.k(key));
      return null;
    }
    return entry.value;
  }

  set<T>(key: string, value: T, options?: CacheOptions): void {
    if (this.maxEntries && this.store.size >= this.maxEntries) {
      const firstKey = this.store.keys().next().value;
      if (firstKey) this.store.delete(firstKey);
    }
    const ttlMs = options?.ttlMs;
    const expiresAt = typeof ttlMs === 'number' ? Date.now() + ttlMs : null;
    this.store.set(this.k(key), { value, expiresAt });
  }

  del(key: string): void {
    this.store.delete(this.k(key));
  }

  clear(namespace?: string): void {
    if (!namespace) {
      this.store.clear();
      return;
    }
    const prefix = `${namespace}:`;
    for (const key of this.store.keys()) {
      if (key.startsWith(prefix)) this.store.delete(key);
    }
  }

  withNamespace(namespace: string): ICache {
    const child = new MemoryCache({}) as MemoryCache;
    (child as MemoryCache).store = this.store;
    (child as MemoryCache).ns = namespace;
    (child as MemoryCache).maxEntries = this.maxEntries;
    return child;
  }
}

export const serverCache = new MemoryCache({});
export const clientCache = new MemoryCache({});
