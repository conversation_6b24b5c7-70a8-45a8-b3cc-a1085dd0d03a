import { FC } from 'react';
import styles from './style.module.scss';
import Typography from '@/ui/typography';
import dictionary from '@/dictionaries'; // Assuming dictionary path is correct

interface ICalendarTabsProps {
  isMyCalendar: boolean;
  onToggleCalendar: (isMyCalendar: boolean) => void;
}

const CalendarTabs: FC<ICalendarTabsProps> = ({
  isMyCalendar,
  onToggleCalendar,
}) => {
  return (
    <div className={styles.calendarTabs}>
      <button
        disabled={isMyCalendar}
        onClick={() => onToggleCalendar(true)}
        className={`${styles.eventTypeCta} ${isMyCalendar ? styles.isTabActive : ''}`}
      >
        <Typography as="span" dictionary={dictionary.myCalendar} />
      </button>
      <button
        disabled={!isMyCalendar}
        onClick={() => onToggleCalendar(false)}
        className={`${styles.eventTypeCta} ${!isMyCalendar ? styles.isTabActive : ''}`}
      >
        <Typography as="span" dictionary={dictionary.academicCalendar} />
      </button>
    </div>
  );
};

export default CalendarTabs;
