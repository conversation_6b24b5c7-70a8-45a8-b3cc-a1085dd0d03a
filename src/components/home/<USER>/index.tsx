import React, { useCallback, useState } from 'react';
import InProgress from './in-progress';
import Required from './required';
import Favorite from './favorite';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';

const MyLearning = () => {
  // Proeperty to hold the active filter
  const [active, setActive] = useState('in-progress');

  const t = useTranslations();

  const FILTERS = [
    { id: 'in-progress', title: t(dictionary.enrolled) },
    { id: 'required', title: t(dictionary.required) },
    { id: 'favorite', title: t(dictionary.favorites) },
  ];

  // Event to handle filter change
  const onChange = useCallback(
    (id: string) => {
      setActive(id);
    },
    [setActive],
  );

  // Return JSX
  return (
    <>
      {active === 'in-progress' && (
        <InProgress active={active} filters={FILTERS} onChange={onChange} />
      )}
      {active === 'required' && (
        <Required active={active} filters={FILTERS} onChange={onChange} />
      )}
      {active === 'favorite' && (
        <Favorite active={active} filters={FILTERS} onChange={onChange} />
      )}
    </>
  );
};

export default MyLearning;
