import { useCallback, useEffect, useState } from 'react';
import Paper from '@/ui/paper';
import Banner from './banner';
import Header from './header';
import Tabs from '@/ui/tabs';
import { useParams } from 'next/navigation';
import useCommunityDetailsStore from '@/stores/my-network/community-details';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';

// Styles
import styles from './style.module.scss';
import Discussion from './discussion';
import Library from './library';
import Members from './members';
import { CommunityDetailsTabsEnum } from '@/types/domain/my-network/communityDetails';
import useCommunityDetailsLibraryStore from '@/stores/my-network/community-details/library';
import dictionary from '@/dictionaries';
import Toast from '@/ui/toast';
import useCommunityDetailsPostsStore from '@/stores/my-network/community-details/posts';

const TABS = [
  {
    id: CommunityDetailsTabsEnum.discussion,
    dictionary: dictionary.discussion,
  },
  { id: CommunityDetailsTabsEnum.members, dictionary: dictionary.Members },
  { id: CommunityDetailsTabsEnum.library, dictionary: dictionary.Library },
];

//Community details component
const Community = () => {
  // Getting tab id from Params
  const communityId = useParams().slug as string;

  const {
    isFetching: creatingPost,
    message,
    clearUpdateToast,
  } = useCommunityDetailsPostsStore();
  const { getCommunityDetails, communityDetails, isFetching } =
    useCommunityDetailsStore((store) => store);

  const {
    getCommunityDetailsLibrary,
    records,
    isFetching: isLoading,
  } = useCommunityDetailsLibraryStore((store) => store);

  // Property to hold active tab id
  const [tab, setTab] = useState(CommunityDetailsTabsEnum.discussion);

  useEffect(() => {
    //get details
    getCommunityDetails(Number(communityId));
    //get details library
    getCommunityDetailsLibrary(Number(communityId));
  }, [
    getCommunityDetails,
    getCommunityDetailsLibrary,
    communityId,
    creatingPost,
  ]);
  /**
   * Method to handle change active tab
   */
  const handleChangeTab = useCallback((id: string) => {
    setTab(id as CommunityDetailsTabsEnum);
  }, []);

  const renderTab = (tabId: CommunityDetailsTabsEnum) => {
    switch (tabId) {
      case CommunityDetailsTabsEnum.library:
        return communityDetails?.is_member ||
          communityDetails?.private === 0 ? (
          <Library records={records} isLoading={isLoading} />
        ) : (
          ''
        );
      case CommunityDetailsTabsEnum.members:
        return communityDetails?.is_member ||
          communityDetails?.private === 0 ? (
          <Members members={communityDetails?.memberdetails} />
        ) : (
          ''
        );
      case CommunityDetailsTabsEnum.discussion:
      default:
        return (
          <Discussion
            discussions={communityDetails?.discussions}
            communityDetails={communityDetails}
          />
        );
    }
  };

  const isLoadingAll = isFetching || creatingPost;

  // Return JSX
  return (
    <>
      <WithLoader loader={<Loading />} loading={isLoadingAll}>
        <Banner
          imageUrl={communityDetails?.imageurl}
          community={communityDetails}
        />
        <div className={styles.headerFixed}>
          <Paper className={styles.head}>
            <Header
              title={communityDetails?.title}
              summary={communityDetails?.summary}
              members={communityDetails?.members}
            />
            <Tabs
              active={tab}
              tabs={
                communityDetails?.is_member || communityDetails?.private === 0
                  ? TABS
                  : [TABS[0]]
              }
              onChange={handleChangeTab}
            />
          </Paper>
        </div>
        {renderTab(tab)}
      </WithLoader>
      {message && (
        <Toast
          text={message as string}
          type={'success'}
          onClose={clearUpdateToast}
        />
      )}
    </>
  );
};

export default Community;
