import React from 'react';
import useCalendarView from '@/hooks/calendar/useCalendarView';
import IFormatedEvent from '@/types/domain/calendar/formated-event';
import styles from './style.module.scss'; // Import styles here

type TimePeriod = 'daily' | 'weekly' | 'monthly';

interface CalendarViewProps {
  selectedDate: Date;
  events: IFormatedEvent[];
  selectedTimePeriod: TimePeriod;
  onDateSelect: (date: Date) => void;
  onEventClick: (event: IFormatedEvent) => void;
  onEventHover: (
    event: IFormatedEvent,
    rect: DOMRect,
    elementId: string,
  ) => void;
}

const CalendarView: React.FC<CalendarViewProps> = ({
  selectedDate,
  events,
  selectedTimePeriod,
  onDateSelect,
  onEventClick,
  onEventHover,
}) => {
  const mainCalendarContent = useCalendarView({
    month: selectedDate.getMonth(),
    year: selectedDate.getFullYear(),
    day: selectedDate.getDate(),
    selectedDate: selectedDate,
    events: events,
    type: 'large',
    onDateSelect: onDateSelect,
    activePeriod: selectedTimePeriod,
    onEventClick: onEventClick,
    onEventHover: onEventHover,
  });

  const timePeriodClass = `${selectedTimePeriod}View`;

  return (
    <div
      className={`${styles.calendar} ${styles[timePeriodClass]}`} // Apply classes here
      id={selectedTimePeriod} // Keep id here
    >
      {mainCalendarContent}
    </div>
  );
};

export default CalendarView;
