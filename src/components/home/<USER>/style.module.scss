@use 'mixins' as *;

.recommendedWrapper {
  > div[class*='paper'] {
    min-height: size(300);

    @include for-all-phone() {
      min-height: size(336);
      @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    }
  }

  > div[class*='head'] {
    a {
      text-decoration: underline;

      &:hover {
        text-decoration: none;
      }

      span {
        background: none;
      }
    }
  }
}
