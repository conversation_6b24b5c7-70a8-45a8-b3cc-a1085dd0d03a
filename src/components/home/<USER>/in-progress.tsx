import React, { FC, useEffect, useMemo } from 'react';
import CONFIG from '@/config';
import CardsSection from '@/components/common/cards-section';
import useInProgressCoursesStore from '@/stores/learning/courses/in-progress';
import Course from '@/components/course';
import EmptyMessage from '@/ui/empty-message';
import styles from './style.module.scss';
import dictionary from '@/dictionaries';

interface IInProgress {
  active: string;
  filters: { id: string; title: string }[];
  onChange: (id: string) => void;
}

// Favorite component
const InProgress: FC<IInProgress> = (props) => {
  // Deconstructing props
  const { active, filters, onChange } = props;

  // Method to fetch courses
  const isFetching = useInProgressCoursesStore((x) => x.isFetching);
  const list = useInProgressCoursesStore((x) => x.list);
  const onFetch = useInProgressCoursesStore((x) => x.fetch);

  // Creating listing page url
  const page = useMemo(() => {
    // If list has more than 3 items
    if (list?.length > 3) {
      return CONFIG.routes.myCourseList.replace('{id}', 'in-progress');
    }
    // Return the listing page URL
    return null;
  }, [list]);

  // Creating dyanmic array of cards
  const items = useMemo(() => {
    // Return the courses
    return list?.map((item) => <Course key={item?.id} course={item} />);
  }, [list]);

  // Effect to fetch the courses
  useEffect(() => {
    if (isFetching === null) {
      onFetch();
    }
  }, [isFetching, onFetch]);

  // Return JSX
  return (
    <CardsSection
      title={dictionary.myLearning}
      listingPageUrl={page}
      items={items}
      limit={10}
      loading={isFetching}
      className="learningSlider2"
      wrapperClass={styles.mylearningWrapper}
      filters={filters}
      activeFilterOption={active}
      onChangeFilter={onChange}
      slidesConfig={{
        desktop: {
          view: list?.length > 4 ? 4.2 : 4,
          gap: 16,
        },
        tablet: {
          view: list?.length > 3 ? 3.2 : 3,
          gap: 16,
        },
        mobile: {
          view: 1.2,
          gap: 10,
        },
      }}
      noDataMessage={
        <EmptyMessage
          icon="search"
          title={dictionary.noCoursesAvailableUnderThisSection}
          description="Please check that all words are spelled correctly, or try another search word"
        />
      }
    />
  );
};

// Export the component
export default InProgress;
