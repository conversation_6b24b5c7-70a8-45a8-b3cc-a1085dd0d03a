import { FC, useCallback, useState } from 'react';
import { motion } from 'motion/react';
import Image from 'next/image';
import Media from '@/ui/media';
import Typography from '@/ui/typography';
import BackOverly from '@/ui/back-overly';
import useRateReviewStore from '@/stores/learning/courses/rate-review';
import useCourseDetailStore from '@/stores/learning/courses/detail';
import useCoreCourseStore from '@/stores/learning/courses/core-course';
import { Checkmark, Close, Info } from '@/ui/icons';
import ReviewSubmitPopup from './review-submit-popup';
import { useTranslations } from 'next-intl';

import starOutline from '@public/images/star-outline.svg';
import star from '@public/images/star.svg';

// Styles
import styles from './style.module.scss';
import Button from '@/ui/form/button';
import dictionary from '@/dictionaries';

const WriteReview: FC<{ onClose: () => void }> = ({ onClose }) => {
  const [reviewRating, setReviewRating] = useState<number>(5);
  const [showPopup, setShowPopup] = useState<boolean>(false);
  const [isSuccess, setIsSuccess] = useState<boolean>(true);
  const [message, setMessage] = useState<string>('');
  const onCoreCourseFetch = useCoreCourseStore((state) => state.fetch);
  const onFetch = useCourseDetailStore((state) => state.fetch);
  const fetchReviews = useRateReviewStore((x) => x.fetch);
  const isFetching = useRateReviewStore((x) => x.isFetching);
  const courseDetail = useCourseDetailStore((state) => state.detail);

  const onRateAction = useCallback(
    async (courseId: string | undefined, reviews: string) => {
      const response = await fetchReviews(courseId, reviews);
      if (response) {
        setShowPopup(true);
        setIsSuccess(response.status);
        setMessage(response.message);
      }
    },
    [fetchReviews],
  );
  const t = useTranslations();

  // Return JSX
  return (
    <>
      <motion.div
        initial={{ opacity: 0, zIndex: 9999 }}
        animate={{ opacity: 1 }}
        className={styles.popup}
      >
        <Media mobile={false}>
          <BackOverly onClick={onClose} />
        </Media>
        <div className={styles.wrapper}>
          <button onClick={onClose} className={styles.close}>
            <i>
              <Close />
            </i>
          </button>
          <Typography
            as="h2"
            className={styles.heading}
            dictionary={dictionary.writeAReview}
          />
          <Typography
            as="h4"
            className={styles.subTitle}
            dictionary={dictionary.rateThisCourse}
          />
          <Typography
            as="p"
            className={styles.subDescription}
            dictionary={dictionary.yourRatingAndReview}
          />
          <Typography
            as="h3"
            className={styles.ratingTitle}
            dictionary={dictionary.howWouldYouRate}
          />
          <nav className={styles.rating}>
            <ul>
              <li>
                <button
                  type="button"
                  className={styles.active}
                  onClick={() => setReviewRating(1)}
                >
                  <i>
                    <Image
                      src={starOutline}
                      alt=""
                      className={styles.outline}
                    />
                    <Image src={star} alt="" className={styles.filled} />
                  </i>
                </button>
              </li>
              <li>
                <button
                  type="button"
                  className={reviewRating > 1 ? styles.active : ''}
                  onClick={() => setReviewRating(2)}
                >
                  <i>
                    <Image
                      src={starOutline}
                      alt=""
                      className={styles.outline}
                    />
                    <Image src={star} alt="" className={styles.filled} />
                  </i>
                </button>
              </li>
              <li>
                <button
                  type="button"
                  className={reviewRating > 2 ? styles.active : ''}
                  onClick={() => setReviewRating(3)}
                >
                  <i>
                    <Image
                      src={starOutline}
                      alt=""
                      className={styles.outline}
                    />
                    <Image src={star} alt="" className={styles.filled} />
                  </i>
                </button>
              </li>
              <li>
                <button
                  type="button"
                  className={reviewRating > 3 ? styles.active : ''}
                  onClick={() => setReviewRating(4)}
                >
                  <i>
                    <Image
                      src={starOutline}
                      alt=""
                      className={styles.outline}
                    />
                    <Image src={star} alt="" className={styles.filled} />
                  </i>
                </button>
              </li>
              <li>
                <button
                  type="button"
                  className={reviewRating > 4 ? styles.active : ''}
                  onClick={() => setReviewRating(5)}
                >
                  <i>
                    <Image
                      src={starOutline}
                      alt=""
                      className={styles.outline}
                    />
                    <Image src={star} alt="" className={styles.filled} />
                  </i>
                </button>
              </li>
            </ul>
          </nav>

          <div className={styles.buttonWrap}>
            <Button
              color="black"
              type="button"
              loading={isFetching}
              disabled={isFetching}
              className={styles.submit}
              dictionary={dictionary.submit}
              onClick={() =>
                onRateAction(
                  courseDetail?.courseId?.toString(),
                  reviewRating.toString(),
                )
              }
            />
          </div>
        </div>
      </motion.div>
      {showPopup ? (
        <ReviewSubmitPopup
          isOpened={showPopup}
          onCancel={() => {
            setShowPopup(false);
            if (isSuccess) {
              onFetch(courseDetail?.courseId?.toString() as string);
              onCoreCourseFetch(courseDetail?.courseId?.toString() as string);
            }
            onClose();
          }}
        >
          <span
            className={
              styles.popupIcon + ` ${isSuccess ? styles.isSuccess : ''}`
            }
          >
            {isSuccess ? <Checkmark /> : <Info />}
          </span>
          <Typography as="h4" className={styles.popupTitle}>
            {isSuccess ? t(dictionary.thankYouForYourReview) : ''}
          </Typography>
          <Typography as="p" className={styles.popupMessage}>
            {isSuccess ? t(dictionary.yourFeedbackHelpsUsImprove) : message}
          </Typography>
        </ReviewSubmitPopup>
      ) : null}
    </>
  );
};

export default WriteReview;
