@use 'mixins' as *;

.detail {
  @include forMarginPadding(padding, size(0), size(40), size(0), size(0));

  @include for-all-phone() {
    @include forMarginPadding(padding, size(0), size(24), size(0), size(0));
  }
}

.banner {
  overflow: hidden;
  @include forMarginPadding(margin, size(0), size(0), size(30), size(0));
  @include borderRadius(var(--radius));
  height: size(215);
}

@include for-all-phone() {
  .banner {
    height: size(249);
  }
}

.announcementTitle {
  color: var(--grey-900);
  font-size: size(28);
}

.date {
  display: flex;
  align-items: center;
  gap: size(8);
  color: var(--grey-800);
  @include forMarginPadding(margin, size(0), size(0), size(20), size(0));

  p {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  }
}

.calIcon {
  @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
  display: flex;
  position: relative;

  svg {
    fill: var(--grey-900);
    fill-rule: evenodd;
    width: size(14);
    height: size(16);
  }
}

.content {
  p {
    color: var(--grey-800);
  }

  ul {
    @include forMarginPadding(padding, size(0), size(0), size(0), size(30));
  }
}
