import { useEffect } from 'react';
import WithLoader from '@/ui/skeleton/with-loader';
import Loading from '@/components/common/loading';
import Paper from '@/ui/paper';
import Grid from '@/ui/grid';
import EmptyMessage from '@/ui/empty-message';
import useTopCoursesStore from '@/stores/learning/courses/top';
import Course from '@/components/course';
import dictionary from '@/dictionaries';
import { useTranslations } from 'next-intl';

const TopCoursesList = () => {
  // Getting loading state
  const isFetching = useTopCoursesStore((x) => x.isFetching);
  const t = useTranslations();
  // Getting fetch action
  const onFetch = useTopCoursesStore((x) => x.fetch);

  // Getting top picks
  const list = useTopCoursesStore((x) => x.list);

  useEffect(() => {
    if (isFetching === null) {
      // fetching short courses
      onFetch();
    }
  }, [onFetch, isFetching]);

  // Return JSX
  return (
    <WithLoader loading={isFetching} loader={<Loading />}>
      <Paper>
        {!isFetching && list?.length > 0 ? (
          <Grid xs={1} sm={3} md={4} gap="lg">
            {list.map((item) => {
              return <Course key={item.courseid} course={item} />;
            })}
          </Grid>
        ) : (
          <EmptyMessage
            icon="search"
            title={t(dictionary.noSearchResultsFound)}
            description={t(
              dictionary.pleaseCheckThatAllWordsAreSpelledCorrectly,
            )}
          />
        )}
      </Paper>
    </WithLoader>
  );
};

export default TopCoursesList;
