import React, { FC, useCallback, useEffect, useRef, useState } from 'react';
import FilterItem from './item';
import { useLocale } from 'next-intl';
// Styles
import styles from './style.module.scss';
import { LeftArrow, RightArrow } from '@/ui/icons';

export interface IFilterOption {
  id: string;
  title: string;
  dictionary?: string;
  numberOfItems?: number;
}

interface IFilterButtons {
  filterOptions?: IFilterOption[];
  activeOption?: string;
  onChange?: (id: string) => void;
  isScrollable?: boolean;
  wrap?: boolean;
}

const FilterButtons: FC<IFilterButtons> = (props) => {
  const {
    filterOptions,
    activeOption = '',
    onChange,
    isScrollable,
    wrap,
  } = props;
  const containerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(false);
  const locale = useLocale();
  const isRTL = locale === 'ar';

  /**
   * Method to handle click on filter option
   */
  const onClickFilterItem = useCallback(
    (id: string) => {
      if (onChange) {
        onChange(id);
      }
    },
    [onChange],
  );

  const handleScrollCheck = useCallback(() => {
    if (containerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = containerRef.current;
      if (isRTL) {
        setShowRightArrow(scrollLeft < 0);
        setShowLeftArrow(-scrollLeft + clientWidth < scrollWidth - 10);
      } else {
        setShowLeftArrow(scrollLeft > 0);
        setShowRightArrow(scrollLeft + clientWidth < scrollWidth);
      }
    }
  }, [isRTL]);

  useEffect(() => {
    if (isScrollable) {
      handleScrollCheck();
    }
  }, [filterOptions, handleScrollCheck, isScrollable]);

  const scrollLeftHandler = useCallback(() => {
    containerRef.current?.scrollBy({
      left: -300,
      behavior: 'smooth',
    });
  }, []);

  const scrollRightHandler = useCallback(() => {
    containerRef.current?.scrollBy({
      left: 300,
      behavior: 'smooth',
    });
  }, []);

  // Return JSX
  if (filterOptions && filterOptions?.length > 0) {
    return (
      <div className={styles.filtersHead}>
        {isScrollable && showLeftArrow && (
          <button className={styles.scrollArrow} onClick={scrollLeftHandler}>
            <LeftArrow />
          </button>
        )}
        <nav
          className={`${styles.filterBtns} ${isScrollable ? styles.scrollable : ''}`}
          ref={containerRef}
          onScroll={isScrollable ? handleScrollCheck : undefined}
        >
          <ul className={wrap ? `${styles.wrapUl}` : ''}>
            {filterOptions.map((filter) => (
              <li key={filter.id}>
                <FilterItem
                  id={filter.id}
                  title={filter.title}
                  dictionary={filter.dictionary || ''}
                  numberOfItems={filter.numberOfItems}
                  active={activeOption}
                  onClick={onClickFilterItem}
                />
              </li>
            ))}
          </ul>
        </nav>
        {isScrollable && showRightArrow && (
          <button className={styles.scrollArrow} onClick={scrollRightHandler}>
            <RightArrow />
          </button>
        )}
      </div>
    );
  }

  return <></>;
};

export default FilterButtons;
