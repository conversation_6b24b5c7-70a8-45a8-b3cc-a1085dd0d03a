#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# check format
yarn check-format || ( echo 'Format check failed! \nPlease fix the format issues and try again. \nFix with `yarn run format`' && exit 1 )

# Check linting
yarn check-lint || ( echo 'Linting check failed! \nPlease fix the issues linting issues and try again. \nCheck with `yarn run check-lint`' && exit 1 )

# check types
yarn check-types || ( echo 'Types check failed! \nPlease fix the issues and try again. \nCheck with `yarn run check-types`' && exit 1 )

# creating build
yarn build || ( echo 'Build failed!' && exit 1 )
