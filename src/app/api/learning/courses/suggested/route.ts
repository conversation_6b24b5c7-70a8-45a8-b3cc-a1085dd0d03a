import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponse } from '@/utils/fetcher';
import { TotaraFetchSuggestedCoursesService } from '@/services/totara/courses';
import IPagination from '@/types/domain/internal/base/pagination';

/**
 * Route to get the announcements
 */
export async function GET(request: NextRequest) {
  try {
    // Getting request params
    const page = request.nextUrl.searchParams.get('page') || '1';
    const perPage = request.nextUrl.searchParams.get('perPage') || '10';

    // Fetching records counts
    const response = await TotaraFetchSuggestedCoursesService({
      page: parseInt(page),
      perPage: parseInt(perPage),
    });

    // TODO - Implement pagination from API response
    // Creating pagination object
    const pagination: IPagination = {
      total: response?.data?.courses?.length || 0,
      page: 1,
      perPage: response?.data?.courses?.length || 0,
      totalPages: 1,
      message: 'Pagination is not working with the API',
    };

    // Return the response
    return NextResponse.json({
      list: response?.data?.recommendedcourses || [],
      pagination: pagination,
    });
  } catch (error) {
    // RRturn Error response
    return ErrorResponse(error);
  }
}
