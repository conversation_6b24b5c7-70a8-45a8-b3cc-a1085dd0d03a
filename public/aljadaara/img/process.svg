<svg width="1554" height="969" viewBox="0 0 1554 969" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M772.5 0V199.5C772.5 239.5 795.1 282.5 865.5 282.5C935.9 282.5 1304.67 282.5 1476 282.5C1510 282.5 1552.5 306.3 1552.5 375.5C1552.5 444.7 1552.5 559 1552.5 607.5C1552.5 644 1544.3 710 1465.5 714C1386.7 714 523 714 101 714C62 714 15 735.3 15 802.5C15 869.7 15 897.833 15 954.5" stroke="#C9C0BB" stroke-width="2"/>
<path d="M772.5 0V199.5C772.5 239.5 795.1 282.5 865.5 282.5C935.9 282.5 1127.17 282.5 1298.5 282.5" stroke="url(#paint0_linear_509_549)" stroke-width="2"/>
<g filter="url(#filter0_b_509_549)">
<circle cx="885" cy="282" r="14" fill="url(#paint1_radial_509_549)" fill-opacity="0.5"/>
<circle cx="885" cy="282" r="14.5" stroke="url(#paint2_radial_509_549)" stroke-opacity="0.8"/>
</g>
<g filter="url(#filter1_b_509_549)">
<circle cx="885" cy="282" r="5" fill="#7CA1C3"/>
<circle cx="885" cy="282" r="5.5" stroke="url(#paint3_radial_509_549)" stroke-opacity="0.8"/>
</g>
<g filter="url(#filter2_b_509_549)">
<circle cx="492" cy="714" r="14" fill="url(#paint4_radial_509_549)" fill-opacity="0.5"/>
<circle cx="492" cy="714" r="14.5" stroke="url(#paint5_radial_509_549)" stroke-opacity="0.8"/>
</g>
<g filter="url(#filter3_b_509_549)">
<circle cx="492" cy="714" r="5" fill="#4976C5"/>
<circle cx="492" cy="714" r="5.5" stroke="url(#paint6_radial_509_549)" stroke-opacity="0.8"/>
</g>
<g filter="url(#filter4_b_509_549)">
<circle cx="15" cy="954" r="14" fill="url(#paint7_radial_509_549)" fill-opacity="0.5"/>
<circle cx="15" cy="954" r="14.5" stroke="url(#paint8_radial_509_549)" stroke-opacity="0.8"/>
</g>
<g filter="url(#filter5_b_509_549)">
<circle cx="15" cy="954" r="5" fill="#4976C5"/>
<circle cx="15" cy="954" r="5.5" stroke="url(#paint9_radial_509_549)" stroke-opacity="0.8"/>
</g>
<defs>
<filter id="filter0_b_509_549" x="830" y="227" width="110" height="110" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_509_549"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_509_549" result="shape"/>
</filter>
<filter id="filter1_b_509_549" x="839" y="236" width="92" height="92" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_509_549"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_509_549" result="shape"/>
</filter>
<filter id="filter2_b_509_549" x="437" y="659" width="110" height="110" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_509_549"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_509_549" result="shape"/>
</filter>
<filter id="filter3_b_509_549" x="446" y="668" width="92" height="92" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_509_549"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_509_549" result="shape"/>
</filter>
<filter id="filter4_b_509_549" x="-40" y="899" width="110" height="110" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_509_549"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_509_549" result="shape"/>
</filter>
<filter id="filter5_b_509_549" x="-31" y="908" width="92" height="92" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="20"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_509_549"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_509_549" result="shape"/>
</filter>
<linearGradient id="paint0_linear_509_549" x1="802.5" y1="10" x2="802.5" y2="282" gradientUnits="userSpaceOnUse">
<stop stop-color="#F5EDE8"/>
<stop offset="1"/>
</linearGradient>
<radialGradient id="paint1_radial_509_549" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(871 268) rotate(45) scale(39.598 27.5142)">
<stop stop-color="#8A8A8A" stop-opacity="0.56"/>
<stop offset="1" stop-color="#DDDDDD" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint2_radial_509_549" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(871 268) rotate(45) scale(39.598 27.5142)">
<stop stop-color="white" stop-opacity="0.56"/>
<stop offset="1" stop-color="#E9E9E9" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint3_radial_509_549" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(880 277) rotate(45) scale(14.1421 9.82651)">
<stop stop-color="white" stop-opacity="0.56"/>
<stop offset="1" stop-color="#E9E9E9" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint4_radial_509_549" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(478 700) rotate(45) scale(39.598 27.5142)">
<stop stop-color="#8A8A8A" stop-opacity="0.56"/>
<stop offset="1" stop-color="#DDDDDD" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint5_radial_509_549" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(478 700) rotate(45) scale(39.598 27.5142)">
<stop stop-color="white" stop-opacity="0.56"/>
<stop offset="1" stop-color="#E9E9E9" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint6_radial_509_549" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(487 709) rotate(45) scale(14.1421 9.82651)">
<stop stop-color="white" stop-opacity="0.56"/>
<stop offset="1" stop-color="#E9E9E9" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint7_radial_509_549" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1 940) rotate(45) scale(39.598 27.5142)">
<stop stop-color="#8A8A8A" stop-opacity="0.56"/>
<stop offset="1" stop-color="#DDDDDD" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint8_radial_509_549" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1 940) rotate(45) scale(39.598 27.5142)">
<stop stop-color="white" stop-opacity="0.56"/>
<stop offset="1" stop-color="#E9E9E9" stop-opacity="0.67"/>
</radialGradient>
<radialGradient id="paint9_radial_509_549" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10 949) rotate(45) scale(14.1421 9.82651)">
<stop stop-color="white" stop-opacity="0.56"/>
<stop offset="1" stop-color="#E9E9E9" stop-opacity="0.67"/>
</radialGradient>
</defs>
</svg>
