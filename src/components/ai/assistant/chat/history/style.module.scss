@use 'mixins' as *;

.chatTitle {
  font-size: size(26);
  font-weight: 400;
  margin-bottom: size(24);
  text-align: left;
  width: 100%;

  @include rtl {
    text-align: right;
  }
}

.closeCta {
  background-color: transparent;
  border: 0;
  position: absolute;
  right: size(40);
  top: size(40);

  svg {
    fill: var(--grey-900);
    height: size(20);
    width: size(20);
  }
}

.chatHistory {
  align-items: center;
  display: flex;
  flex-direction: column;
  padding: size(32);

  @include for-all-phone() {
    background-color: var(--white);
    position: fixed;
    z-index: 5;
  }
}

.searchWrapper {
  border-bottom: 1px solid var(--grey-400);
  margin-bottom: size(24);
  padding-bottom: size(40);
  position: relative;
  width: 100%;

  svg {
    fill: var(--grey-900);
    height: size(17);
    left: size(24);
    position: absolute;
    top: size(24);
    width: size(17);

    @include rtl {
      left: auto;
      right: size(24);
    }
  }
}

.searchHistory {
  @include forMarginPadding(padding, size(20), size(20), size(20), size(50));
  background-color: var(--gun-smoke-400);
  border: 0;
  border-radius: size(8);
  width: 100%;

  &:focus {
    outline: 0;
  }

  &::placeholder {
    color: var(--grey-600);
    font-size: size(18);
  }
}

@include for-dark-theme {
  .searchHistory {
    background-color: var(--grey-400);
  }
}

.newCourseCta {
  @include forMarginPadding(padding, size(24), size(24), size(24), size(60));
  background-color: transparent;
  border: 1px solid var(--disabled-border);
  border-radius: size(10);
  cursor: pointer;
  font-size: size(20);
  text-align: left;
  margin-bottom: size(32);
  position: relative;
  transition: 0.3s all ease-in-out;
  width: 100%;

  @include rtl {
    text-align: right;
  }

  &:hover {
    background-color: var(--cool-grey-500);
    border: 1px solid var(--grey-600);
    transition: 0.3s all ease-in-out;
  }

  svg {
    fill: var(--grey-900);
    height: size(12);
    left: size(24);
    position: absolute;
    top: size(29);
    width: size(12);

    @include rtl {
      left: auto;
      right: size(24);
    }
  }

  &:disabled {
    cursor: not-allowed;
    transition: 0.3s all ease-in-out;

    svg {
      fill: var(--grey-500);
      transition: 0.3s all ease-in-out;
    }
  }
}

@include for-dark-theme {
  .newCourseCta {
    &:hover {
      background-color: #2e2e2e;
    }
  }
}

.sessionWrapper {
  border-bottom: 1px solid var(--grey-400);
  display: flex;
  flex-direction: column;
  height: calc(100vh - size(400));
  overflow: auto;
  width: 100%;

  @media (min-width: 576px) {
    &::-webkit-scrollbar-track {
      background-color: transparent;
    }

    &::-webkit-scrollbar {
      width: size(8);
      background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: size(8);
      background-color: var(--grey-900);
    }
  }
}

.sessionGroupWrapper {
  display: flex;
  flex-direction: column;
  gap: size(12);
  margin-bottom: size(32);
}

.groupTitle {
  color: var(--grey-700);
  font-size: size(16);
  font-weight: 400;
  margin-bottom: size(16);
}

.sessionItem {
  align-items: center;
  border: 1px solid var(--grey-400);
  border-radius: size(10);
  display: flex;
  position: relative;
  transition: 0.3s all ease-in-out;

  &:hover {
    border: 1px solid var(--grey-500);
    background-color: var(--cool-grey-500);
    transition: 0.3s all ease-in-out;
  }
}

@include for-dark-theme {
  .sessionItem {
    &:hover {
      background-color: #2e2e2e;
    }
  }
}

button.activeSessionCta {
  svg {
    fill: var(--text-link-blue);
  }
}

.sessionCta {
  @include forMarginPadding(padding, size(18), size(16), size(18), size(48));
  align-items: center;
  background-color: transparent;
  border: 0;
  cursor: pointer;
  display: flex;
  font-size: size(16);
  text-align: left;
  flex: 1;
  transition: 0.2s all ease-in-out;
  text-decoration: none;

  @include rtl {
    text-align: right;
  }

  &:hover {
    text-decoration: underline;
  }

  svg {
    fill: var(--grey-900);
    left: size(16);
    position: absolute;
    height: size(20);
    transition: 0.2s all ease-in-out;
    width: size(20);

    @include rtl {
      right: size(16);
      left: auto;
    }
  }

  &:disabled {
    cursor: not-allowed;
    transition: 0.2s all ease-in-out;

    svg {
      fill: var(--grey-500);
      transition: 0.2s all ease-in-out;
    }
  }
}

.deleteSession {
  background-color: transparent;
  border: 0;
  cursor: pointer;
  width: size(48);

  svg {
    fill: var(--grey-900);
    height: size(20);
    transition: 0.2s all ease-in-out;
    width: size(20);
  }

  &:hover {
    svg {
      fill: var(--danger-800);
      transition: 0.2s all ease-in-out;
    }
  }

  &:disabled {
    cursor: not-allowed;

    svg {
      fill: var(--grey-500);
    }
  }
}

.clearAllCta {
  @include forMarginPadding(padding, size(24), size(24), size(24), size(40));
  align-items: center;
  align-self: flex-start;
  background-color: transparent;
  border: 0;
  cursor: pointer;
  display: flex;
  font-size: size(20);
  position: relative;
  transition: 0.2s all ease-in-out;
  width: auto;

  svg {
    fill: var(--grey-900);
    height: size(22);
    left: 0;
    position: absolute;
    width: size(20);

    @include rtl {
      right: 0;
      left: auto;
    }
  }

  &:hover {
    text-decoration: underline;

    svg {
      fill: var(--danger-800);
      transition: 0.2s all ease-in-out;
    }
  }
}
