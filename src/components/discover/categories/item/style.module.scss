@use 'mixins' as *;

.linkItem {
  text-decoration: none;
  display: flex;
  align-items: center;
  @include transitions(0.5s);
  font-size: size(14);
  font-weight: 700;
  color: var(--grey-900);

  box-shadow: none;
  border: none;
  background-color: transparent;
  cursor: pointer;

  .text {
    display: inline;
    padding-bottom: 0;
    transition: all 0.5s linear;
    background: linear-gradient(
      to bottom,
      var(--grey-900) 0%,
      var(--grey-900) 98%
    );
    background-size: 0 1px;
    background-repeat: no-repeat;
    background-position: left 100%;

    @include rtl {
      background-position: right 100%;
    }
  }
}

.icon {
  width: size(24);
  height: size(24);
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
  @include forMarginPadding(margin, size(0), size(20), size(0), size(0));
  display: flex;
  justify-content: center;

  i {
    display: block;

    img {
      filter: invert(88%) sepia(21%) saturate(935%) hue-rotate(123deg)
        brightness(60%) contrast(97%);
      object-fit: fill;
    }
  }
}

@include hover {
  .linkItem {
    &:hover {
      .text {
        background-size: 100% 1px;
      }
    }
  }
}

@include for-all-phone() {
  .linkItem {
    flex-direction: column;
    justify-content: center;

    .text {
      white-space: initial;
      text-align: center;
    }
  }

  .icon {
    width: size(64);
    height: size(64);
    @include borderRadius(16px);
    @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
    @include forMarginPadding(margin, size(0), size(0), size(10), size(0));
    display: flex;
    justify-content: center;
    align-items: center;

    i {
      width: size(32);
      height: size(32);
    }
  }
}
