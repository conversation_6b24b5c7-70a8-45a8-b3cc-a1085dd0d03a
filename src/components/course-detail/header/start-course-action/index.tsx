import { useCallback, useEffect, useState } from 'react';
import useStartLearningStore from '@/stores/learning/courses/start-learning';
import useCourseDetailStore from '@/stores/learning/courses/detail';
import useCoreCourseStore from '@/stores/learning/courses/core-course';
import Button from '@/ui/form/button';
import ICoreSectionData from '@/types/domain/learning/course/coreSectionData';
import ICoreSection from '@/types/domain/learning/course/coreSection';
import dictionary from '@/dictionaries';
import useTotaraAuth from '@/stores/totara-auth';
import useAuthStore from '@/stores/auth';
import { useLocale } from 'next-intl';

const StartCourseAction = () => {
  const [actionUrl, setActionUrl] = useState<string>('');
  const onStartLearning = useStartLearningStore((x) => x.fetch);
  const onTotaraAuthFetch = useTotaraAuth((x) => x.fetch);
  const setIsLoading = useTotaraAuth((x) => x.setIsLoading);
  const user = useAuthStore((x) => x.user);
  const locale = useLocale();
  // Getting course detail
  const courseDetail = useCourseDetailStore((state) => state.detail);
  const coreCourseDetails = useCoreCourseStore(
    (state) => state.coreCourseDetails,
  );
  const sections = coreCourseDetails?.mobile_course?.course?.sections;

  /**
   * Method to handle course start
   */
  const onStartCourse = useCallback(() => {
    setIsLoading(true);
    onTotaraAuthFetch(actionUrl, user?.idn as string, locale);
    onStartLearning();
  }, [
    onTotaraAuthFetch,
    actionUrl,
    user?.idn,
    onStartLearning,
    setIsLoading,
    locale,
  ]);

  const findFirstActivity = useCallback((sections: ICoreSection[]) => {
    for (const j of sections) {
      for (const i of j?.data as ICoreSectionData[]) {
        if (
          i?.completionstatus != 'complete' &&
          i.viewurl &&
          i?.viewurl?.length
        ) {
          return i;
        }
      }
    }

    return undefined;
  }, []);

  useEffect(() => {
    if (sections?.length && sections?.length > 0 && courseDetail?.isEnrolled) {
      if (courseDetail?.is_external_course) {
        if (sections[0]?.data?.length && sections[0]?.data?.length > 0) {
          setActionUrl(sections[0]?.data[0].viewurl);
        }
      } else {
        const item = findFirstActivity(sections);

        if (item) {
          setActionUrl(item.viewurl);
        }
      }
    }
  }, [sections, courseDetail, findFirstActivity]);

  return (
    <Button
      color="black"
      type="button"
      onClick={onStartCourse}
      dictionary={
        courseDetail?.status == 'Not Yet Started'
          ? dictionary.startCourse
          : dictionary.resumeCourse
      }
    />
  );
};

export default StartCourseAction;
