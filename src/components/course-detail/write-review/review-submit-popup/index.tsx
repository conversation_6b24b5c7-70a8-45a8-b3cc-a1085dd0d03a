import { FC, ReactNode, useRef, useEffect } from 'react';
import styles from './style.module.scss';
import Typography from '@/ui/typography';
import Button from '@/ui/form/button';
import dictionary from '@/dictionaries';

interface IReviewSubmitPopup {
  isOpened?: boolean;
  title?: string;
  children: ReactNode;
  onCancel?: () => void;
}

const ReviewSubmitPopup: FC<IReviewSubmitPopup> = ({
  isOpened,
  title,
  children,
  onCancel,
}) => {
  const dialogRef = useRef<HTMLDialogElement>(null);

  useEffect(() => {
    if (isOpened) {
      dialogRef.current?.showModal();
    } else {
      dialogRef.current?.close();
    }
  }, [isOpened]);

  // Return JSX
  return (
    <dialog
      ref={dialogRef}
      className={
        styles.reviewSubmitPopup +
        ` ${isOpened ? styles.reviewSubmitPopupOpened : ''}`
      }
    >
      <div className={styles.reviewSubmitPopupTop}>
        {title ? (
          <Typography
            dictionary={title}
            className={styles.reviewSubmitPopupTitle}
          />
        ) : null}
      </div>
      <div className={styles.reviewSubmitPopupBody}>{children}</div>
      <div className={styles.reviewSubmitPopupActions}>
        <Button dictionary={dictionary.close} onClick={onCancel} />
      </div>
    </dialog>
  );
};

export default ReviewSubmitPopup;
