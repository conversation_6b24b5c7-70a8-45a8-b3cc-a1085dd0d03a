@use 'mixins' as *;

.calendarTabs {
  // @include forMarginPadding(padding, size(0), size(0), size(0), size(16));
  border-bottom: size(1.5) solid var(--disabled-border);

  .eventTypeCta {
    background-color: transparent;
    border: 0;
    color: var(--grey-600);
    cursor: pointer;
    font-size: size(16);
    height: size(48);
    position: relative;
    width: size(200);

    &.isTabActive {
      cursor: default;
      color: var(--grey-900);

      &:before {
        background-color: var(--grey-900);
        bottom: size(-2);
        content: '';
        display: block;
        position: absolute;
        height: size(2);
        width: 100%;
        z-index: 1;
      }
    }

    @include for-all-phone() {
      width: size(160);
    }
  }
}

@include for-all-phone {
  .calendarTabs {
    margin-top: 24px;
    .eventTypeCta {
      height: auto;
      padding-bottom: 12px;
    }
    .isTabActive span {
      color: var(--grey-900);
    }
  }
}
