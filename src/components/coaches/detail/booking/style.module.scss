@use 'mixins' as *;

.body {
  width: 100%;
  @include forMarginPadding(padding, size(24), size(24), size(24), size(24));
  overflow-y: auto;
  height: calc(100vh - size(192));

  @include for-all-phone() {
    height: calc(100vh - size(179));
  }

  @media (min-width: 576px) {
    &::-webkit-scrollbar-track {
      background-color: var(--white);
    }

    &::-webkit-scrollbar {
      width: size(8);
      background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: size(8);
      background-color: var(--grey-900);
    }
  }
}

.footer {
  width: 100%;
  z-index: 9;
  background: var(--white);
  @include forMarginPadding(padding, size(24), size(24), size(24), size(24));
  height: 9vh;
}

.head {
  @include forMarginPadding(padding, size(12), size(24), size(12), size(24));
  background: var(--white);
  width: 100%;
  height: 7vh;
  display: flex;
}

.backDetail {
  display: flex;
  align-items: center;
  font-size: size(40);
  line-height: 1;
  background: none;
  border: none;
  cursor: pointer;

  i {
    width: size(26);
    height: size(26);
    @include forMarginPadding(margin, size(0), size(10), size(0), size(0));

    svg {
      vertical-align: top;
    }
  }
}

@include for-dark-theme() {
  .innerBookCta:disabled {
    background-color: var(--grey-700) !important;
    color: var(--disabled) !important;
  }
}

.calendar {
  ul {
    @include forMarginPadding(margin, size(0), size(0), size(10), size(0));
    @include forMarginPadding(padding, size(0), size(0), size(110), size(0));
    position: relative;

    &:before {
      width: 2px;
      height: 100%;
      position: absolute;
      @include leftToRight(22%);
      top: 0;
      background: var(--grey-400);
      content: '';
    }

    li {
      list-style: none;
      display: flex;
      position: relative;
      @include forMarginPadding(padding, size(0), size(0), size(0), size(0));

      &:first-child {
        &:before {
          display: none;
        }
      }

      + li {
        @include forMarginPadding(
          padding,
          size(20),
          size(0),
          size(20),
          size(0)
        );
      }

      // &:last-child {
      //   @include forMarginPadding(padding, size(40), size(0), size(0), size(0));
      // }
    }
  }
}

.radionNotSelected {
  width: size(16);
  height: size(16);
  position: absolute;
  @include leftToRight(calc(22% - size(7)));
  top: size(30);
  @include borderRadius(50%);
  background: var(--white);
  border: solid 2px var(--grey-400);
  content: '';
}

.radioSelected {
  width: size(12);
  height: size(12);
  background-color: var(--success-800);
  border: 2px solid var(--white);
  position: absolute;
  @include leftToRight(calc(22% - size(5)));
  top: size(30);
  @include borderRadius(50%);
  box-shadow: 0 0 0 2px var(--success-800);
}

.left {
  display: flex;
  width: 20%;
  align-items: flex-start;
  justify-content: center;
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
}

.data {
  width: 80%;
  @include forMarginPadding(padding, size(0), size(0), size(0), size(50));
}

.center {
  display: inline-block;
  justify-content: center;
  align-items: center;
  flex-direction: column;

  > span {
    text-align: center;
  }
}

.day {
  font-size: size(16);
  line-height: 1;
  display: block;
  @include forMarginPadding(margin, size(0), size(0), size(5), size(0));
  color: var(--grey-700);
  text-transform: uppercase;
}

.date {
  font-size: size(32);
  font-weight: 700;
  line-height: 1;
  display: block;
  @include forMarginPadding(margin, size(0), size(0), size(5), size(0));
}

.month {
  font-size: size(16);
  line-height: 1;
  display: block;
  color: var(--grey-700);
}

.time {
  text-transform: lowercase;
}

.selectedDetailsContainer {
  @include forMarginPadding(padding, size(20), size(16), size(20), size(36));
  background: var(--warning-500);
  @include borderRadius(var(--radius));

  display: flex;
  position: fixed;
  bottom: size(150);
  width: size(530);
}

.selectedImage {
  overflow: hidden;
  display: flex;
  height: 100%;
  @include borderRadius(50%);
}

.selectedInfo {
  display: flex;
  flex-direction: column;
  @include forMarginPadding(padding, size(0), size(0), size(0), size(16));
  h4 {
    margin-bottom: 0;
  }
}

@include for-all-phone() {
  .left,
  .data {
    h5 {
      font-size: size(20);
    }
  }

  .backDetail {
    font-size: size(24);

    i {
      width: size(16);
      height: size(16);
    }
  }

  .footer {
    button {
      font-size: size(18);
      height: size(56);
      text-align: center;
    }
  }

  .selectedDetailsContainer {
    width: 90%;
    height: size(100);
  }
}

@include for-dark-theme() {
  .radionNotSelected {
    border: solid 2px var(--grey-800);
  }

  .radioSelected {
    background-color: var(--green-900);
    box-shadow: 0 0 0 2px var(--green-900);
  }

  .selectedDetailsContainer {
    background-color: var(--cardBg);
  }
}
