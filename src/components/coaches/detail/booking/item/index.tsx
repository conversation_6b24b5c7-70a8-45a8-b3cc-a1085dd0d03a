'use client';

import { FC, useCallback, useMemo } from 'react';
import Image from 'next/image';
import Typography from '@/ui/typography';
import teamsIcon from '@public/images/teams.png';
import Pin from '@/ui/icons/pin';
import cn from 'classnames';
import styles from './style.module.scss';
import ISlot from '@/types/domain/learning/coach/slot';
import useCoachBookingStore from '@/stores/learning/coaches/booking';
import { formatLocalTime } from '@/utils/time';
import { useLocale } from 'next-intl';
import dictionary from '@/dictionaries';

interface IItem {
  slot: ISlot;
  title?: string;
}

const Item: FC<IItem> = (props) => {
  // Deconstructing props
  const { slot, title } = props;

  // Getting selected slot
  const selected = useCoachBookingStore((x) => x.selected);

  // Getting action to select slot
  const setSelected = useCoachBookingStore((x) => x.setSelected);

  const locale = useLocale();

  // Getting active state
  const active = useMemo(() => {
    return selected?.slot_id === slot?.slot_id;
  }, [selected?.slot_id, slot?.slot_id]);

  // Creating classnames
  const classNames = cn({
    [styles.item]: true,
    [styles.active]: active,
  });

  // Memoizing the start
  const start = useMemo(() => {
    // If start is available
    if (slot?.time_from) {
      return formatLocalTime(slot?.time_from);
    }
    // Returning null
    return null;
  }, [slot?.time_from]);

  // Memoizing the end
  const end = useMemo(() => {
    // If start is available
    if (slot?.time_to) {
      return formatLocalTime(slot?.time_to);
    }
    // Returning null
    return null;
  }, [slot?.time_to]);

  /**
   * Method to handle click
   */
  const handleCLick = useCallback(() => {
    if (slot) {
      setSelected(slot);
    }
  }, [slot, setSelected]);

  // Return JSX
  return (
    <div className={classNames} onClick={handleCLick}>
      {(title || start || end) && (
        <div className={styles.head}>
          {title && <Typography as="span">{title}</Typography>}
          {(start || end) && (
            <Typography as="span" className={styles.time}>
              {start?.locale(locale === 'ar' ? 'ar' : 'en').format('h:mma')} -{' '}
              {end?.locale(locale === 'ar' ? 'ar' : 'en').format('h:mma')}
            </Typography>
          )}
        </div>
      )}
      {slot?.meeting_link && (
        <div className={styles.location}>
          <i>
            <Image src={teamsIcon} alt="" className="img-fluid" />
          </i>
          <Typography as="span" dictionary={dictionary.viaMicrosoftTeams} />
        </div>
      )}
      {slot?.location && (
        <div className={styles.location}>
          <i>
            <Pin />
          </i>
          <Typography as="span">{slot?.location}</Typography>
        </div>
      )}
    </div>
  );
};

export default Item;
