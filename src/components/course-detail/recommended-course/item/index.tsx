import { FC, useCallback, useMemo } from 'react';
import { StaticImageData } from 'next/image';
import Image from '@/ui/image';
import Link from '@/components/course/link';
import Typography from '@/ui/typography';
import KababMenu from '@/ui/menu/kabab';
import useReferCourseStore from '@/stores/learning/courses/refer-course';

import { Share, ShareProfile } from '@/ui/icons';
import courseImg from '@public/images/course.jpg';
import linkedin from '@public/images/linkedin.png';
import ICourse from '@/types/domain/learning/course';
import { useLocale } from 'next-intl';
import useShareCourseStore from '@/stores/learning/courses/share';

// Styles
import styles from '../style.module.scss';
import dictionary from '@/dictionaries';

interface IItem {
  image?: string | StaticImageData;
  title?: string;
  linkText?: string;
  linkImg?: string | StaticImageData;
  course: ICourse;
}

const Item: FC<IItem> = (props) => {
  const {
    image = courseImg,
    title,
    linkText,
    linkImg = linkedin,
    course,
  } = props;
  const baseUrl = `${window.location.protocol}//${window.location.host}`;
  const locale = useLocale();

  const setVisible = useReferCourseStore((state) => state.setVisible);
  const setCourseInfo = useReferCourseStore((state) => state.setCourseInfo);
  const showShareToast = useShareCourseStore((state) => state.showShareToast);

  const type = useMemo(() => {
    return course?.component_type?.toString() || course?.type?.toString();
  }, [course?.component_type, course?.type]);

  const onReferCourse = useCallback(
    (id?: number, name?: string, image?: string) => {
      setCourseInfo(id, name, image);
      setVisible(true);
    },
    [setVisible, setCourseInfo],
  );

  const shareCourse = (id: number | undefined) => {
    const courseLink =
      baseUrl +
      '/' +
      locale +
      `${type === 'Program' ? '/program/' : '/course/'}${id}`;
    const textArea = document.createElement('textarea');
    textArea.value = courseLink;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    showShareToast();
  };

  return (
    <div className={styles.card}>
      {image && (
        <div className={styles.photo}>
          <Image
            src={image}
            alt=""
            className="img-fluid"
            width={100}
            height={100}
          />
        </div>
      )}

      <div className={styles.data}>
        {title && (
          <div className="mb-1">
            <Link course={course} className={styles.titleLink}>
              <Typography as="span">{title}</Typography>
            </Link>
          </div>
        )}
        {(linkText || linkImg) && (
          <Link course={course} className={styles.siteLink}>
            {linkImg && (
              <i className={styles.linkLogo}>
                <Image src={linkImg} alt="" className="img-fluid" />
              </i>
            )}
            {linkText && <Typography as="span">{linkText}</Typography>}
          </Link>
        )}

        <div className={styles.menuWrap}>
          <KababMenu>
            <nav>
              <ul className={styles.menuList}>
                {/* <li>
                  <button type="button">
                    <i className={styles.plus}>
                      <Plus2 />
                    </i>
                    <Typography as="span">
                      Add to your personal learning
                    </Typography>
                  </button>
                </li> */}
                {type !== 'Program' && (
                  <li>
                    <button
                      type="button"
                      onClick={() =>
                        onReferCourse(course?.id, course?.title, course?.image)
                      }
                    >
                      <i>
                        <ShareProfile />
                      </i>
                      <Typography
                        as="span"
                        dictionary={dictionary.referACourse}
                      />
                    </button>
                  </li>
                )}
                <li>
                  <button type="button" onClick={() => shareCourse(course?.id)}>
                    <i>
                      <Share />
                    </i>
                    <Typography as="span" dictionary={dictionary.Share} />
                  </button>
                </li>
              </ul>
            </nav>
          </KababMenu>
        </div>
      </div>
    </div>
  );
};

export default Item;
