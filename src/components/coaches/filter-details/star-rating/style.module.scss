@use 'mixins' as *;

.filter {
  + .filter {
    @include forMarginPadding(margin, size(20), size(0), size(0), size(0));
  }

  h4 {
    @include forMarginPadding(margin, size(0), size(0), size(20), size(0));
    @include forMarginPadding(padding, size(0), size(0), size(10), size(0));
    font-size: size(22);
    font-weight: 400;
    line-height: 1.2;
    color: var(--grey-800);
  }

  ul {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
    display: flex;
    flex-wrap: wrap;

    li {
      list-style: none;
      @include forMarginPadding(margin, size(0), size(10), size(10), size(0));
    }
  }
}

.ratingFilter {
  display: flex;
  align-items: center;
  @include rightToLeft(size(16));
  font-size: size(20);

  i {
    width: 14px;
    height: 14px;
    position: relative;
    @include forMarginPadding(margin, size(0), size(5), size(0), size(0));
  }

  span {
    font-size: 12px;
    line-height: 12px;
  }
}
