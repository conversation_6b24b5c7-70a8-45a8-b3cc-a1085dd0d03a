import React, { useEffect, useMemo } from 'react';
import CONFIG from '@/config';
import CardsSection from '@/components/common/cards-section';
import useTrendingCoursesStore from '@/stores/learning/courses/trending';
import Course from '@/components/course';
import EmptyMessage from '@/ui/empty-message';
import { useTranslations } from 'next-intl';
import dictionaries from '@/dictionaries';
import dictionary from '@/dictionaries';

const TrendingCourses = () => {
  // Getting loading state
  const isFetching = useTrendingCoursesStore((x) => x.isFetching);

  // Getting fetch action
  const onFetch = useTrendingCoursesStore((x) => x.fetch);

  // Getting trending courses
  const selected = useTrendingCoursesStore((x) => x.selected);
  const t = useTranslations();
  // Creating dyanmic array of cards
  const items = useMemo(() => {
    if (selected) {
      return selected.courses.map((item) => (
        <Course key={item.courseid} course={item} />
      ));
    }
  }, [selected]);

  useEffect(() => {
    // fetching trending courses
    onFetch();
  }, [onFetch]);

  // Return JSX
  return (
    <CardsSection
      title={t(dictionaries.TrendingCourses)}
      listingPageUrl={CONFIG.routes.discover.trending}
      items={items}
      limit={4}
      loading={isFetching}
      className="learningSlider2"
      slidesConfig={{
        desktop: {
          view: 4,
          gap: 16,
        },
        mobile: {
          view: 1.3,
          gap: 10,
        },
      }}
      noDataMessage={
        <EmptyMessage
          icon="search"
          title={t(dictionary.noSearchResultsFound)}
          description={t(dictionary.pleaseCheckThatAllWordsAreSpelledCorrectly)}
        />
      }
    />
  );
};

export default TrendingCourses;
