import ISlot from '@/types/domain/learning/coach/slot';
import { Dispatch, FC, SetStateAction, useMemo } from 'react';

import styles from './style.module.scss';
import { useLocale } from 'next-intl';
import moment from 'moment';
import Typography from '@/ui/typography';

interface IDayWithSlots {
  date: string;
  slots: ISlot[];
}

interface ISessionDate {
  dayWithSlots: IDayWithSlots;
  sessionDayIndex: number;
  selectedIndex: number;
  selectDate: Dispatch<SetStateAction<number>>;
}

const SessionDate: FC<ISessionDate> = ({
  dayWithSlots,
  sessionDayIndex,
  selectDate,
  selectedIndex,
}) => {
  const locale = useLocale();

  const handleSelectDate = () => {
    selectDate(sessionDayIndex);
  };

  const date = useMemo(() => {
    return moment(dayWithSlots.date, 'ddd MMM DD YYYY', 'en').locale(
      locale === 'ar' ? 'ar' : 'en',
    );
  }, [dayWithSlots.date, locale]);

  const isSelected = selectedIndex === sessionDayIndex;

  return (
    <div
      className={`${styles.dayWithSlots} ${isSelected ? styles.isSelected : ''}`}
      onClick={handleSelectDate}
    >
      <Typography as="span" className={styles.day}>
        {date.format('ddd').toLocaleUpperCase()}
      </Typography>
      <Typography as="span" className={styles.date}>
        {date.format('DD MMM')}
      </Typography>
      <Typography as="span" className={styles.slotCount}>
        {dayWithSlots.slots.length} slots
      </Typography>
    </div>
  );
};

export default SessionDate;
