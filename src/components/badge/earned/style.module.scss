@use 'mixins' as *;

.popup {
  position: fixed;
  top: 0;
  @include leftToRight(0);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  z-index: 99;
}

.inner {
  width: size(700);
  min-height: size(300);
  max-height: size(600);
  @include borderRadius(var(--radius));
  background: var(--white);
  background:
    url('/images/earned-badge.svg') no-repeat center center,
    white;
  background-size: cover;
  overflow: hidden;
  position: relative;
  z-index: 9;
}

.content {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  @include forMarginPadding(padding, size(40), size(25), size(40), size(25));
}

.scrollView {
  width: 100%;
  max-height: size(300);
  overflow-y: auto;
  overflow-x: hidden;
}

.closeBtn {
  position: absolute;
  @include rightToLeft(size(10));
  top: size(10);
  width: size(30);
  height: size(30);
  color: var(--grey-900);
  background: none;
  border: none;
  @include forMarginPadding(padding, size(0), size(0), size(0), size(0));
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  cursor: pointer;
  @include transitions(0.5s);

  i {
    width: size(20);
    height: size(28);

    svg {
      fill: var(--grey-900);
      @include transitions(0.5s);
    }
  }
}

.badge {
  width: size(150);
  height: size(150);
  @include forMarginPadding(margin, size(48), size(0), size(40), size(0));
  @include borderRadius(50%);
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: size(20);

  img {
    width: 100%;
  }
}

.preheader h4 {
  margin-bottom: size(0);
}

.header h3 {
  @include forMarginPadding(margin, size(24), size(0), size(16), size(0));
}

.subheader p {
  margin-bottom: size(0);
}

.badgeTitle {
  @include forMarginPadding(margin, size(0), size(0), size(48), size(0));
  @include forMarginPadding(padding, size(12), size(12), size(12), size(12));
  background-color: #ffffff80;
  border-radius: size(10);
  border: 1px solid var(--grey-400);
  text-transform: uppercase;
  h5 {
    @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    font-weight: normal;
  }
}

.detail {
  @include borderRadius(var(--radius));
  @include forMarginPadding(padding, size(25), size(30), size(25), size(30));
  margin-bottom: size(20);
  border: solid 1px var(--grey-400);
  width: 100%;

  &:last-of-type {
    margin-bottom: size(0);
  }
}

.title {
  display: flex;
  align-items: center;
  @include forMarginPadding(margin, size(0), size(0), size(10), size(0));

  p {
    @include forMarginPadding(margin, size(0), size(5), size(0), size(0));

    + p {
      font-weight: 700;
      @include forMarginPadding(margin, size(0), size(0), size(0), size(0));
    }
  }
}

.btn {
  width: size(300);
}

@include for-all-phone() {
  .popup {
    align-items: flex-end;
  }

  .inner {
    min-width: initial;
    min-height: size(300);
    -webkit-border-top-left-radius: 16px;
    -webkit-border-top-right-radius: 16px;
    -moz-border-radius-topleft: 16px;
    -moz-border-radius-topright: 16px;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;

    -webkit-border-bottom-right-radius: 0;
    -webkit-border-bottom-left-radius: 0;
    -moz-border-radius-bottomright: 0;
    -moz-border-radius-bottomleft: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    width: 100%;
  }

  .badge {
    width: size(118);
    height: size(118);
  }

  .badgeTitle {
    h4 {
      font-size: size(16);
      font-weight: 700;
    }
  }

  .detail {
    @include forMarginPadding(padding, size(20), size(20), size(20), size(20));

    h5 {
      font-size: size(16);
    }
  }

  .title {
    p {
      font-size: size(14);
    }
  }
}

@include for-dark-theme() {
  .content {
    color: var(--grey-300);
  }

  .badgeTitle {
    background-color: var(--grey-500);
    color: var(--oat-milk-900);
  }

  .btn {
    background-color: var(--grey-300);
    color: #ffffff;
  }

  @include hover() {
    .btn {
      &:hover {
        background: none;
        color: var(--tag);
      }
    }
  }
}
