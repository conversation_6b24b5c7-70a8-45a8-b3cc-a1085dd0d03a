name: build-push

on:
  push:
    branches: [develop, main, staging, 'chore/**', 'feat/**','task/**', 'azure-**']
    paths-ignore:
      - 'README.md'

  workflow_dispatch:



env:
  REGISTRY: acrgovacademywsprduaen.azurecr.io
  # github.repository as <account>/<repo>
  IMAGE_NAME: ${{ github.repository }}
  PLATFORMS: linux/amd64,linux/arm64 

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
      # This is used to complete the identity challenge
      # with sigstore/fulcio when running outside of PRs.
      id-token: write

    steps:
      # Get Branch name to variable BRANCH.
      - name: Get Branch name
        run: echo "BRANCH=$(echo $GITHUB_REF | cut -d'/' -f 3)" >> $GITHUB_ENV

      # Set pipeline env variable based on $BRANCH name
      - name:  Set ENV variable
        run: |
          case $BRANCH in
            main | azure-prd-*)
              ENV=prd       
              ;;
            develop | feat* |azure-dev-*)
              ENV=dev           
              ;;
            feat* )
              ENV=dev           
              ;;              
            *)
              ENV=null
              ;;
          esac
          echo "ENV=$ENV" >> "$GITHUB_ENV"
          echo "COMMIT_ID=`echo ${GITHUB_SHA}`" >> $GITHUB_ENV
          echo "AZURE_CLIENT_ID=AZURE_CLIENT_ID_${ENV^^}" >> "$GITHUB_ENV"
          echo "AZURE_TENANT_ID=AZURE_TENANT_ID_${ENV^^}" >> "$GITHUB_ENV"
          echo "AZURE_SUBSCRIPTION_ID=AZURE_SUBSCRIPTION_ID_${ENV^^}" >> "$GITHUB_ENV"
          echo "AZ_AKS_RG_NAME=rg-gov-academy-ws-${ENV}-aks-uaen" >> "$GITHUB_ENV"
          echo "AZ_AKS_NAME=aks-gov-academy-ws-${ENV}-uaen" >> "$GITHUB_ENV"

      - name: Checkout repository
        uses: actions/checkout@v4

      # - uses: actions/setup-java@v4
      #   with:
      #     distribution: temurin
      #     java-version: 11
          
      # Install the cosign tool except on PR
      # https://github.com/sigstore/cosign-installer
      - name: Install cosign
        if: github.event_name != 'pull_request'
        uses: sigstore/cosign-installer@v3.5.0
        with:
          cosign-release: 'v2.2.4'
        # Add support for more platforms with QEMU (optional)
        # https://github.com/docker/setup-qemu-action
      -  name: Set up QEMU
         uses: docker/setup-qemu-action@v3          

      # Azure login
      - name: Login to Azure
        if: github.event_name != 'pull_request'
        uses: azure/login@v1
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID_PRD }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID_PRD }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID_PRD }}
      
      # ACR Token
      - name: Get Azure Container Registry Token
        if: github.event_name != 'pull_request'
        uses: azure/cli@v2
        with:
          azcliversion: latest
          inlineScript: |
            az account show
            echo "ACR_ACCESS_TOKEN=$(az acr login --name ${{ env.REGISTRY }} --expose-token --output tsv --query accessToken)" >> "$GITHUB_ENV"

      # Set up BuildKit Docker container builder to be able to build
      # multi-platform images and export cache
      # https://github.com/docker/setup-buildx-action
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # Login against a Docker registry except on PR
      # https://github.com/docker/login-action
      - name: Log into registry ${{ env.REGISTRY }}
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ********-0000-0000-0000-************
          password: ${{ env.ACR_ACCESS_TOKEN }}

      # Extract metadata (tags, labels) for Docker
      # https://github.com/docker/metadata-action
      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          # generate Docker tags based on the following events/attributes
          tags: |
            type=sha,prefix=build-{{branch}}-commit-,format=long
            type=raw,value=build-{{branch}}-{{date 'YYYYMMDDHHmmss'}}
            type=raw,value=build-{{branch}}-latest

      # Build and push Docker image with Buildx (don't push on PR)
      # https://github.com/docker/build-push-action
      - name: Build and push Docker image
        id: build-and-push
        uses: docker/build-push-action@v5
        with:
          context: .
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: ${{ env.PLATFORMS }}

      # Sign the resulting Docker image digest except on PRs.
      # This will only write to the public Rekor transparency log when the Docker
      # repository is public to avoid leaking data.  If you would like to publish
      # transparency data even for private images, pass --force to cosign below.
      # https://github.com/sigstore/cosign
      - name: Sign the published Docker image
        if: ${{ github.event_name != 'pull_request' }}
        env:
          # https://docs.github.com/en/actions/security-guides/security-hardening-for-github-actions#using-an-intermediate-environment-variable
          TAGS: ${{ steps.meta.outputs.tags }}
          DIGEST: ${{ steps.build-and-push.outputs.digest }}
        # This step uses the identity token to provision an ephemeral certificate
        # against the sigstore community Fulcio instance.
        run: echo "${TAGS}" | xargs -I {} cosign sign --yes {}@${DIGEST}


      # Azure login for AKS deployment.
      - name: Login to ${{ env.ENV }} Azure environment
        if: env.ENV == 'dev'
        uses: azure/login@v1
        with:
          client-id: ${{ secrets[env.AZURE_CLIENT_ID] }}
          tenant-id: ${{ secrets[env.AZURE_TENANT_ID] }}
          subscription-id: ${{ secrets[env.AZURE_SUBSCRIPTION_ID] }}

      # Using kubectl set command to update image in the deployment.
      - name: Update Deployment
        if: env.ENV == 'dev'
        uses: azure/cli@v2
        with:
          azcliversion: latest
          inlineScript: |
            az account show
            echo "Getting current deployed image"
            az aks command invoke -n ${{ env.AZ_AKS_NAME }} -g ${{ env.AZ_AKS_RG_NAME }} -c "kubectl get deployment dge-web-app -o=jsonpath='{.spec.template.spec.containers[0].image}'" --only-show-errors
            echo "Updating deployment with latest image"
            az aks command invoke -n ${{ env.AZ_AKS_NAME }} -g ${{ env.AZ_AKS_RG_NAME }} -c "kubectl set image deployment/dge-web-app liferay=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:build-develop-commit-${{ env.COMMIT_ID }}" --only-show-errors
            echo "Getting latest deployed image"
            az aks command invoke -n ${{ env.AZ_AKS_NAME }} -g ${{ env.AZ_AKS_RG_NAME }} -c "kubectl get deployment dge-web-app -o=jsonpath='{.spec.template.spec.containers[0].image}'" --only-show-errors
            sleep 150
            echo "Getting all pods"
            az aks command invoke -n ${{ env.AZ_AKS_NAME }} -g ${{ env.AZ_AKS_RG_NAME }} -c "kubectl get pods" --only-show-errors